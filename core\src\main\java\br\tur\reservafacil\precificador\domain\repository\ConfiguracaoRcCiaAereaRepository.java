package br.tur.reservafacil.precificador.domain.repository;

import br.tur.reservafacil.dominio.aereo.CiaAerea;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcCiaAerea;

import java.util.List;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
public interface ConfiguracaoRcCiaAereaRepository
		extends Repository<ConfiguracaoRcCiaAerea, Integer> {

    void deleteByConfigPrecificacaoId(Integer idConfigPrecificacao);

    List<CiaAerea> findCiasAereasByConfiguracaoId(Integer configuracaoId);
}
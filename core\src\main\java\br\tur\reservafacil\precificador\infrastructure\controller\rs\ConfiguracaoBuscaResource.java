package br.tur.reservafacil.precificador.infrastructure.controller.rs;

import br.tur.reservafacil.precificador.application.service.ConfiguracaoBuscaService;
import br.tur.reservafacil.precificador.domain.component.gwaereo.response.ConfiguracaoBuscaResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/rs/v1/configuracoesBusca")
public class ConfiguracaoBuscaResource {

    private final ConfiguracaoBuscaService service;

    @Autowired
    public ConfiguracaoBuscaResource(ConfiguracaoBuscaService service) {
	this.service = service;
    }

    /**
     * Endpoint que verifica se uma ConfiguracaoBusca específica tem o bestPrice ativado.
     * @param id O ID da configuração de busca.
     * @return ResponseEntity contendo um boolean.
     */
    @RequestMapping(value="/{id}/bestPrice", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, method = RequestMethod.GET)
    @ResponseBody
    public ResponseEntity<ConfiguracaoBuscaResponse> isBestPriceEnabled(@PathVariable Integer id) {
	boolean isEnabled = this.service.isBestPriceEnabled(id);

	ConfiguracaoBuscaResponse response = new ConfiguracaoBuscaResponse(isEnabled);

	return ResponseEntity.ok(response);
    }
}
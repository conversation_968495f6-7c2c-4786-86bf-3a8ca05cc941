package br.tur.reservafacil.precificador.application.service;

import br.tur.reservafacil.dominio.Produto;
import br.tur.reservafacil.dominio.TipoConfigFee;
import br.tur.reservafacil.dominio.aereo.FiltroConfiguracaoFee;
import br.tur.reservafacil.dominio.aereo.Restricao;
import br.tur.reservafacil.dominio.aereo.configuracaofee.CiaAereaFee;
import br.tur.reservafacil.dominio.aereo.configuracaofee.ConfiguracaoFee;
import br.tur.reservafacil.dominio.aereo.voosExcluidos.MotivoExclusaoVoo;
import br.tur.reservafacil.dominio.intermediario.v0.to.precificacao.ConfiguracaoFeeComMotivoExclusao;
import br.tur.reservafacil.dominio.intermediario.v0.to.precificacao.ParametroSessaoPrecificacao;
import br.tur.reservafacil.dominio.intermediario.v0.to.precificacao.Precificavel;
import br.tur.reservafacil.dominio.tipo.NacInt;
import br.tur.reservafacil.dominio.tipo.TipoConfigPrecificacao;
import br.tur.reservafacil.dominio.tipo.TipoTarifaAcordo;
import br.tur.reservafacil.precificador.application.service.cache.ConfiguracaoFeeRedisCacheComponent;
import br.tur.reservafacil.precificador.domain.component.aereo.InternacionalizacaoComponent;
import br.tur.reservafacil.precificador.domain.component.restricao.RestricaoValidatorComponent;
import com.newrelic.api.agent.Trace;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.rits.cloning.Cloner;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import static br.tur.reservafacil.dominio.tipo.TipoConfigPrecificacao.RESTRITA;

@Service
public class ConfiguracaoFeeService {

    @Autowired
    private ConfiguracaoFeeRetriever retriever;

    @Autowired
    private InternacionalizacaoComponent internacionalizacaoComponent;

    @Trace
    public Optional<ConfiguracaoFee> obterConfiguracaoFeePorPrecificavel(List<Restricao> restricoes, Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao) {
        FiltroConfiguracaoFee filtro = criaFiltroPorPrecificavel(restricoes, precificavel, sessaoPrecificacao, RESTRITA);
        return retriever.getFirstMatchConfiguracaoFee(filtro);
    }

    @Trace
    public List<ConfiguracaoFeeComMotivoExclusao> obterConfiguracoesFeePorPrecificavel(List<Restricao> restricoes, Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao) {
        FiltroConfiguracaoFee filtro = criaFiltroPorPrecificavel(restricoes, precificavel, sessaoPrecificacao, RESTRITA);
        return retriever.getAllConfiguracaoFee(filtro);
    }

    public ConfiguracaoFee findById(Integer configFeeId) {
        return retriever.findById(configFeeId);
    }

    private FiltroConfiguracaoFee criaFiltroPorPrecificavel(List<Restricao> restricoes, Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao, TipoConfigPrecificacao tipoConfigPrecificacao) {
        final String codigoCiaViagem = precificavel.getCiaAereaViagem();
        final Integer empresaId = sessaoPrecificacao.getEmpresa().getId();
        final Boolean isBuscaDeTeste = sessaoPrecificacao.isBuscaDeTeste();
        final String produto = precificavel.getProdutoPrecificacao();
        final NacInt nacInt = precificavel.isNacional() == null ?
                precificavel.getAeroportos().stream().allMatch(a -> a.isNacional(internacionalizacaoComponent.obtemPaisOrigem())) ?
                        NacInt.NAC :
                        NacInt.INT :
                precificavel.isNacional() ? NacInt.NAC : NacInt.INT;
        final TipoTarifaAcordo tipoTarifaAcordo = precificavel.getTipoTarifaAcordo();

        return criarFiltro(codigoCiaViagem, restricoes, empresaId, isBuscaDeTeste, tipoConfigPrecificacao,
                produto, nacInt, tipoTarifaAcordo);
    }

    private FiltroConfiguracaoFee criarFiltro(String codigoCia, List<Restricao> restricoes, Integer empresaId, Boolean isBuscaDeTeste, TipoConfigPrecificacao tipoConfigPrecificacao, String produto, NacInt nacInt, TipoTarifaAcordo tipoTarifaAcordo) {
        FiltroConfiguracaoFee filtro = new FiltroConfiguracaoFee();
        filtro.setRestricoes(restricoes);
        filtro.setEmpresaId(empresaId);
        filtro.setBuscaDeTeste(isBuscaDeTeste);
        filtro.setProduto(produto);
        filtro.setCodCia(codigoCia);
        filtro.setNacInt(nacInt);
        filtro.setTipoTarifaAcordo(tipoTarifaAcordo);
        return filtro;
    }

    @Trace
    public List<ConfiguracaoFee> findByEmpresa(Integer empresaId, boolean isBuscaDeTeste) {
        return retriever.findByEmpresa(empresaId, isBuscaDeTeste);
    }

    public Optional<ConfiguracaoFee> filterConfiguracaoFeePorPrecificavel(List<ConfiguracaoFee> configsFee, List<Restricao> restricoes, Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao) {
        FiltroConfiguracaoFee filtro = criaFiltroPorPrecificavel(restricoes, precificavel, sessaoPrecificacao, RESTRITA);
        return retriever.filterConfiguracaoFeePorPrecificavel(configsFee, filtro);
    }

    @Component
    static class ConfiguracaoFeeRetriever {

        @Autowired
        private Cloner cloner;

        @Autowired
        private ConfiguracaoFeeRepositoryRetriever configuracaoFeeRepositoryRetriever;

        @Autowired
        private RestricaoValidatorComponent restricaoValidatorComponent;

        @Trace
        public Optional<ConfiguracaoFee> getFirstMatchConfiguracaoFee(FiltroConfiguracaoFee filtro) {
            final LocalTime time = LocalTime.now();
            int second = time.getSecond();
            String timeToCache = filtro.isBuscaDeTeste() ? ", time=" + DateTimeFormatter.ofPattern("HH:mm:").format(time) + second / 10 : "";

            Integer empresaId = filtro.getEmpresaId();
            String codCia = filtro.getCodCia();
            String produto = filtro.getProduto();
            TipoTarifaAcordo tipoTarifaAcordo = filtro.getTipoTarifaAcordo();
            NacInt nacInt = filtro.getNacInt();
            Integer grupoId = filtro.getGrupoId();

            List<ConfiguracaoFee> configsFee = configuracaoFeeRepositoryRetriever.findByEmpresaCiaProdutoTipoTarifaNacIntComRes(empresaId, codCia, produto,
                    tipoTarifaAcordo, nacInt, grupoId,
                    TipoConfigFee.PADRAO, filtro.isBuscaDeTeste(), timeToCache);

            for (ConfiguracaoFee configuracaoFee : configsFee) {
                boolean isValid = restricaoValidatorComponent.validaRestricoes(filtro.getRestricoes(), configuracaoFee.getRestricoes());
                if (isValid) {
                    return Optional.ofNullable(configuracaoFee);
                }
            }
            return Optional.empty();
        }

        @Trace
        public List<ConfiguracaoFeeComMotivoExclusao> getAllConfiguracaoFee(FiltroConfiguracaoFee filtro) {
            final LocalTime time = LocalTime.now();
            int second = time.getSecond();

            String timeToCache = filtro.isBuscaDeTeste() ? ", time=" + DateTimeFormatter.ofPattern("HH:mm:").format(time) + second / 10 : "";

            Integer empresaId = filtro.getEmpresaId();
            String codCia = filtro.getCodCia();
            String produto = filtro.getProduto();
            TipoTarifaAcordo tipoTarifaAcordo = filtro.getTipoTarifaAcordo();
            NacInt nacInt = filtro.getNacInt();
            Integer grupoId = filtro.getGrupoId();

            List<ConfiguracaoFeeComMotivoExclusao> configuracoesEMotivos = new ArrayList<>();
            List<ConfiguracaoFee> configsFee = configuracaoFeeRepositoryRetriever.findByEmpresaCiaProdutoTipoTarifaNacIntComRes(empresaId, codCia, produto,
                    tipoTarifaAcordo, nacInt, grupoId,
                    TipoConfigFee.PADRAO, filtro.isBuscaDeTeste(), timeToCache);

            for (ConfiguracaoFee configuracaoFee : configsFee) {
                MotivoExclusaoVoo motivoExclusaoVoo = restricaoValidatorComponent.validaRestricoes(filtro.getRestricoes(), configuracaoFee.getRestricoes(), true);
                if(motivoExclusaoVoo == null){
                    motivoExclusaoVoo = new MotivoExclusaoVoo();
                }
                motivoExclusaoVoo.setIdConfiguracaoPrecificacao(configuracaoFee.getId());
                motivoExclusaoVoo.setNomeConfiguracaoPrecificacao(configuracaoFee.getNome());

                configuracoesEMotivos.add(ConfiguracaoFeeComMotivoExclusao.of(configuracaoFee, motivoExclusaoVoo));
            }

            return configuracoesEMotivos;
        }

        public ConfiguracaoFee findById(Integer configFeeId) {
            return configuracaoFeeRepositoryRetriever.findById(configFeeId);
        }

        @Trace
        public List<ConfiguracaoFee> findByEmpresa(Integer empresaId, boolean isBuscaDeTeste) {
            return configuracaoFeeRepositoryRetriever.findByEmpresa(empresaId, isBuscaDeTeste);
        }

        public Optional<ConfiguracaoFee> filterConfiguracaoFeePorPrecificavel(List<ConfiguracaoFee> configsFee, FiltroConfiguracaoFee filtro) {
            String codCia = filtro.getCodCia();
            String produto = filtro.getProduto();
            TipoTarifaAcordo tipoTarifaAcordo = filtro.getTipoTarifaAcordo();
            NacInt nacInt = filtro.getNacInt();

            List<ConfiguracaoFee> configsFeeClone = cloner.deepClone(configsFee);

            List<ConfiguracaoFee> configsFiltradas = configsFeeClone.stream()
                                                                    .filter(f -> (f.getTipoTarifaAcordo() != null && f.getTipoTarifaAcordo().equals(tipoTarifaAcordo)) || TipoTarifaAcordo.AMBAS.equals(f.getTipoTarifaAcordo()))
                                                                    .filter(f -> (f.getNacInt() != null && f.getNacInt().equals(nacInt)) || NacInt.AMBOS.equals(f.getNacInt()))
                                                                    .filter(f -> CollectionUtils.isNotEmpty(f.getCiasAereas()) && f.getCiasAereas().stream().map(CiaAereaFee::getCodigo).collect(Collectors.toList()).contains(codCia))
                                                                    .filter(f -> f.getCiasAereas().stream().anyMatch(ciaAerea -> ciaAerea.getCodigo().equals(codCia) && (ciaAerea.getNacInt().equals(nacInt) || NacInt.AMBOS.equals(ciaAerea.getNacInt()))))
                                                                    .filter(f -> CollectionUtils.isNotEmpty(f.getProdutos()) && f.getProdutos().stream().map(Produto::getNome).collect(Collectors.toList()).contains(produto))
                                                                    .collect(Collectors.toList());

            for (ConfiguracaoFee configuracaoFee : configsFiltradas) {
                boolean isValid = restricaoValidatorComponent.validaRestricoes(filtro.getRestricoes(), configuracaoFee.getRestricoes());
                if (isValid) {
                    Optional<CiaAereaFee> ciaAereaFeeOpt = configuracaoFee.getCiasAereas().stream().filter(cia -> cia.getCodigo() != null && cia.getCodigo().equalsIgnoreCase(codCia)).findFirst();
                    configuracaoFee.setCiasAereas(Collections.singletonList(ciaAereaFeeOpt.orElse(null)));
                    return Optional.of(configuracaoFee);
                }
            }
            return Optional.empty();
        }
    }

    @Component
    static class ConfiguracaoFeeRepositoryRetriever {

        @Autowired
        private ConfiguracaoFeeRedisCacheComponent configuracaoFeeRedisCacheComponent;

        @Cacheable(cacheNames = "ConfiguracaoFeeService::findByEmpresaCiaProdutoTipoTarifaNacInt", sync = true, cacheManager = "ehCacheManager")
        @Trace
        public List<ConfiguracaoFee> findByEmpresaCiaProdutoTipoTarifaNacInt(Integer empresaId, String codCia, String produto, TipoTarifaAcordo tipoTarifaAcordo,
                                                                       NacInt nacInt, Integer grupoId, TipoConfigFee tipoConfigFee, boolean isBuscaTeste, String timeToCache) {
            return configuracaoFeeRedisCacheComponent.findByEmpresaCiaProdutoTipoTarifaNacInt(empresaId, codCia, produto, tipoTarifaAcordo, nacInt, grupoId, tipoConfigFee, isBuscaTeste, timeToCache);
        }

        @Cacheable(cacheNames = "ConfiguracaoFeeService::findByEmpresaCiaProdutoTipoTarifaNacIntComRes", sync = true, cacheManager = "ehCacheManager")
        @Trace
        public List<ConfiguracaoFee> findByEmpresaCiaProdutoTipoTarifaNacIntComRes(Integer empresaId, String codCia, String produto, TipoTarifaAcordo tipoTarifaAcordo,
                                                                             NacInt nacInt, Integer grupoId, TipoConfigFee tipoConfigFee, boolean isBuscaTeste, String timeToCache) {
            return configuracaoFeeRedisCacheComponent.findByEmpresaCiaProdutoTipoTarifaNacIntComRes(empresaId, codCia, produto, tipoTarifaAcordo, nacInt, grupoId, tipoConfigFee, isBuscaTeste, timeToCache);
        }
        @Cacheable(cacheNames = "ConfiguracaoFeeRepositoryRetriever::findById", cacheManager = "ehCacheManager")
        public ConfiguracaoFee findById(Integer configFeeId) {
            return configuracaoFeeRedisCacheComponent.findById(configFeeId);
        }

        @Cacheable(cacheNames = "ConfiguracaoFeeRepositoryRetriever::findByEmpresa", sync = true, cacheManager = "ehCacheManager")
        public List<ConfiguracaoFee> findByEmpresa(Integer empresaId, boolean isBuscaDeTeste) {
            return configuracaoFeeRedisCacheComponent.findByEmpresa(empresaId, isBuscaDeTeste);
        }
    }
}

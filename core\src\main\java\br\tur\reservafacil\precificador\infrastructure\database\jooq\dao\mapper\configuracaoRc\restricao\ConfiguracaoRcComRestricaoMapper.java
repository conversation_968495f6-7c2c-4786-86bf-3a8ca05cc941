package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.configuracaoRc.restricao;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.*;
import br.tur.reservafacil.dominio.Agencia;
import br.tur.reservafacil.dominio.Empresa;
import br.tur.reservafacil.dominio.Produto;
import br.tur.reservafacil.dominio.aereo.CiaAerea;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRc;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcProduto;
import br.tur.reservafacil.dominio.aereo.Restricao;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.*;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.configuracaoRc.ConfiguracaoRcRecord2ConfiguracaoRcMapper;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.configuracaoRc.produto.ConfiguracaoRcProdutoRecord2ConfiguracaoRcProdutoMapper;
import org.apache.commons.lang3.StringUtils;
import org.jooq.Record;
import org.jooq.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.Tables.*;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.ConfiguracaoRc.CONFIGURACAO_RC;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.Restricao.RESTRICAO;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
@Component
public class ConfiguracaoRcComRestricaoMapper
		implements CustomMultipleMapper<List<ConfiguracaoRc>, Result<Record>> {

	@Autowired
	private ConfiguracaoRcRecord2ConfiguracaoRcMapper confPrecMapper;

	@Autowired
	private EmpresaRecord2EmpresaMapper empresaRecord2EmpresaMapper;

	@Autowired
	private AgenciaRecord2AgenciaMapper agenciaRecord2AgenciaMapper;

	@Autowired
	private CiaAereaRecord2CiaAereaMapper ciaAereaRecord2CiaAereaMapper;

	@Autowired
	private ConfiguracaoRcProdutoRecord2ConfiguracaoRcProdutoMapper configuracaoRcProdutoRecord2ConfiguracaoRcProdutoMapper;

	@Autowired
	private ProdutoRecord2ProdutoMapper produtoRecord2ProdutoMapper;

	@Autowired
	private RestricaoRecord2RestricaoMapper restricaoMapper;

	@Override
	public List<ConfiguracaoRc> map(Result<Record> result) {
		List<ConfiguracaoRc> resultados = new ArrayList<>();

		Map<ConfiguracaoRcRecord, Result<Record>> confResult = result.intoGroups(CONFIGURACAO_RC);
		for (Map.Entry<ConfiguracaoRcRecord, Result<Record>> entry : confResult.entrySet()) {
			ConfiguracaoRc configuracaoPrecificacao = this.confPrecMapper.map(entry.getKey());
			configuracaoPrecificacao.setEmpresas(this.mapeiaEmpresas(entry));
			configuracaoPrecificacao.setAgencias(this.mapeiaAgencias(entry));
			configuracaoPrecificacao.setCiasAereas(this.mapeiaCiasAereas(entry));
			configuracaoPrecificacao.setConfiguracaoRcProdutos(this.mapeiaConfiguracaoRcProdutos(entry));
			configuracaoPrecificacao.setRestricoes(this.mapeiaRestricoes(entry));
			resultados.add(configuracaoPrecificacao);
		}
		return resultados;
	}

	private List<Empresa> mapeiaEmpresas(Map.Entry<ConfiguracaoRcRecord, Result<Record>> entry) {
		List<Empresa> empresas = new ArrayList<>();
		for (Record record : entry.getValue()) {
			final EmpresaRecord empresaRecord = record.into(EMPRESA);
			if (empresaRecord != null && StringUtils.isNotEmpty(empresaRecord.getReferencia())) {
				empresas.add(this.empresaRecord2EmpresaMapper.map(empresaRecord));
			}
		}
		return empresas;
	}

	private List<Agencia> mapeiaAgencias(Map.Entry<ConfiguracaoRcRecord, Result<Record>> entry) {
		List<Agencia> agencias = new ArrayList<>();
		for (Record record : entry.getValue()) {
			final AgenciaRecord agenciaRecord = record.into(AGENCIA);
			if (agenciaRecord != null && StringUtils.isNotEmpty(agenciaRecord.getReferencia())) {
				agencias.add(this.agenciaRecord2AgenciaMapper.map(agenciaRecord));
			}
		}
		return agencias;
	}

	private List<CiaAerea> mapeiaCiasAereas(Map.Entry<ConfiguracaoRcRecord, Result<Record>> entry) {
		List<CiaAerea> ciaAereas = new ArrayList<>();
		for (Record record : entry.getValue()) {
			final CiaAereaRecord ciaAereaRecord = record.into(CIA_AEREA);
			if (ciaAereaRecord != null && StringUtils.isNotEmpty(ciaAereaRecord.getCodigo())) {
				ciaAereas.add(this.ciaAereaRecord2CiaAereaMapper.map(ciaAereaRecord));
			}
		}
		return ciaAereas;
	}

	private List<ConfiguracaoRcProduto> mapeiaConfiguracaoRcProdutos(Map.Entry<ConfiguracaoRcRecord, Result<Record>> entry) {
		List<ConfiguracaoRcProduto> configuracaoRcProdutos = new ArrayList<>();
		for (Record record : entry.getValue()) {
			final ConfiguracaoRcProdutoRecord configuracaoRcProdutoRecord = record.into(CONFIGURACAO_RC_PRODUTO);
			if (configuracaoRcProdutoRecord != null && configuracaoRcProdutoRecord.getProdutoId() != null) {
				ConfiguracaoRcProduto configuracaoRcProduto = this.configuracaoRcProdutoRecord2ConfiguracaoRcProdutoMapper.map(configuracaoRcProdutoRecord);

				final ProdutoRecord produtoRecord = record.into(PRODUTO);
				if (produtoRecord != null && StringUtils.isNotEmpty(produtoRecord.getNome())) {
					Produto produto = this.produtoRecord2ProdutoMapper.map(produtoRecord);
					configuracaoRcProduto.setProduto(produto);
				}
				configuracaoRcProdutos.add(configuracaoRcProduto);
			}
		}
		return configuracaoRcProdutos;
	}

	private List<Restricao> mapeiaRestricoes(Map.Entry<ConfiguracaoRcRecord, Result<Record>> entry) {
		List<Restricao> restricoes = new ArrayList<>();
		for (Record record : entry.getValue()) {
			final RestricaoRecord restricaoRecord = record.into(RESTRICAO);
			if (restricaoRecord != null && StringUtils.isNotEmpty(restricaoRecord.getValor())) {
				restricoes.add(this.restricaoMapper.map(restricaoRecord));
			}
		}
		return restricoes;
	}
}

package br.tur.reservafacil.precificador.application.service.cache;

import br.tur.reservafacil.dominio.aereo.pagamento.CondicaoPagamento;
import br.tur.reservafacil.precificador.domain.repository.CondicaoPagamentoRepository;
import com.newrelic.api.agent.Trace;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

/**
 * Created by pamaehara on 23/10/2024.
 */
@Component
public class CondicaoPagamentoRedisCacheComponent {

    @Autowired
    private CondicaoPagamentoRepository condicaoPagamentoRepository;

    @Trace
    @Cacheable(cacheNames = "CondicaoPagamentoService::obterCondicoesPorPrecificavelEEmpresa", cacheManager = "redisCacheManager")
    public List<CondicaoPagamento> obterCondicoesPorPrecificavelEEmpresa(String ciaAerea, long empresaId, String produto, Boolean isBuscaDeTeste) {
        return condicaoPagamentoRepository.obterCondicoesPorCiaAereaEEmpresa(ciaAerea, empresaId, produto, isBuscaDeTeste);
    }

    @Trace
    @Cacheable(cacheNames = "CondicaoPagamentoService::findByEmpresa", cacheManager = "redisCacheManager")
    public List<CondicaoPagamento> findByEmpresa(Integer empresaId, boolean buscaDeTeste) {
        return condicaoPagamentoRepository.findByEmpresa(empresaId, buscaDeTeste);
    }
}

package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.configuracaoRc;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoRcRecord;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRc;
import org.springframework.stereotype.Component;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.CustomMapper;

/**
 * Created by <PERSON><PERSON> on 20/08/25 - GA-12251.
 */
@Component
public class ConfiguracaoRc2ConfiguracaoRcRecordMapper
		implements CustomMapper<ConfiguracaoRc, ConfiguracaoRcRecord> {

    @Override
    public ConfiguracaoRcRecord map(ConfiguracaoRc input, ConfiguracaoRcRecord output) {
	if (input.getId() != null) {
	    output.setId(input.getId());
	}
	output.setPrioridade(input.getPrioridade());
	output.setStatusPublicacao(input.getStatusPublicacao() != null ? input.getStatusPublicacao().name() : null);
	output.setNome(input.getNome());
	output.setVersao(input.getVersao());
	output.setIdOrigem(input.getIdOrigem());
	output.setAtivo(input.getAtivo());
	if (input.isEditavel() != null) {
	    output.setEditavel(input.isEditavel());
	}

	return output;
    }
}

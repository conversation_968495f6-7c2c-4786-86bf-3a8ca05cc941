package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.configuracaoRc.produto;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoRcProdutoRecord;
import br.tur.reservafacil.dominio.Produto;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcProduto;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.CustomMapper;
import org.springframework.stereotype.Component;

/**
 * Created by Gabriel<PERSON> on 20/08/25 - GA-12251.
 */
@Component
public class ConfiguracaoRcProduto2ConfiguracaoRcProdutoRecordMapper
        implements CustomMapper<ConfiguracaoRcProduto, ConfiguracaoRcProdutoRecord> {

    @Override
    public ConfiguracaoRcProdutoRecord map(ConfiguracaoRcProduto input, ConfiguracaoRcProdutoRecord output) {
        if (input.getId() != null) {
            output.setId(input.getId());
        }
        if (input.getCodigoRc() != null) {
            output.setCodigoRc(input.getCodigoRc());
        }
        output.setTaxaBolsa(input.getTaxaBolsa());
        output.setTaxaBolsaPerc(input.getTaxaBolsaPerc());
        output.setTaxaBolsaChd(input.getTaxaBolsaChd());
        output.setTaxaBolsaPercChd(input.getTaxaBolsaPercChd());
        output.setTaxaBolsaInf(input.getTaxaBolsaInf());
        output.setTaxaBolsaPercInf(input.getTaxaBolsaPercInf());
        output.setConfiguracaoRcId(input.getConfiguracaoRcId());
        output.setProdutoId(this.getProdutoId(input.getProduto()));
        output.setMoedaRepasse(input.getMoedaRepasse().name());
        if (input.getAtivo() != null) {
            output.setAtivo(input.getAtivo());
        } else {
            output.setAtivo(null);
        }
        return output;
    }

    private Integer getProdutoId(Produto produto) {
        return produto != null ? produto.getId() : null;
    }

}


package br.tur.reservafacil.dominio.aereo;

import br.tur.reservafacil.dominio.Empresa;
import br.tur.reservafacil.dominio.Filial;
import br.tur.reservafacil.dominio.Produto;
import br.tur.reservafacil.dominio.aereo.interfaces.Identificavel;
import br.tur.reservafacil.dominio.aereo.interfaces.Restringivel;
import br.tur.reservafacil.dominio.aereo.interfaces.Versionavel;
import br.tur.reservafacil.dominio.tipo.*;
import br.tur.reservafacil.dominio.util.PairOfObjects;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static br.tur.reservafacil.dominio.util.PairOfObjects.aPairOfObjects;

/**
 * Created by <PERSON><PERSON><PERSON> on 4/26/16.
 */
public class ConfiguracaoBusca implements Serializable, Identificavel<Integer>, Restringivel<Integer>, Versionavel<Integer> {

    private static final long serialVersionUID = 8714498225845309600L;

    private Integer id;

    private String nome;

    private Empresa empresa;

    private List<Filial> filiais = new ArrayList<>();

    private Integer prioridade;

    private List<Restricao> restricoes = new ArrayList<>();

    private List<AcordoComercial> acordosComerciais = new ArrayList<>();

    private List<ConfiguracaoBuscaCiasExcluidas> ciasExcluidas = new ArrayList<>();

    private List<Produto> produtos = new ArrayList<>();

    private StatusPublicacao statusPublicacao = StatusPublicacao.NAO_PUBLICADA;

    private NacInt nacInt;

    private Integer idOrigem;

    private Integer versao = 0;

    private Boolean editavel = Boolean.TRUE;

    private Boolean agrupaChamadaUnica = Boolean.FALSE;

    private Boolean ativo = Boolean.TRUE;

    private BigDecimal peso;

    private Boolean bestPrice = Boolean.FALSE;

    //Este model só será utilizado para facilitar a visualização e evitar processamento no frontend
    private List<RestricaoAgrupadora> restricoesAgrupadas = new ArrayList<>();

    public String getNome() {
	return nome;
    }

    public void setNome(String nome) {
	this.nome = nome;
    }

    public Integer getId() {
	return id;
    }

    public void setId(Integer id) {
	this.id = id;
    }

    public Empresa getEmpresa() {
	return empresa;
    }

    public void setEmpresa(Empresa empresa) {
	this.empresa = empresa;
    }

    public List<Filial> getFiliais() {
	return filiais;
    }

    public void setFiliais(List<Filial> filiais) {
	this.filiais = filiais;
    }

    public Integer getPrioridade() {
	return prioridade;
    }

    public void setPrioridade(Integer prioridade) {
	this.prioridade = prioridade;
    }

    public List<Restricao> getRestricoes() {
	return restricoes;
    }

    public void setRestricoes(List<Restricao> restricoes) {
	this.restricoes = restricoes;
    }

    public List<AcordoComercial> getAcordosComerciais() {
	return acordosComerciais;
    }

    public void setAcordosComerciais(List<AcordoComercial> acordosComerciais) {
	this.acordosComerciais = acordosComerciais;
    }

    public List<ConfiguracaoBuscaCiasExcluidas> getCiasExcluidas() {
	return ciasExcluidas;
    }

    public void setCiasExcluidas(List<ConfiguracaoBuscaCiasExcluidas> ciasExcluidas) {
	this.ciasExcluidas = ciasExcluidas;
    }

    public List<Produto> getProdutos() {
	return produtos;
    }

    public void setProdutos(List<Produto> produtos) {
	this.produtos = produtos;
    }

    public StatusPublicacao getStatusPublicacao() {
	return statusPublicacao;
    }

    public void setStatusPublicacao(StatusPublicacao statusPublicacao) {
	this.statusPublicacao = statusPublicacao;
    }

    public NacInt getNacInt() {
	return nacInt;
    }

    public void setNacInt(NacInt nacInt) {
	this.nacInt = nacInt;
    }

    public Integer getIdOrigem() {
	return idOrigem != null ? idOrigem : id;
    }

    public void setIdOrigem(Integer idOrigem) {
	this.idOrigem = idOrigem;
    }

    public Integer getVersao() {
	return versao;
    }

    public void setVersao(Integer versao) {
	this.versao = versao;
    }

    public void atualizaVersao() {
	this.versao++;
    }

    public Boolean isEditavel() {
	return editavel;
    }

    public void setEditavel(Boolean editavel) {
	this.editavel = editavel;
    }

    public Boolean getAtivo() {
	return ativo;
    }

    public void setAtivo(Boolean ativo) {
	this.ativo = ativo;
    }

    public boolean isConfiguracaoOrigem() {
        if(this.getIdOrigem() != null) {
            return this.getIdOrigem().equals(id);
        }
        return true;
    }

    public List<RestricaoAgrupadora> getRestricoesAgrupadas() {
	return restricoesAgrupadas;
    }

    public void setRestricoesAgrupadas(List<RestricaoAgrupadora> restricoesAgrupadas) {
	this.restricoesAgrupadas = restricoesAgrupadas;
	this.desagrupaRestricoes();
    }

    public void agrupaRestricoes() {
	if (this.restricoes != null && !restricoes.isEmpty()) {
	    this.restricoesAgrupadas = this.geraListaDeRestricaoAgrupadora();
	}
    }

    public List<RestricaoAgrupadora> geraListaDeRestricaoAgrupadora(){
	Map<PairOfObjects<TipoRestricao, TipoOperador>, List<Restricao>> restricaoConfigMap = this.agrupaRestricoesPorTipoEOperador();
	return restricaoConfigMap.entrySet()
			.stream()
			.map(entry -> new RestricaoAgrupadora(entry.getKey().getTail(), new TipoDadoRestricao(entry.getKey().getHead()), entry.getValue()))
			.collect(Collectors.toList());
    }

    private Map<PairOfObjects<TipoRestricao, TipoOperador>, List<Restricao>> agrupaRestricoesPorTipoEOperador() {
	return this.restricoes.stream().collect(
			Collectors.groupingBy(restricao -> aPairOfObjects(restricao.getTipoRestricao(), restricao.getTipoOperador())));
    }

    private Map<SistEmis, List<AcordoComercial>> getAcordosComerciaisAgrupados() {
	if(acordosComerciais != null && !acordosComerciais.isEmpty()) {
	    return this.acordosComerciais.stream().collect(Collectors.groupingBy(acordo -> acordo.getCredencial().getSistEmis()));
	}
	return new HashMap<>();
    }

    public Boolean getAgrupaChamadaUnica() {
	return agrupaChamadaUnica;
    }

    public void setAgrupaChamadaUnica(Boolean agrupaChamadaUnica) {
	this.agrupaChamadaUnica = agrupaChamadaUnica;
    }

    public BigDecimal getPeso() {
	return peso;
    }

    public void setPeso(BigDecimal peso) {
	this.peso = peso;
    }

    public Boolean getBestPrice() {
	return this.bestPrice;
    }

    public void setBestPrice(Boolean bestPrice) {
	this.bestPrice = bestPrice;
    }

    @Override
    public boolean equals(Object o) {
	if (this == o)
	    return true;
	if (o == null || getClass() != o.getClass())
	    return false;
	ConfiguracaoBusca that = (ConfiguracaoBusca)o;
	return Objects.equals(id, that.id) &&
			Objects.equals(nome, that.nome) &&
			Objects.equals(empresa, that.empresa) &&
			Objects.equals(filiais, that.filiais) &&
			Objects.equals(prioridade, that.prioridade) &&
			Objects.equals(restricoes, that.restricoes) &&
			Objects.equals(acordosComerciais, that.acordosComerciais) &&
			Objects.equals(ciasExcluidas, that.ciasExcluidas) &&
			Objects.equals(produtos, that.produtos) &&
			Objects.equals(versao, that.versao) &&
			Objects.equals(idOrigem, that.idOrigem) &&
			Objects.equals(editavel, that.editavel) &&
			Objects.equals(agrupaChamadaUnica, that.agrupaChamadaUnica) &&
			Objects.equals(bestPrice, that.bestPrice) &&
			Objects.equals(nacInt, that.nacInt);
    }

    @Override
    public int hashCode() {
	return Objects.hash(id, nome, empresa, filiais, prioridade, restricoes, acordosComerciais, ciasExcluidas, produtos, nacInt, versao, idOrigem,
			    agrupaChamadaUnica, editavel, bestPrice);
    }

    @Override
    public String toString() {
	return "ConfiguracaoBusca{" +
			"id=" + id +
			", nome='" + nome + '\'' +
			", empresa=" + empresa +
			", filiais=" + filiais +
			", prioridade=" + prioridade +
			", restricoes=" + restricoes +
			", acordosComerciais=" + acordosComerciais +
			", ciasExcluidas=" + ciasExcluidas +
			", produtos=" + produtos +
			", nacInt=" + nacInt +
			", peso=" + peso +
			", idOrigem=" + idOrigem +
			", versao=" + versao +
			", editavel=" + editavel +
			", agrupaChamadaUnica=" + agrupaChamadaUnica +
			'}';
    }
}

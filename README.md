# Precificador Core
O precificador core tem a responsabilidade de executar todas as "pilotagens" feitas pelos usuários em seu front. Ele é responsável pelo direcionamento das credenciais até a precificação das recomendações.
 
Os seguintes macro serviços estão disponíveis nele:
* Provedor, credencial e unidade operacional a ser utilizada em uma busca
* Precificar recomendações: Markup, Fee, Comissão, Incentivo, RAV
* Prover os opções de parcelamento da unidade de negócio e cia aérea
* Manter os cadastros de perfil tarifários das cias aéreas
* Prover os cadastros de unidades operacionais
* Possibilitar bloquear um sistema emissor e/ou cia

Mais informações: https://git.reservafacil.tur.br/precificador/precificador/wikis/home

----

## Tecnologias usadas no projeto
* Spring (boot 2, rest, security, aop, cache)
* Jooq
* Flyway
* Guava
* Jedis
* Akka
* JWT
* Apache POI
* Postgres

## Para começar a desenvolver no Precificador

##### Base de dados
Para instalar a base de dados atualizada basta excutar a imagem de desenvolvimento do RF através do devtools e executar o script do flyway:
```
$ ./docker-migrate.sh && ./docker-migrate-bigdata.sh
```

##### Subir a base e o redis local no Docker
```
$ cd core/ && sudo ./scripts/run-docker-compose.sh --custom postgres redis
```

##### Iniciando a aplicação com Spring Boot

Para iniciar o servidor de debug excute o script abaixo na raiz do 
```
$ cd core/ && mvn spring-boot:run -Pdev
```

<!--o script roda o seguinte comando maven-->

<!--```-->
<!--mvn -Pdev clean spring-boot:run -Drun.jvmArguments="-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=y,address=5006"-->
<!--```-->

<!--que inicia o servidor de debug na porta 5006-->
<!--```-->
<!--[INFO] --- spring-boot-maven-plugin:1.4.3.RELEASE:run (default-cli) @ precificador ----->
<!--[INFO] Attaching agents: []-->
<!--Listening for transport dt_socket at address: 5006-->
<!--```-->

<!--para finalizar a inicialização basta configurar o debug remoto através da IDE.-->
<!--O exemplo a seguir mostra como configurar no Intellij.-->
<!--Acess: run -> Debug... -> Edit configurations... -> botão + -> Remote-->
<!--<img src="http://localhost/idea_debug.png" />-->

<!--clique em 'Debug'.-->

<!--###### Execução de teste de carga (verificar ambiente configurado)-->

Perfil Tarifário:
```
mvn gatling:execute -Dgatling.simulationClass=com.developers.perspective.simulation.PerfilTarifarioSimulation
```
Precificacão:
```
mvn gatling:execute -Dgatling.simulationClass=com.developers.perspective.simulation.PrecificacaoSimulation
```

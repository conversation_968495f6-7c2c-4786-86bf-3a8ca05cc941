<ehcache xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="ehcache.xsd" updateCheck="false"
         monitoring="autodetect" dynamicConfig="true">

    <!-- By default, Ehcache stored the cached files in temp folder. -->
    <diskStore path="java.io.tmpdir" />
    <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->

    <defaultCache eternal="false" maxElementsInMemory="2000"
                  overflowToDisk="false" diskPersistent="false" timeToIdleSeconds="0"
                  timeToLiveSeconds="600" memoryStoreEvictionPolicy="LRU" statistics="true" />

    <cache name="todosAeroportos"
           maxEntriesLocalHeap="7000"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToLiveSeconds="1800">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="FlagFeatureComponent::findAll"
           maxEntriesLocalHeap="100"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToLiveSeconds="1800">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="todosAeroportosParaRestricoes"
           timeToLiveSeconds="300"
           timeToIdleSeconds="60"
           maxEntriesLocalHeap="100000"
           diskSpoolBufferSizeMB="75"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="grupos"
           maxEntriesLocalHeap="500"
           timeToIdleSeconds="30"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="filiais"
           maxEntriesLocalHeap="500"
           timeToIdleSeconds="30"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="recursoIntegracao"
           maxEntriesLocalHeap="500"
           timeToIdleSeconds="30"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="EmpresaProdutoDefaultService::findProdutoByEmpresa"
           maxEntriesLocalHeap="500"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="GrupoService::findByReferenciaEEmpresaId"
           maxEntriesLocalHeap="10000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="EmpresaConfiguracaoTimeoutService::findTimeoutByEmpresa"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="MarkupProdutoService::findByConfiguracaoId"
           maxEntriesLocalHeap="5000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="MarkupProdutoService::findByExcecaoId"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoPrecificacaoService::findByCiaTipoConfigEmpresaIdProduto"
           maxEntriesLocalHeap="2000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="180"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoPrecificacaoService::findByCiaTipoConfigEmpresaIdProdutoBPRICEMarkupLimite"
           maxEntriesLocalHeap="2000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="180"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="CodigoContratoService::findByIdentificador"
           maxEntriesLocalHeap="3000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>
    <cache name="CodigoContratoService::findCiasAereasByCodigoContratoId"
           maxEntriesLocalHeap="100"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>
    <cache name="ConfiguracaoPrecificacaoComercialService::findByCiaTipoConfigEmpresaIdProduto"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoPrecificacaoComercialService::reportConditionByAgencyBranchCompany"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="240"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="SistemaEmissorInativoBuscaDispService::getAllSistemaEmissorInativoBuscaDisp"
           maxEntriesLocalHeap="100"
           timeToIdleSeconds="240"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoPrecificacaoComercialService::reportConditionByCodigoPrecificacao"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="240"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>
    <cache name="ConfiguracaoPrecificacaoComercialProdutoService::findByConfigPrecificacaoComercialId"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoRavService::findByEmpresaCiaProdutoTipoTarifa"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoFeeService::findByEmpresaCiaProdutoTipoTarifaNacInt"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoFeeService::findByEmpresaCiaProdutoTipoTarifaNacIntComRes"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoRavService::findViewByEmpresaCiaProdutoTipoTarifaNacIntGrupo"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoDuService::findByCiaNacInt"
           maxEntriesLocalHeap="100"
           timeToIdleSeconds="60"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoDuService::findAll"
           maxEntriesLocalHeap="100"
           timeToIdleSeconds="60"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoFiltrosRecomendacaoService::findByEmpresa"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoDesativaValorService::findByProdutoEmpresa"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoPrecificacaoService::findByIds"
           maxEntriesLocalHeap="5000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="600"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoPrecificacaoService::findByKey"
           maxEntriesLocalHeap="5000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="600"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoPrecificacaoService::findEagerByKey"
           maxEntriesLocalHeap="5000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="600"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoPrecificacaoService::findByFiltroConfiguracao"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="BloqueioSistEmisService::bloqueiosAtivos"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="cambio"
           maxEntriesLocalHeap="90"
           eternal="false"
           diskSpoolBufferSizeMB="20"
           timeToLiveSeconds="1200"
           timeToIdleSeconds="14400"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="ConfiguracaoEstrategiaBuscaOWService::findBySistEmis"
           maxEntriesLocalHeap="20"
           timeToIdleSeconds="60"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="EmpresaService::findByKey"
           maxEntriesLocalHeap="500"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="EmpresaService::findByReferencia"
           maxEntriesLocalHeap="500"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ProdutoService::findByKey"
           maxEntriesLocalHeap="500"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ProdutoService::buscaProdutoDefault"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="AgenciaService::buscaAgenciasPorExcecao"
           maxEntriesLocalHeap="100"
           timeToIdleSeconds="30"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="AgenciaService::findByIdEmpresaAndReferencia"
           maxEntriesLocalHeap="5000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="60"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="AgenciaService::findByKey"
           maxEntriesLocalHeap="5000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="60"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="AcordoComercialService::findAcordosVigentesByConfiguracaoId"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="30"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="40"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="AcordoComercialService::findAcordosByUnidadesComCapturaEEmpresa"
           maxEntriesLocalHeap="500"
           timeToIdleSeconds="30"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="AcordoComercialService::findAcordosByUnidadePadrao"
           maxEntriesLocalHeap="500"
           timeToIdleSeconds="30"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="GrupoService::findByReferenciaEmpresa"
           maxEntriesLocalHeap="5000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="GrupoService::findPadraoByEmpresa"
           maxEntriesLocalHeap="5000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>
    <cache name="GrupoService::findNomeByKey"
           maxEntriesLocalHeap="5000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>


    <cache name="FilialService::findByReferenciaEmpresa"
           maxEntriesLocalHeap="2000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off">
        <persistence strategy="localTempSwap"/>
    </cache>

    <cache name="FilialService::findByKey"
           maxEntriesLocalHeap="2000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off">
        <persistence strategy="localTempSwap"/>
    </cache>

    <cache name="FilialService::findPadraoByEmpresa"
           maxEntriesLocalHeap="2000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off">
        <persistence strategy="localTempSwap"/>
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="FilialService::findNomeByKey"
           maxEntriesLocalHeap="2000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off">
        <persistence strategy="localTempSwap"/>
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ciasAereas"
           maxEntriesLocalHeap="2000"
           timeToIdleSeconds="30"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="40"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="aeroportosPorCidade"
           maxEntriesLocalHeap="7000"
           diskSpoolBufferSizeMB="40"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToLiveSeconds="1800">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="cidadePorAeroporto"
           maxEntriesLocalHeap="10000"
           diskSpoolBufferSizeMB="75"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToLiveSeconds="1800">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="paisPorAeroporto"
           maxEntriesLocalHeap="5000"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToLiveSeconds="1800">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="paisesPorAeroportos"
           maxEntriesLocalHeap="5000"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToLiveSeconds="1800">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="estadoPorAeroporto"
           maxEntriesLocalHeap="5000"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToLiveSeconds="1800">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="continentePorAeroporto"
           maxEntriesLocalHeap="1000"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToLiveSeconds="1800">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="continentesPorAeroporto"
           maxEntriesLocalHeap="1000"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToLiveSeconds="1800">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="aeroportoPorCodigo"
           maxEntriesLocalHeap="8000"
           diskSpoolBufferSizeMB="70"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToLiveSeconds="1800">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="DestinoCiasService::getAeroportosOperadosPorCia"
           maxEntriesLocalHeap="100"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToIdleSeconds="30"
           timeToLiveSeconds="600">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="RotasCiasService::getRotasOperadasPorCia"
           maxEntriesLocalHeap="100"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToIdleSeconds="30"
           timeToLiveSeconds="600">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="RotaService::findAllCiaRoutes"
           maxEntriesLocalHeap="100"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToIdleSeconds="30"
           timeToLiveSeconds="600">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="FilialUnidadeOperacionalService::buscaPorEmpresaIdReferenciaFilialSistEmisTipoChamada"
           maxEntriesLocalHeap="5000"
           diskSpoolBufferSizeMB="40"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToIdleSeconds="30"
           timeToLiveSeconds="300">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="ConfiguracaoBuscaService::isBestPriceEnabled"
           maxEntriesLocalHeap="1000"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToIdleSeconds="30"
           timeToLiveSeconds="600">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="ConfiguracaoBuscaService::findAcordosByConfiguracaoId"
           maxEntriesLocalHeap="1000"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToIdleSeconds="30"
           timeToLiveSeconds="600">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="ConfiguracaoBuscaService::configuracoesSoComRestricaoByEmpresaSisteEmisProduto"
           maxEntriesLocalHeap="2000"
           diskSpoolBufferSizeMB="30"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToIdleSeconds="30"
           timeToLiveSeconds="300">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="ConfiguracaoBuscaService::findConfiguracaoBuscaPorFilialEEmpresaESistEmisProduto"
           maxEntriesLocalHeap="1000"
           diskSpoolBufferSizeMB="30"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToIdleSeconds="30"
           timeToLiveSeconds="300">
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="UnidadeOperacionalService::findAll"
           maxEntriesLocalHeap="15000"
           diskSpoolBufferSizeMB="75"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToIdleSeconds="30"
           timeToLiveSeconds="600">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="UnidadeOperacionalService::findBySistEmis"
           maxEntriesLocalHeap="2000"
           diskSpoolBufferSizeMB="40"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToIdleSeconds="30"
           timeToLiveSeconds="300">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="UnidadeOperacionalService::findBySistEmisAndOfficeIdEagger"
           maxEntriesLocalHeap="1000"
           diskSpoolBufferSizeMB="30"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToIdleSeconds="30"
           timeToLiveSeconds="300">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="UnidadeOperacionalService::findBySistEmisAndOfficeId"
           maxEntriesLocalHeap="1000"
           diskSpoolBufferSizeMB="30"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToIdleSeconds="30"
           timeToLiveSeconds="300">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="UnidadeOperacionalService::recuperaDadosBuscaDeUnidades"
           maxEntriesLocalHeap="100"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToIdleSeconds="30"
           timeToLiveSeconds="300">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="UnidadeOperacionalService::findUnidadeOperacionalSubstitutaByCodigo"
           maxEntriesLocalHeap="100"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToIdleSeconds="30"
           timeToLiveSeconds="300">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="ImportaLocService::findByProdutoSistEmisEmpresaFilialAgenciaGrupo"
           maxEntriesLocalHeap="200"
           diskSpoolBufferSizeMB="30"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           timeToIdleSeconds="30"
           timeToLiveSeconds="300">
        <persistence strategy="localTempSwap" />
    </cache>

    <cache name="ConfiguracaoBuscaCiasExcluidasService::findByConfigBuscaId"
           maxEntriesLocalHeap="2000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoBuscaCiasExcluidasService::findByConfigBuscaIdProdutoSistEmis"
           maxEntriesLocalHeap="2000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="PerfilTarifarioService::findAll"
           maxEntriesLocalHeap="2000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="900"
           eternal="false"
           diskSpoolBufferSizeMB="70"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="PerfilTarifarioService::findBySistEmis"
           maxEntriesLocalHeap="2000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="900"
           eternal="false"
           diskSpoolBufferSizeMB="70"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="CredencialPaymenthubJooqDao::getByEmpresaId"
           maxEntriesLocalHeap="100"
           timeToIdleSeconds="300"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="20"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off">
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="CondicaoPagamentoService::obterCondicoesPorPrecificavelEEmpresa"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="600"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="CondicaoPagamentoService::findByEmpresa"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="600"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoRavService::findByEmpresa"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="600"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoPrecificacaoComercialService::findByEmpresa"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="600"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ObfeeCredencialService::findByCredencialIdAndCodigoCia"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="900"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="UsuarioService::findByLogin::core"
           maxEntriesLocalHeap="100"
           timeToIdleSeconds="60"
           timeToLiveSeconds="900"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
           copyOnRead="true"
           copyOnWrite="true"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoPrecificacaoFretamentoService::buscaConfiguracaoFretamento"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="900"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoFeeRepositoryRetriever::findById"
           maxEntriesLocalHeap="5000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoFeeRepositoryRetriever::findByEmpresa"
           maxEntriesLocalHeap="1000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="600"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoRavService::findEagerByKey"
           maxEntriesLocalHeap="5000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="UnidadeOperacionalService::findByKey"
           maxEntriesLocalHeap="15000"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="75"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="CredencialService::findOneByKey"
           maxEntriesLocalHeap="1500"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="50"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="UnidadeOperacionalService::findBySistEmisAndIata"
           maxEntriesLocalHeap="1500"
           timeToIdleSeconds="60"
           timeToLiveSeconds="1800"
           eternal="false"
           diskSpoolBufferSizeMB="75"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="configuracaoProdutoService::findAll"
           maxEntriesLocalHeap="1500"
           timeToIdleSeconds="60"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="75"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoRcService::findByEmpresa"
           maxEntriesLocalHeap="1500"
           timeToIdleSeconds="60"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="75"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoRcService::findById"
           maxEntriesLocalHeap="1500"
           timeToIdleSeconds="60"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="75"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoRcService::carregaDadosParaPrecificacao"
           maxEntriesLocalHeap="1500"
           timeToIdleSeconds="60"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="75"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>

    <cache name="ConfiguracaoRcService::findByConfigRcId"
           maxEntriesLocalHeap="1500"
           timeToIdleSeconds="60"
           timeToLiveSeconds="300"
           eternal="false"
           diskSpoolBufferSizeMB="75"
           memoryStoreEvictionPolicy="LFU"
           transactionalMode="off"
    >
        <persistence strategy="localTempSwap" />
        <!-- sizeOfPolicy maxDepth="1000" maxDepthExceededBehavior="abort" / -->
    </cache>


</ehcache>

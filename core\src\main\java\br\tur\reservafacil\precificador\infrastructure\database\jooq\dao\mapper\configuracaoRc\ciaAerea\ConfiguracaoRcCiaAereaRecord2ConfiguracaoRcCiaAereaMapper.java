package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.configuracaoRc.ciaAerea;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoRcCiaAereaRecord;
import br.tur.reservafacil.dominio.aereo.CiaAerea;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRc;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcCiaAerea;
import org.jooq.RecordMapper;
import org.springframework.stereotype.Component;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
@Component
public class ConfiguracaoRcCiaAereaRecord2ConfiguracaoRcCiaAereaMapper
		implements RecordMapper<ConfiguracaoRcCiaAereaRecord, ConfiguracaoRcCiaAerea> {

    @Override
    public ConfiguracaoRcCiaAerea map(ConfiguracaoRcCiaAereaRecord record) {
        ConfiguracaoRcCiaAerea configuracaoPrecificacaoRestricao = new ConfiguracaoRcCiaAerea();
	configuracaoPrecificacaoRestricao.setId(record.getId());
	configuracaoPrecificacaoRestricao.setCiaAerea(this.getCiaAereaLazy(record.getCiaAereaId()));
	configuracaoPrecificacaoRestricao.setConfiguracaoRc(this.getConfiguracaoLazy(record.getConfiguracaoRcId()));
	return configuracaoPrecificacaoRestricao;
    }

    private ConfiguracaoRc getConfiguracaoLazy(Integer configuracaoId) {
	if (configuracaoId != null) {
	    ConfiguracaoRc configuracaoPrecificacao = new ConfiguracaoRc();
	    configuracaoPrecificacao.setId(configuracaoId);
	    return configuracaoPrecificacao;
	}
	return null;
    }

    private CiaAerea getCiaAereaLazy(Integer ciaAereaId) {
	if (ciaAereaId != null) {
	    CiaAerea ciaAerea = new CiaAerea();
	    ciaAerea.setId(ciaAereaId);
	    return ciaAerea;
	}
	return null;
    }
}

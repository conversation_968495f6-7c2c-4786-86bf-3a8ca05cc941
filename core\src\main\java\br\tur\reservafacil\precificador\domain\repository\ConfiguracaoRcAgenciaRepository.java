package br.tur.reservafacil.precificador.domain.repository;

import br.tur.reservafacil.dominio.Agencia;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcAgencia;

import java.util.List;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
public interface ConfiguracaoRcAgenciaRepository
		extends Repository<ConfiguracaoRcAgencia, Integer> {

    void deleteByConfigPrecificacaoId(Integer idConfigPrecificacao);

    List<Agencia> findAgenciasByConfiguracaoId(Integer configuracaoId);

}

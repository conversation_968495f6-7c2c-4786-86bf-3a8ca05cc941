package br.tur.reservafacil.precificador.application.service.cache;

import br.tur.reservafacil.dominio.aereo.ConfiguracaoRc;
import br.tur.reservafacil.precificador.domain.repository.ConfiguracaoRcRepository;
import com.newrelic.api.agent.Trace;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by <PERSON><PERSON> on 20/08/25 - GA-12251.
 */
@Component
public class ConfiguracaoRcRedisCacheComponent {

    @Autowired
    private ConfiguracaoRcRepository repository;

    @Trace
    @Cacheable(cacheNames = "ConfiguracaoRcService::findByEmpresa", cacheManager = "redisCacheManager")
    public List<ConfiguracaoRc> findByEmpresa(Integer empresaId, boolean isBuscaDeTeste, String timeToCache) {
        return repository.findByEmpresa(empresaId, isBuscaDeTeste);
    }

    @Trace
    @Cacheable(cacheNames = "ConfiguracaoRcService::findById", cacheManager = "redisCacheManager")
    public ConfiguracaoRc findById(Integer configRcId) {
        return repository.findEaggerByKey(configRcId);
    }
}

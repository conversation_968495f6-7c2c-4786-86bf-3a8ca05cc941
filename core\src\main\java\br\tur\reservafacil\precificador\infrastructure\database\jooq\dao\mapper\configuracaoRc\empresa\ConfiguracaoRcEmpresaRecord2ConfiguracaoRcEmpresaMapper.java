package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.configuracaoRc.empresa;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoRcEmpresaRecord;
import br.tur.reservafacil.dominio.Empresa;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRc;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcEmpresa;
import org.jooq.RecordMapper;
import org.springframework.stereotype.Component;

/**
 * Created by Gabriel<PERSON> on 20/08/25 - GA-12251.
 */
@Component
public class ConfiguracaoRcEmpresaRecord2ConfiguracaoRcEmpresaMapper
		implements RecordMapper<ConfiguracaoRcEmpresaRecord, ConfiguracaoRcEmpresa> {

    @Override
    public ConfiguracaoRcEmpresa map(ConfiguracaoRcEmpresaRecord record) {
        ConfiguracaoRcEmpresa configuracaoPrecificacaoEmpresa = new ConfiguracaoRcEmpresa();
	configuracaoPrecificacaoEmpresa.setId(record.getId());
	configuracaoPrecificacaoEmpresa.setEmpresa(this.getEmpresaLazy(record.getEmpresaId()));
	configuracaoPrecificacaoEmpresa.setConfiguracaoRc(this.getConfiguracaoLazy(record.getConfiguracaoRcId()));
	return configuracaoPrecificacaoEmpresa;
    }

    private ConfiguracaoRc getConfiguracaoLazy(Integer configuracaoId) {
	if (configuracaoId != null) {
	    ConfiguracaoRc configuracaoPrecificacao = new ConfiguracaoRc();
	    configuracaoPrecificacao.setId(configuracaoId);
	    return configuracaoPrecificacao;
	}
	return null;
    }

    private Empresa getEmpresaLazy(Integer empresaId) {
	if (empresaId != null) {
	    Empresa empresa = new Empresa();
	    empresa.setId(empresaId);
	    return empresa;
	}
	return null;
    }
}

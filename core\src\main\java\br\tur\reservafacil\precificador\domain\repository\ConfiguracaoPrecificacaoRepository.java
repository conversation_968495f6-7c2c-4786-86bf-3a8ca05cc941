package br.tur.reservafacil.precificador.domain.repository;

import br.tur.reservafacil.dominio.aereo.ConfiguracaoPrecificacao;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoPrecificacaoIsaCondicao;
import br.tur.reservafacil.dominio.tipo.TipoConfigPrecificacao;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON> on 6/9/16.
 */
public interface ConfiguracaoPrecificacaoRepository extends VersionavelRepository<ConfiguracaoPrecificacao, Integer>  {


    List<ConfiguracaoPrecificacao> buscaSoComRestricaoByCiaTipoConfigEmpresaIdProduto(String codigoCia,
                                                                                      TipoConfigPrecificacao tipoConfigPrecificacao,
                                                                                      Integer empresaId, String produto,
                                                                                      Boolean isBuscaDeTeste,
                                                                                      Boolean markupCiaFirst);

    List<ConfiguracaoPrecificacao> buscaSoComRestricaoByCiaTipoConfigEmpresaIdProdutoBPRICEMarkupLimite(String codigoCia,
                                                                                                        TipoConfigPrecificacao tipoConfigPrecificacao,
                                                                                                        Integer empresaId, String produto);

    ConfiguracaoPrecificacao findEaggerByKey(Integer idConfiguracaoPrecificacao);

    List<ConfiguracaoPrecificacao> findEagerByKeys(List<Integer> idsConfiguracoesPrecificacao);

    List<ConfiguracaoPrecificacao> findByParameters(int page, int pageSize, String orderBy, Map<String, String> params);

    Integer countByParameters(Map<String, String> params);

    List<ConfiguracaoPrecificacao> buscaConfiguracoesPrecificacaoParaNotificacao();

    ConfiguracaoPrecificacaoIsaCondicao findConfiguracaoPrecificacaoRestritaByIds(List<Integer> ids);

    List<ConfiguracaoPrecificacao> findByKeys(Set<Integer> ids);

    List<ConfiguracaoPrecificacao> findEagerByParameters(Map<String, String> params);

    List<ConfiguracaoPrecificacao> findByConfigPrecificacaoComercialId(Integer configuracaoId);
}

package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.configuracaoRc;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoRcRecord;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRc;
import br.tur.reservafacil.dominio.tipo.StatusPublicacao;
import org.jooq.RecordMapper;
import org.springframework.stereotype.Component;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
@Component
public class ConfiguracaoRcRecord2ConfiguracaoRcMapper
		implements RecordMapper<ConfiguracaoRcRecord, ConfiguracaoRc> {

    @Override
    public ConfiguracaoRc map(ConfiguracaoRcRecord record) {

        ConfiguracaoRc configuracaoPrecificacao = new ConfiguracaoRc();
	configuracaoPrecificacao.setId(record.getId());
	configuracaoPrecificacao.setPrioridade(record.getPrioridade());
	configuracaoPrecificacao.setNome(record.getNome());
	configuracaoPrecificacao.setStatusPublicacao(record.getStatusPublicacao()!=null?StatusPublicacao.valueOf(record.getStatusPublicacao()):null);
	configuracaoPrecificacao.setVersao(record.getVersao());
	configuracaoPrecificacao.setIdOrigem(record.getIdOrigem());
	configuracaoPrecificacao.setEditavel(record.getEditavel());
	configuracaoPrecificacao.setAtivo(record.getAtivo());

	return configuracaoPrecificacao;
    }

}

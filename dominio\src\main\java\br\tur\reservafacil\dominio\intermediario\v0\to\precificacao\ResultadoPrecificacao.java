package br.tur.reservafacil.dominio.intermediario.v0.to.precificacao;

import br.tur.reservafacil.dominio.aereo.v0.to.common.OpcoesParcelamentoResponse;
import br.tur.reservafacil.dominio.common.ValorTarifa;
import br.tur.reservafacil.dominio.tipo.TipoDetalhePrecificacao;
import br.tur.reservafacil.dominio.tipo.TipoValor;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

/**
 * Created by da<PERSON><PERSON> on 5/16/17.
 */
public class ResultadoPrecificacao implements Serializable {

    private String idAgrupamento;
    private String idUnidadePrecificavel;
    private Integer idConfigRestrita;
    private Integer configuracaoRavId;
    private Integer configuracaoFeeId;
    private Integer configuracaoPrecificacaoComercial;
    private List<Integer> idsConfigsPadroes = new ArrayList<>();
    private Integer idConfigSucessor;
    private String codigoContrato;
    private Boolean contratoProprio = Boolean.FALSE;
    private String produtoUtilizado;
    private Boolean requiredHotel = Boolean.FALSE;
    private String tourcode;
    private String endosso;
    private String osi;
    private Boolean emissionOnline;
    private String condicaoPrecificacao;
    private Map<String, ValorTarifa> valoresCalculados = new HashMap<>();
    private List<TipoValor> valoresDesativados = new ArrayList<>();
    private MarkupProduto markupProduto;
    private Merchant merchant;
    private PerfilTarifario perfilTarifario;
    private Map<TipoDetalhePrecificacao, String> detalhesPrecificacao = new HashMap<>();
    private String condicaoPrecificacaoComercial;
    private OpcoesParcelamentoResponse opcoesParcelamentoResponse;

    @JsonIgnore
    public String getId() {
	return this.idAgrupamento + "-" + idUnidadePrecificavel;
    }

    public String getIdAgrupamento() {
	return idAgrupamento;
    }

    public void setIdAgrupamento(String idAgrupamento) {
	this.idAgrupamento = idAgrupamento;
    }

    public String getIdUnidadePrecificavel() {
	return idUnidadePrecificavel;
    }

    public void setIdUnidadePrecificavel(String idUnidadePrecificavel) {
	this.idUnidadePrecificavel = idUnidadePrecificavel;
    }

    public Integer getIdConfigRestrita() {
	return idConfigRestrita;
    }

    public void setIdConfigRestrita(Integer idConfigRestrita) {
	this.idConfigRestrita = idConfigRestrita;
    }

    public List<Integer> getIdsConfigsPadroes() {
	return idsConfigsPadroes;
    }

    public void setIdsConfigsPadroes(List<Integer> idsConfigsPadroes) {
	this.idsConfigsPadroes = idsConfigsPadroes;
    }

    public Integer getIdConfigSucessor() {
        return idConfigSucessor;
    }

    public void setIdConfigSucessor(Integer idConfigSucessor) {
        this.idConfigSucessor = idConfigSucessor;
    }

    public String getCodigoContrato() {
	return codigoContrato;
    }

    public void setCodigoContrato(String codigoContrato) {
	this.codigoContrato = codigoContrato;
    }
    public Boolean getContratoProprio() {
	return contratoProprio;
    }

    public void setContratoProprio(Boolean contratoProprio) {
	this.contratoProprio = contratoProprio;
    }

    public String getProdutoUtilizado() {
	return produtoUtilizado;
    }

    public void setProdutoUtilizado(String produtoUtilizado) {
	this.produtoUtilizado = produtoUtilizado;
    }

    public Map<String, ValorTarifa> getValoresCalculados() {
	return valoresCalculados;
    }

    public void setValoresCalculados(Map<String, ValorTarifa> valoresCalculados) {
	this.valoresCalculados = valoresCalculados;
    }

    public String getTourcode() {
	return tourcode;
    }

    public void setTourcode(String tourcode) {
	this.tourcode = tourcode;
    }

    public String getEndosso() {
	return endosso;
    }

    public void setEndosso(String endosso) {
	this.endosso = endosso;
    }

    public String getOsi() {
        return osi;
    }

    public void setOsi(String osi) {
        this.osi = osi;
    }

    public Boolean getEmissionOnline() {
        return emissionOnline;
    }

    public void setEmissionOnline(Boolean emissionOnline) {
        this.emissionOnline = emissionOnline;
    }

    public List<TipoValor> getValoresDesativados() {
	return valoresDesativados;
    }

    public void setValoresDesativados(List<TipoValor> valoresDesativados) {
	this.valoresDesativados = valoresDesativados;
    }

    public MarkupProduto getMarkupProduto() {return markupProduto;}

    public void addMarkupProduto(Integer idMarkup, Integer idReferencia, BigDecimal fatorMarkup, BigDecimal fatorMarkupCalculado, BigDecimal comissao, BigDecimal incentivo){
	this.markupProduto = new MarkupProduto(idMarkup, idReferencia, fatorMarkup, fatorMarkupCalculado, comissao, incentivo);
    }

    public String getCondicaoPrecificacao() {
        return condicaoPrecificacao;
    }

    public void setCondicaoPrecificacao(String condicaoPrecificacao) {
        this.condicaoPrecificacao = condicaoPrecificacao;
    }

    public Merchant getMerchant() {
	return merchant;
    }

    public void setMerchant(Merchant merchant) {
	this.merchant = merchant;
    }

    public Boolean getRequiredHotel() {
        return requiredHotel;
    }

    public void setRequiredHotel(Boolean requiredHotel) {
        this.requiredHotel = requiredHotel;
    }

    /**
     *
     * @param id o nome da faixa etaria no momento da busca e tarifação, ou o id do passageiro no momento da reserva
     * @return
     */
    public Optional<ValorTarifa> getValorTarifaById(String id) {
	return Optional.ofNullable(this.valoresCalculados.get(id));
    }

    public void addValorCalculado(String id, ValorTarifa valorTarifa) {
	this.valoresCalculados.put(id, valorTarifa);
    }

    /**
     *
     * @return os ids das configurações padrões + os id da configuração restrita
     */
    @JsonIgnore
    public List<Integer> getIdsPrecificacao() {
	List<Integer> ids = new ArrayList<>();
	ids.add(idConfigRestrita);
    ids.add(idConfigSucessor);
	ids.addAll(idsConfigsPadroes);
	return ids;
    }

    @JsonIgnore
    public BigDecimal getTotalSobretaxas(String id) {
	return this.valoresCalculados.getOrDefault(id, new ValorTarifa()).getTotalSobretaxas();
    }

    @JsonIgnore
    public BigDecimal getTotalDetalhesTarifa(String id) {
	return this.valoresCalculados.getOrDefault(id, new ValorTarifa()).getTotalDetalhesTarifa();
    }

    @JsonIgnore
    public BigDecimal getTotalTaxas(String id) {
	return this.valoresCalculados.getOrDefault(id, new ValorTarifa()).getTotalTaxas();
    }

    public PerfilTarifario getPerfilTarifario() {
	return perfilTarifario;
    }

    public void setPerfilTarifario(PerfilTarifario perfilTarifario) {
	this.perfilTarifario = perfilTarifario;
    }

    public Map<TipoDetalhePrecificacao, String> getDetalhesPrecificacao() {
	return detalhesPrecificacao;
    }

    public void setDetalhesPrecificacao(Map<TipoDetalhePrecificacao, String> detalhesPrecificacao) {
	this.detalhesPrecificacao = detalhesPrecificacao;
    }

    public Integer getConfiguracaoRavId() {
	return configuracaoRavId;
    }

    public void setConfiguracaoRavId(Integer configuracaoRavId) {
	this.configuracaoRavId = configuracaoRavId;
    }

    public Integer getConfiguracaoFeeId() {
	return configuracaoFeeId;
    }

    public void setConfiguracaoFeeId(Integer configuracaoFeeId) {
	this.configuracaoFeeId = configuracaoFeeId;
    }

    public void removeTaxaCalculada(TipoValor tipoValor) {
	for (Map.Entry<String, ValorTarifa> valoresTarifa : valoresCalculados.entrySet()) {
	    valoresTarifa.getValue().removeTaxaSobretaxaDetalheTarifaIndividual(tipoValor);
	}
    }

    public Integer getConfiguracaoPrecificacaoComercial() {
	return configuracaoPrecificacaoComercial;
    }

    public void setConfiguracaoPrecificacaoComercial(Integer configuracaoPrecificacaoComercial) {
	this.configuracaoPrecificacaoComercial = configuracaoPrecificacaoComercial;
    }

    public String getCondicaoPrecificacaoComercial() {
	return condicaoPrecificacaoComercial;
    }

    public void setCondicaoPrecificacaoComercial(String condicaoPrecificacaoComercial) {
	this.condicaoPrecificacaoComercial = condicaoPrecificacaoComercial;
    }

    public OpcoesParcelamentoResponse getOpcoesParcelamentoResponse() {
	return opcoesParcelamentoResponse;
    }

    public void setOpcoesParcelamentoResponse(OpcoesParcelamentoResponse opcoesParcelamentoResponse) {
	this.opcoesParcelamentoResponse = opcoesParcelamentoResponse;
    }
}

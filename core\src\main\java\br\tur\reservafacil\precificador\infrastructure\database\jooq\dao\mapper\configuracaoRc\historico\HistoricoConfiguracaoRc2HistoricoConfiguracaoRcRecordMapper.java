package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.configuracaoRc.historico;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.HistoricoConfiguracaoRcRecord;
import br.tur.reservafacil.dominio.aereo.HistoricoConfiguracaoRc;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.CustomMapper;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
@Component
public class HistoricoConfiguracaoRc2HistoricoConfiguracaoRcRecordMapper
		implements CustomMapper<HistoricoConfiguracaoRc, HistoricoConfiguracaoRcRecord> {

    @Override
    public HistoricoConfiguracaoRcRecord map(HistoricoConfiguracaoRc input, HistoricoConfiguracaoRcRecord output) {
	if (input.getId() != null) {
	    output.setId(input.getId());
	}
	output.setConfiguracaoRcId(input.getConfiguracaoRc() != null ? input.getConfiguracaoRc().getId() : null);
	output.setConfiguracaoRcIdOrigem(input.getConfiguracaoRc() != null ? input.getConfiguracaoRc().getIdOrigem() : null);
	output.setDataAtualizacao(input.getDataAtualizacao() != null ? Timestamp.valueOf(input.getDataAtualizacao()) : null);
	output.setUsuarioId(input.getUsuario() != null ? input.getUsuario().getId() : null);
	output.setPublicadorId(input.getPublicador() != null ? input.getPublicador().getId() : null);
	output.setDataEdicao(input.getDataEdicao() != null ? Timestamp.valueOf(input.getDataEdicao()) : null);
	output.setAtivo(true);

	return output;
    }
}

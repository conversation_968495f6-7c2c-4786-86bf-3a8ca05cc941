package br.tur.reservafacil.dominio.aereo;

import br.tur.reservafacil.dominio.Produto;
import br.tur.reservafacil.dominio.aereo.interfaces.Identificavel;
import br.tur.reservafacil.dominio.tipo.FaixaEtaria;
import br.tur.reservafacil.dominio.tipo.Moeda;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
public class ConfiguracaoRcProduto
		implements Serializable, Identificavel<Integer> {

    private static final long serialVersionUID = -383186087228666625L;

    private Integer id;

    private Integer configuracaoRcId;

    private ConfiguracaoRc configuracaoRc;

    private Produto produto;

    private String codigoRc;

    private BigDecimal taxaBolsa = BigDecimal.ZERO;

    private BigDecimal taxaBolsaPerc = BigDecimal.ZERO;

    private BigDecimal taxaBolsaChd = BigDecimal.ZERO;

    private BigDecimal taxaBolsaPercChd = BigDecimal.ZERO;

    private BigDecimal taxaBolsaInf = BigDecimal.ZERO;

    private BigDecimal taxaBolsaPercInf = BigDecimal.ZERO;

    private Boolean ativo = Boolean.TRUE;

    private Moeda moedaRepasse = Moeda.BRL;

    public ConfiguracaoRcProduto() {
    }

    public ConfiguracaoRcProduto(ConfiguracaoRc configuracaoRc, Integer configuracaoRcId, Produto produto, String codigoRc, BigDecimal taxaBolsa,
        BigDecimal taxaBolsaPerc, BigDecimal taxaBolsaChd, BigDecimal taxaBolsaPercChd, BigDecimal taxaBolsaInf, BigDecimal taxaBolsaPercInf) {
        this.configuracaoRcId = configuracaoRcId;
        this.configuracaoRc = configuracaoRc;
        this.produto = produto;
        this.codigoRc = codigoRc;
        this.taxaBolsa = taxaBolsa;
        this.taxaBolsaPerc = taxaBolsaPerc;
        this.taxaBolsaChd = taxaBolsaChd;
        this.taxaBolsaPercChd = taxaBolsaPercChd;
        this.taxaBolsaInf = taxaBolsaInf;
        this.taxaBolsaPercInf = taxaBolsaPercInf;
    }


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getConfiguracaoRcId() {
        return configuracaoRcId;
    }

    public void setConfiguracaoRcId(Integer configuracaoRcId) {
        this.configuracaoRcId = configuracaoRcId;
    }

    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    public String getCodigoRc() {
        return codigoRc;
    }

    public void setCodigoRc(String codigoRc) {
        this.codigoRc = codigoRc;
    }

    public BigDecimal getTaxaBolsa() {
        return taxaBolsa;
    }

    public void setTaxaBolsa(BigDecimal taxaBolsa) {
        this.taxaBolsa = taxaBolsa;
    }

    public BigDecimal getTaxaBolsaPerc() {
        return taxaBolsaPerc;
    }

    public void setTaxaBolsaPerc(BigDecimal taxaBolsaPerc) {
        this.taxaBolsaPerc = taxaBolsaPerc;
    }

    public BigDecimal getTaxaBolsaChd() {
        return taxaBolsaChd;
    }

    public void setTaxaBolsaChd(BigDecimal taxaBolsaChd) {
        this.taxaBolsaChd = taxaBolsaChd;
    }

    public BigDecimal getTaxaBolsaPercChd() {
        return taxaBolsaPercChd;
    }

    public void setTaxaBolsaPercChd(BigDecimal taxaBolsaPercChd) {
        this.taxaBolsaPercChd = taxaBolsaPercChd;
    }

    public BigDecimal getTaxaBolsaInf() {
        return taxaBolsaInf;
    }

    public void setTaxaBolsaInf(BigDecimal taxaBolsaInf) {
        this.taxaBolsaInf = taxaBolsaInf;
    }

    public BigDecimal getTaxaBolsaPercInf() {
        return taxaBolsaPercInf;
    }

    public void setTaxaBolsaPercInf(BigDecimal taxaBolsaPercInf) {
        this.taxaBolsaPercInf = taxaBolsaPercInf;
    }

    public Boolean getAtivo() {
        return ativo;
    }

    public void setAtivo(Boolean ativo) {
        this.ativo = ativo;
    }

    public ConfiguracaoRc getConfiguracaoRc() {
        return configuracaoRc;
    }

    public void setConfiguracaoRc(ConfiguracaoRc configuracaoRc) {
        this.configuracaoRc = configuracaoRc;
    }

    public BigDecimal getTaxaBolsaPorFaixaEtaria(FaixaEtaria faixaEtaria){
        if(FaixaEtaria.ADT.equals(faixaEtaria)){
            return this.getTaxaBolsa();
        }
        if(FaixaEtaria.CHD.equals(faixaEtaria)){
            return this.getTaxaBolsaChd();
        }
        if(FaixaEtaria.INF.equals(faixaEtaria)){
            return this.getTaxaBolsaInf();
        }
        return BigDecimal.ZERO;
    }

    public BigDecimal getTaxaBolsaPercPorFaixaEtaria(FaixaEtaria faixaEtaria){
        if(FaixaEtaria.ADT.equals(faixaEtaria)){
            return this.getTaxaBolsaPerc();
        }
        if(FaixaEtaria.CHD.equals(faixaEtaria)){
            return this.getTaxaBolsaPercChd();
        }
        if(FaixaEtaria.INF.equals(faixaEtaria)){
            return this.getTaxaBolsaPercInf();
        }
        return BigDecimal.ZERO;
    }

    public boolean isTaxaBolsaFixa(FaixaEtaria faixaEtaria) {
        if (getTaxaBolsaPercPorFaixaEtaria(faixaEtaria) != null && getTaxaBolsaPercPorFaixaEtaria(faixaEtaria).compareTo(BigDecimal.ZERO) > 0) {
            return false;
        }
        return true;
    }

    public Moeda getMoedaRepasse() {
        return moedaRepasse;
    }

    public void setMoedaRepasse(Moeda moedaRepasse) {
        this.moedaRepasse = moedaRepasse;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        ConfiguracaoRcProduto that = (ConfiguracaoRcProduto)o;
        return Objects.equals(id, that.id) &&
                Objects.equals(configuracaoRcId, that.configuracaoRcId) &&
                Objects.equals(produto, that.produto) &&
                Objects.equals(codigoRc, that.codigoRc) &&
                Objects.equals(taxaBolsa, that.taxaBolsa);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, configuracaoRcId, produto, codigoRc, taxaBolsa);
    }
}

package br.tur.reservafacil.precificador.application.service.cache;

import br.tur.reservafacil.dominio.TipoConfigFee;
import br.tur.reservafacil.dominio.aereo.configuracaofee.ConfiguracaoFee;
import br.tur.reservafacil.dominio.tipo.NacInt;
import br.tur.reservafacil.dominio.tipo.TipoTarifaAcordo;
import br.tur.reservafacil.precificador.domain.repository.ConfiguracaoFeeRepository;
import com.newrelic.api.agent.Trace;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by fepelichero on 08/11/2018.
 */
@Component
public class ConfiguracaoFeeRedisCacheComponent {

    @Autowired
    private ConfiguracaoFeeRepository configuracaoFeeRepository;

    @Cacheable(cacheNames = "ConfiguracaoFeeService::findByEmpresaCiaProdutoTipoTarifaNacInt", cacheManager = "redisCacheManager")
    @Trace
    public List<ConfiguracaoFee> findByEmpresaCiaProdutoTipoTarifaNacInt(Integer empresaId, String codCia, String produto, TipoTarifaAcordo tipoTarifaAcordo,
                                                                         NacInt nacInt, Integer grupoId, TipoConfigFee tipoConfigFee, boolean isBuscaTeste, String timeToCache) {
        return configuracaoFeeRepository.findByEmpresaCiaProdutoTipoTarifaNacIntTipoConfig(empresaId, codCia, produto, tipoTarifaAcordo, nacInt, grupoId, tipoConfigFee, isBuscaTeste);
    }

    @Cacheable(cacheNames = "ConfiguracaoFeeService::findByEmpresaCiaProdutoTipoTarifaNacIntComRes", cacheManager = "redisCacheManager")
    @Trace
    public List<ConfiguracaoFee> findByEmpresaCiaProdutoTipoTarifaNacIntComRes(Integer empresaId, String codCia, String produto, TipoTarifaAcordo tipoTarifaAcordo,
                                                                               NacInt nacInt, Integer grupoId, TipoConfigFee tipoConfigFee, boolean isBuscaTeste, String timeToCache) {
        return configuracaoFeeRepository.findByEmpresaCiaProdutoTipoTarifaNacIntTipoConfigComRes(empresaId, codCia, produto, tipoTarifaAcordo, nacInt, grupoId, tipoConfigFee, isBuscaTeste);
    }

    @Cacheable(cacheNames = "ConfiguracaoFeeService::findById", cacheManager = "redisCacheManager")
    public ConfiguracaoFee findById(Integer configFeeId) {
        return configuracaoFeeRepository.findEagerByKey(configFeeId);
    }

    @Cacheable(cacheNames = "ConfiguracaoFeeService::findByEmpresa", cacheManager = "redisCacheManager")
    public List<ConfiguracaoFee> findByEmpresa(Integer empresaId, boolean isBuscaDeTeste) {
        return configuracaoFeeRepository.findByEmpresa(empresaId, isBuscaDeTeste);
    }
}

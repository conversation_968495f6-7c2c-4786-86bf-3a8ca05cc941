package br.tur.reservafacil.precificador.application.service;

import br.tur.reservafacil.dominio.Produto;
import br.tur.reservafacil.dominio.aereo.CiaAerea;
import br.tur.reservafacil.dominio.aereo.Restricao;
import br.tur.reservafacil.dominio.aereo.pagamento.CondicaoPagamento;
import br.tur.reservafacil.dominio.aereo.pagamento.OpcaoPagamento;
import br.tur.reservafacil.dominio.aereo.v0.to.common.OpcaoParcelamento;
import br.tur.reservafacil.dominio.aereo.v0.to.common.OpcoesParcelamentoResponse;
import br.tur.reservafacil.dominio.aereo.v0.to.common.OpcoesParcelamentoTesteResponse;
import br.tur.reservafacil.dominio.aereo.voosExcluidos.MotivoExclusaoVoo;
import br.tur.reservafacil.dominio.common.BandeiraCartao;
import br.tur.reservafacil.dominio.intermediario.v0.to.precificacao.Precificavel;
import br.tur.reservafacil.precificador.application.service.cache.CondicaoPagamentoRedisCacheComponent;
import br.tur.reservafacil.precificador.domain.component.parcelamento.CondicaoPagamentoValidatorComponent;
import br.tur.reservafacil.precificador.domain.repository.CondicaoPagamentoRepository;
import br.tur.reservafacil.precificador.infrastructure.security.service.AutenticacaoService;
import com.newrelic.api.agent.Trace;
import com.rits.cloning.Cloner;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service public class CondicaoPagamentoService
        extends AbstractService<CondicaoPagamento, Integer, CondicaoPagamentoRepository> {

    private final RestricaoService                    restricaoService;
    private final AutenticacaoService                 autenticacaoService;
    private final CondicaoPagamentoValidatorComponent condicaoPagamentoValidatorComponent;
    private final OpcaoPagamentoService               opcaoPagamentoService;
    private final CondicaoPagamentoRetriever          condicaoPagamentoRetriever;
    private final Cloner			      cloner;

    @Autowired public CondicaoPagamentoService(RestricaoService restricaoService,
            AutenticacaoService autenticacaoService,
            CondicaoPagamentoValidatorComponent condicaoPagamentoValidatorComponent,
            OpcaoPagamentoService opcaoPagamentoService,
            CondicaoPagamentoRepository condicaoPagamentoRepository,
            CondicaoPagamentoRetriever condicaoPagamentoRetriever,
            Cloner cloner) {
        this.restricaoService = restricaoService;
        this.autenticacaoService = autenticacaoService;
        this.condicaoPagamentoValidatorComponent = condicaoPagamentoValidatorComponent;
        this.opcaoPagamentoService = opcaoPagamentoService;
        this.setRepository(condicaoPagamentoRepository);
        this.condicaoPagamentoRetriever = condicaoPagamentoRetriever;
        this.cloner = cloner;
    }

    public List<Restricao> obterRestricoesPorCondicao(int condicaoPagamento) {
        return getRepository().obterRestricoesPorCondicao(condicaoPagamento);
    }

    public OpcoesParcelamentoResponse obtemOpcaoParcelamentoInterno(Precificavel precificavel) {
        OpcoesParcelamentoResponse parcelamentoResponse = new OpcoesParcelamentoResponse();

        List<Restricao> restricoesPrecificavel = restricaoService.getRestricoesByPrecificavel(precificavel);

        List<CondicaoPagamento> listaCondicoesPagamento =
                condicaoPagamentoRetriever.obterCondicoesPorPrecificavelEEmpresa(precificavel.getCiaAereaViagem(),
                        autenticacaoService.currentEmpresaId(),
                        precificavel.getProdutoPrecificacao(), false);

        Optional<CondicaoPagamento> condicaoPagamento = listaCondicoesPagamento.stream()
                                                                               .sorted(Comparator.comparing(CondicaoPagamento::getPrioridade))
                                                                               .peek(this::populaCondicaoPagamento)
                                                                               .filter(condicaoPagamentoPredicate -> condicaoPagamentoValidatorComponent.validar(condicaoPagamentoPredicate, restricoesPrecificavel, precificavel))
                                                                               .findFirst();

        if(condicaoPagamento.isPresent()) {
            parcelamentoResponse.setOpcoesPagamento(condicaoPagamento.get().getOpcoesPagamento());
            parcelamentoResponse.setOpcoesParcelamento(obterOpcoesPossiveis(precificavel, condicaoPagamento.get()));
        }
        return parcelamentoResponse;
    }

    public OpcoesParcelamentoResponse searchInstallmentsByPassenger(Precificavel precificavel) {
        List<Restricao> restricoesPrecificavel = restricaoService.getRestricoesByPrecificavel(precificavel);
        return searchInstallmentsByPassenger(precificavel, restricoesPrecificavel);
    }

    public OpcoesParcelamentoResponse searchInstallmentsByPassenger(Precificavel precificavel, List<Restricao> restricoesPrecificavel) {
        OpcoesParcelamentoResponse installmentsResponse = new OpcoesParcelamentoResponse();

        List<CondicaoPagamento> listaCondicoesPagamento =
                condicaoPagamentoRetriever.obterCondicoesPorPrecificavelEEmpresa(precificavel.getCiaAereaViagem(),
                        autenticacaoService.currentEmpresaId(),
                        precificavel.getProdutoPrecificacao(), false);

        Optional<CondicaoPagamento> condicaoPagamento = listaCondicoesPagamento.stream()
                                                                               .sorted(Comparator.comparing(CondicaoPagamento::getPrioridade))
                                                                               .peek(this::populaCondicaoPagamento)
                                                                               .filter(condicaoPagamentoPredicate -> condicaoPagamentoValidatorComponent.validaRestricoes(condicaoPagamentoPredicate, restricoesPrecificavel, precificavel))
                                                                               .findFirst();

        if(condicaoPagamento.isPresent()) {
            installmentsResponse.setOpcoesPagamento(condicaoPagamento.get().getOpcoesPagamento());
            installmentsResponse.setOpcoesParcelamento(this.searchPossibleOptions(precificavel, condicaoPagamento.get()));
        }

        return installmentsResponse;
    }

    public OpcoesParcelamentoTesteResponse testaOpcaoParcelamento(Precificavel precificavel) {
        OpcoesParcelamentoTesteResponse opcoesParcelamentoTesteResponse = new OpcoesParcelamentoTesteResponse();
        List<MotivoExclusaoVoo> motivosExclusaoVoos = new ArrayList<>();

        List<Restricao> restricoesPrecificavel = restricaoService.getRestricoesByPrecificavel(precificavel);

        List<CondicaoPagamento> listaCondicoesPagamento =
                condicaoPagamentoRetriever.obterCondicoesPorPrecificavelEEmpresa(precificavel.getCiaAereaViagem(),
                        autenticacaoService.currentEmpresaId(),
                        precificavel.getProdutoPrecificacao(), true);

        Optional<CondicaoPagamento> condicaoPagamento = listaCondicoesPagamento.stream()
                                                                               .sorted(Comparator.comparing(CondicaoPagamento::getPrioridade))
                                                                               .distinct()
                                                                               .peek(this::populaCondicaoPagamento)
                                                                               .filter(condicaoPagamentoPredicate -> {
                                                                                   List<MotivoExclusaoVoo> motivoExclusaoVoos = condicaoPagamentoValidatorComponent.validarMotivosExclusao(
                                                                                           condicaoPagamentoPredicate,
                                                                                           restricoesPrecificavel,
                                                                                           precificavel);
                                                                                   if (CollectionUtils.isNotEmpty(motivoExclusaoVoos)) {
                                                                                       motivoExclusaoVoos.stream().forEach(motivo -> {
                                                                                           if (motivosExclusaoVoos.stream().noneMatch(m -> m.getIdConfiguracaoPrecificacao().equals(motivo.getIdConfiguracaoPrecificacao()))) {
                                                                                               motivosExclusaoVoos.add(motivo);
                                                                                           }
                                                                                       });
                                                                                       return false;
                                                                                   }
                                                                                   return true;
                                                                               })
                                                                               .findFirst();

        opcoesParcelamentoTesteResponse.setMotivosExclusaoVoos(motivosExclusaoVoos);

        if(condicaoPagamento.isPresent()) {
            OpcoesParcelamentoResponse parcelamentoResponse = new OpcoesParcelamentoResponse();
            parcelamentoResponse.setOpcoesParcelamento(obterOpcoesPossiveis(precificavel, condicaoPagamento.get()));
            parcelamentoResponse.setOpcoesPagamento(condicaoPagamento.get().getOpcoesPagamento());
            opcoesParcelamentoTesteResponse.setOpcoesParcelamentoResponse(parcelamentoResponse);
        }
        return opcoesParcelamentoTesteResponse;
    }

    private void populaCondicaoPagamento(CondicaoPagamento condicaoPagamento) {
        condicaoPagamento.setOpcoesParcelamento(opcaoPagamentoService.obterOpcoesPorCondicao(condicaoPagamento.getId()));
        condicaoPagamento.setOpcoesPagamento(opcaoPagamentoService.obterOpcoesPagamentoPorCondicao(condicaoPagamento.getId()));
        condicaoPagamento.setRestricoes(this.obterRestricoesPorCondicao(condicaoPagamento.getId()));
    }

    private List<OpcaoParcelamento> obterOpcoesPossiveis(Precificavel precificavel, CondicaoPagamento condicaoPagamento) {
        return condicaoPagamento.getOpcoesParcelamento()
                                .stream()
                                .sorted(Comparator.comparing(OpcaoPagamento::getQuantidadeParcelas))
                                .flatMap(opcao -> opcao
                                        .obterBandeiras()
                                        .stream()
                                        .map(bandeira -> condicaoPagamentoValidatorComponent.calcularParcela(precificavel, opcao, condicaoPagamento, bandeira)))
                                .filter(Optional::isPresent)
                                .map(Optional::get)
                                .collect(Collectors.toList());
    }

    private List<OpcaoParcelamento> searchPossibleOptions(Precificavel precificavel, CondicaoPagamento paymentCondition) {
        return paymentCondition.getOpcoesParcelamento()
                               .stream()
                               .sorted(Comparator.comparing(OpcaoPagamento::getQuantidadeParcelas))
                               .flatMap(option -> option
                                       .obterBandeiras()
                                       .stream()
                                       .map(searchInstallmentsFunction(precificavel, paymentCondition, option)))
                               .flatMap(List::stream)
                               .filter(Optional::isPresent)
                               .map(Optional::get)
                               .collect(Collectors.toList());
    }

    private Function<BandeiraCartao, List<Optional<OpcaoParcelamento>>> searchInstallmentsFunction(Precificavel precificavel, CondicaoPagamento paymentCondition, OpcaoPagamento option) {
        return flag -> precificavel.getTarifacoes()
                                   .stream()
                                   .map(fare -> {
                                       Optional<OpcaoParcelamento> opcaoParcelamento = condicaoPagamentoValidatorComponent.calculateInstallmentByPassenger(precificavel, fare, option, paymentCondition, flag);
                                       return IntStream.range(0, fare.getQuantidade()).filter(i -> opcaoParcelamento.isPresent())
                                                       .mapToObj(i -> {
                                                           OpcaoParcelamento installment = cloner.deepClone(opcaoParcelamento.get());
                                                           installment.setPassengerId(installment.getPassengerId() + (i+1));
                                                           return Optional.of(installment);
                                                       }).collect(Collectors.toList());
                                   })
                                   .flatMap(Collection::stream)
                                   .collect(Collectors.toList());
    }

    @Trace
    public List<CondicaoPagamento> findByEmpresa(Integer empresaId, boolean buscaDeTeste) {
        return condicaoPagamentoRetriever.findByEmpresa(empresaId, buscaDeTeste);
    }

    public OpcoesParcelamentoResponse obtemOpcaoParcelamentoInterno(List<CondicaoPagamento> configsInstallments, Precificavel precificavel) {
        OpcoesParcelamentoResponse parcelamentoResponse = new OpcoesParcelamentoResponse();

        List<Restricao> restricoesPrecificavel = restricaoService.getRestricoesByPrecificavel(precificavel);

        Optional<CondicaoPagamento> condicaoPagamento = configsInstallments.stream()
                                                                           .filter(c -> CollectionUtils.isEmpty(c.getProdutos()) || (CollectionUtils.isNotEmpty(c.getProdutos()) && c.getProdutos().stream().map(Produto::getNome).collect(Collectors.toList()).contains(precificavel.getProdutoPrecificacao())))
                                                                           .filter(c -> CollectionUtils.isNotEmpty(c.getCiasAereas()) && c.getCiasAereas().stream().map(CiaAerea::getCodigo).collect(Collectors.toList()).contains(precificavel.getCiaAereaViagem()))
                                                                           .filter(condicaoPagamentoPredicate -> condicaoPagamentoValidatorComponent.validar(condicaoPagamentoPredicate, restricoesPrecificavel, precificavel))
                                                                           .findFirst();

        if(condicaoPagamento.isPresent()) {
            parcelamentoResponse.setOpcoesPagamento(condicaoPagamento.get().getOpcoesPagamento());
            parcelamentoResponse.setOpcoesParcelamento(obterOpcoesPossiveis(precificavel, condicaoPagamento.get()));
        }
        return parcelamentoResponse;
    }

    /**
     *
     * FIXME: Existe apenas por causa que utilizando apenas Spring AOP, sem AspectJ, o @Cacheable não funciona quando chamando métodos da
     * própria classe. Como a idéia é eventualmente migrar para compile-time weaving com AspectJ, essa classe deve sumir e a classe voltar
     * ao que era antes.
     *
     */
    @Component
    static class CondicaoPagamentoRetriever {

        @Autowired
        private CondicaoPagamentoRedisCacheComponent condicaoPagamentoRedisCacheComponent;

        @Cacheable(cacheNames = "CondicaoPagamentoService::obterCondicoesPorPrecificavelEEmpresa", cacheManager = "ehCacheManager")
        public List<CondicaoPagamento> obterCondicoesPorPrecificavelEEmpresa(String ciaAerea, long empresaId, String produto, Boolean isBuscaDeTeste) {
            return condicaoPagamentoRedisCacheComponent.obterCondicoesPorPrecificavelEEmpresa(ciaAerea, empresaId, produto, isBuscaDeTeste);
        }

        @Cacheable(cacheNames = "CondicaoPagamentoService::findByEmpresa", cacheManager = "ehCacheManager")
        public List<CondicaoPagamento> findByEmpresa(Integer empresaId, boolean buscaDeTeste) {
            return condicaoPagamentoRedisCacheComponent.findByEmpresa(empresaId, buscaDeTeste);
        }
    }
}

package br.tur.reservafacil.dominio.aereo;

import br.tur.reservafacil.dominio.Agencia;
import br.tur.reservafacil.dominio.aereo.interfaces.Identificavel;

import java.io.Serializable;
import java.util.Objects;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
public class ConfiguracaoRcAgencia
		implements Serializable, Identificavel<Integer> {

    private static final long serialVersionUID = 7365494099879634795L;

    private Integer id;

    private ConfiguracaoRc configuracaoRc;

    private Agencia agencia;

    private Boolean ativo = Boolean.TRUE;

    public ConfiguracaoRcAgencia() {
    }

    public ConfiguracaoRcAgencia(Integer id, ConfiguracaoRc configuracaoRc, Agencia agencia) {
	this.id = id;
	this.configuracaoRc = configuracaoRc;
	this.agencia = agencia;
    }

    public Integer getId() {
	return id;
    }

    public void setId(Integer id) {
	this.id = id;
    }

    public ConfiguracaoRc getConfiguracaoRc() {
	return configuracaoRc;
    }

    public void setConfiguracaoRc(ConfiguracaoRc configuracaoRc) {
	this.configuracaoRc = configuracaoRc;
    }

    public Agencia getAgencia() {
	return agencia;
    }

    public void setAgencia(Agencia agencia) {
	this.agencia = agencia;
    }

    public Boolean getAtivo() {
	return ativo;
    }

    public void setAtivo(Boolean ativo) {
	this.ativo = ativo;
    }

    @Override
    public String toString() {
	return "ConfiguracaoRcAgencia{" +
			"id=" + id +
			", configuracaoRc=" + configuracaoRc +
			", agencia=" + agencia +
			'}';
    }

    @Override
    public boolean equals(Object o) {
	if (this == o)
	    return true;
	if (o == null || getClass() != o.getClass())
	    return false;
	ConfiguracaoRcAgencia that = (ConfiguracaoRcAgencia)o;
	return Objects.equals(id, that.id) &&
			Objects.equals(configuracaoRc, that.configuracaoRc) &&
			Objects.equals(agencia, that.agencia);
    }

    @Override
    public int hashCode() {
	return Objects.hash(id, configuracaoRc, agencia);
    }
}

package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.configuracaoRc.agencia;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoRcAgenciaRecord;
import br.tur.reservafacil.dominio.Agencia;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRc;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcAgencia;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.CustomMapper;
import org.springframework.stereotype.Component;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
@Component
public class ConfiguracaoRcAgencia2ConfiguracaoRcAgenciaRecordMapper
		implements CustomMapper<ConfiguracaoRcAgencia, ConfiguracaoRcAgenciaRecord> {

    @Override
    public ConfiguracaoRcAgenciaRecord map(ConfiguracaoRcAgencia input, ConfiguracaoRcAgenciaRecord output) {
	if (input.getId() != null) {
	    output.setId(input.getId());
	}
	output.setConfiguracaoRcId(this.getConfiguracaoRcId(input.getConfiguracaoRc()));
	output.setAgenciaId(this.getAgenciaId(input.getAgencia()));
        if (input.getAtivo() != null) {
	    output.setAtivo(input.getAtivo());
        } else {
	    output.setAtivo(null);
        }
	return output;
    }

    private Integer getConfiguracaoRcId(ConfiguracaoRc configuracaoRc) {
	return configuracaoRc != null ? configuracaoRc.getId() : null;
    }

    private Integer getAgenciaId(Agencia agencia) {
	return agencia != null ? agencia.getId() : null;
    }

}

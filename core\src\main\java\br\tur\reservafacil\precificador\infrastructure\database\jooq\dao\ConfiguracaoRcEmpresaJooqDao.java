package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoRcEmpresaRecord;
import br.tur.reservafacil.dominio.Empresa;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcEmpresa;
import br.tur.reservafacil.precificador.domain.repository.ConfiguracaoRcEmpresaRepository;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.EmpresaRecord2EmpresaMapper;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.Tables.CONFIGURACAO_RC_EMPRESA;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.Tables.EMPRESA;

/**
 * Created by <PERSON><PERSON> on 20/08/25 - GA-12251.
 */
@Repository
public class ConfiguracaoRcEmpresaJooqDao
		extends AbstractJooqDao<ConfiguracaoRcEmpresaRecord, ConfiguracaoRcEmpresa, Integer> implements
        ConfiguracaoRcEmpresaRepository {

    @Autowired
    private EmpresaRecord2EmpresaMapper empresaMapper;

    @Autowired
    protected ConfiguracaoRcEmpresaJooqDao(Configuration configuration, DSLContext context) {
	super(CONFIGURACAO_RC_EMPRESA, ConfiguracaoRcEmpresa.class, configuration, context);
    }

    @Override
    protected Integer getId(ConfiguracaoRcEmpresa bean) {
	return bean.getId();
    }

    @Override
    public void deleteByConfigPrecificacaoId(Integer configuracaoPrecificacaoId) {
	this.getContext().deleteFrom(CONFIGURACAO_RC_EMPRESA).where(CONFIGURACAO_RC_EMPRESA.CONFIGURACAO_RC_ID.eq(configuracaoPrecificacaoId)).execute();
    }

    @Override
    public List<Empresa> findEmpresasByConfiguracaoId(Integer configuracaoId) {
        return this.getContext().select(EMPRESA.fields())
                .from(EMPRESA)
                .join(CONFIGURACAO_RC_EMPRESA)
                .on(EMPRESA.ID.eq(CONFIGURACAO_RC_EMPRESA.EMPRESA_ID))
                .where(CONFIGURACAO_RC_EMPRESA.CONFIGURACAO_RC_ID.eq(configuracaoId))
                .fetch().map(record -> this.empresaMapper.map(record.into(EMPRESA)));
    }
}

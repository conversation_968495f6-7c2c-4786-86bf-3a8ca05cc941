package br.tur.reservafacil.precificador.domain.repository;

import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcRestricao;
import br.tur.reservafacil.dominio.aereo.Restricao;

import java.util.List;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
public interface ConfiguracaoRcRestricaoRepository
		extends Repository<ConfiguracaoRcRestricao, Integer> {

    void deleteByConfigPrecificacaoId(Integer idConfigPrecificacao);

    void deleteByRestricaoList(List<Restricao> restricoes);

}

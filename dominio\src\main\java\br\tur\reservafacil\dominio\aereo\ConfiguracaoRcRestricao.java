package br.tur.reservafacil.dominio.aereo;

import br.tur.reservafacil.dominio.aereo.interfaces.AssociacaoRestricao;
import br.tur.reservafacil.dominio.aereo.interfaces.Identificavel;

import java.io.Serializable;
import java.util.Objects;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
public class ConfiguracaoRcRestricao
		implements Serializable, Identificavel<Integer>, AssociacaoRestricao<Integer, ConfiguracaoRc> {

    private static final long serialVersionUID = -3610134602002050773L;

    private Integer id;

    private ConfiguracaoRc restringivel;

    private Restricao restricao;

    public ConfiguracaoRcRestricao() {
    }

    public ConfiguracaoRcRestricao(ConfiguracaoRc restringivel, Restricao restricao) {
	this.restringivel = restringivel;
	this.restricao = restricao;
    }

    public Integer getId() {
	return id;
    }

    public void setId(Integer id) {
	this.id = id;
    }

    public ConfiguracaoRc getRestringivel() {
	return restringivel;
    }

    public void setRestringivel(ConfiguracaoRc restringivel) {
	this.restringivel = restringivel;
    }

    public Restricao getRestricao() {
	return restricao;
    }

    public void setRestricao(Restricao restricao) {
	this.restricao = restricao;
    }

    @Override
    public boolean equals(Object o) {
	if (this == o)
	    return true;
	if (o == null || getClass() != o.getClass())
	    return false;
	ConfiguracaoRcRestricao that = (ConfiguracaoRcRestricao)o;
	return Objects.equals(id, that.id) &&
			Objects.equals(restringivel, that.restringivel) &&
			Objects.equals(restricao, that.restricao);
    }

    @Override
    public int hashCode() {
	return Objects.hash(id, restringivel, restricao);
    }
}

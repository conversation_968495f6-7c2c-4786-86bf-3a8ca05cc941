package br.tur.reservafacil.precificador.domain.repository;

import br.tur.reservafacil.dominio.Empresa;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRc;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcEmpresa;

import java.util.List;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
public interface ConfiguracaoRcEmpresaRepository
		extends Repository<ConfiguracaoRcEmpresa, Integer> {

    void deleteByConfigPrecificacaoId(Integer configuracaoPrecificacaoId);

    List<Empresa> findEmpresasByConfiguracaoId(Integer configuracaoId);
}

package br.tur.reservafacil.dominio.aereo;

import br.tur.reservafacil.dominio.ApplicationUser;
import br.tur.reservafacil.dominio.aereo.interfaces.Identificavel;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
public class HistoricoConfiguracaoRc
		implements Serializable, Identificavel<Integer> {

    private static final long serialVersionUID = -9066187928367015616L;

    private Integer id;

    private ConfiguracaoRc configuracaoRc;

    private Integer idOrigem;

    private LocalDateTime dataAtualizacao = LocalDateTime.now();

    private LocalDateTime dataEdicao = LocalDateTime.now();

    private ApplicationUser usuario;

    private ApplicationUser publicador;

    public Integer getId() {
	return id;
    }

    public void setId(Integer id) {
	this.id = id;
    }

    public ConfiguracaoRc getConfiguracaoRc() {
	return configuracaoRc;
    }

    public void setConfiguracaoRc(ConfiguracaoRc configuracaoRc) {
	this.configuracaoRc = configuracaoRc;
    }

    public Integer getIdOrigem() {
	return idOrigem;
    }

    public void setIdOrigem(Integer idOrigem) {
	this.idOrigem = idOrigem;
    }

    public LocalDateTime getDataAtualizacao() {
	return dataAtualizacao;
    }

    public void setDataAtualizacao(LocalDateTime dataAtualizacao) {
	this.dataAtualizacao = dataAtualizacao;
    }

    public ApplicationUser getUsuario() {
	return usuario;
    }

    public void setUsuario(ApplicationUser usuario) {
	this.usuario = usuario;
    }

    public LocalDateTime getDataEdicao() {
	return dataEdicao;
    }

    public void setDataEdicao(LocalDateTime dataEdicao) {
	this.dataEdicao = dataEdicao;
    }

    public ApplicationUser getPublicador() {
	return publicador;
    }

    public void setPublicador(ApplicationUser publicador) {
	this.publicador = publicador;
    }

    @Override
    public boolean equals(Object o) {
	if (this == o)
	    return true;
	if (o == null || getClass() != o.getClass())
	    return false;
	HistoricoConfiguracaoRc that = (HistoricoConfiguracaoRc)o;
	return Objects.equals(id, that.id) &&
			Objects.equals(configuracaoRc, that.configuracaoRc) &&
			Objects.equals(idOrigem, that.idOrigem) &&
			Objects.equals(dataAtualizacao, that.dataAtualizacao) &&
			Objects.equals(dataEdicao, that.dataEdicao) &&
			Objects.equals(usuario, that.usuario) &&
			Objects.equals(publicador, that.publicador);
    }

    @Override
    public int hashCode() {
	return Objects.hash(id, configuracaoRc, idOrigem, dataAtualizacao, usuario, dataEdicao, publicador);
    }

    @Override
    public String toString() {
	return "HistoricoConfiguracaoRc{" +
			"id=" + id +
			", configuracaoRc=" + configuracaoRc +
			", dataAtualizacao=" + dataAtualizacao +
			", usuario=" + usuario +
			", publicador=" + publicador +
			", dataEdicao=" + dataEdicao +
			'}';
    }
}

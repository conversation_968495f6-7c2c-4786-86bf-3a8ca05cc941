package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.Tables;
import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoRavRecord;
import br.tur.reservafacil.dominio.Empresa;
import br.tur.reservafacil.dominio.Produto;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRav;
import br.tur.reservafacil.dominio.tipo.NacInt;
import br.tur.reservafacil.dominio.tipo.TipoTarifaAcordo;
import br.tur.reservafacil.precificador.domain.repository.ConfiguracaoRavRepository;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.ConfiguracaoRavEagerMapper;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.EmpresaRecord2EmpresaMapper;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.ProdutoRecord2ProdutoMapper;
import org.jooq.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.*;

import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.CiaAerea.CIA_AEREA;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.ConfiguracaoRav.CONFIGURACAO_RAV;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.ConfiguracaoRavCiaAerea.CONFIGURACAO_RAV_CIA_AEREA;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.ConfiguracaoRavEmpresa.CONFIGURACAO_RAV_EMPRESA;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.ConfiguracaoRavProduto.CONFIGURACAO_RAV_PRODUTO;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.Empresa.EMPRESA;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.Produto.PRODUTO;

/**
 * Created by davidson on 6/30/16.
 */
@Repository
public class ConfiguracaoRavJooqDao extends AbstractJooqDao<ConfiguracaoRavRecord, ConfiguracaoRav, Integer> implements ConfiguracaoRavRepository {

    private static final String PARAM_NOME = "nome";
    private static final String PARAM_EMPRESA = "empresa";
    private static final String DEFAULT_FIELD_ORDER_BY = "prioridade";
    private static final String PARAM_CIA = "ciaAerea";
    private static final String PARAM_ATIVO = "ativo";

    @Autowired
    private ConfiguracaoRavEagerMapper mapper;

    @Autowired
    private EmpresaRecord2EmpresaMapper empresaMapper;

    @Autowired
    private ProdutoRecord2ProdutoMapper produtoMapper;

    @Autowired
    public ConfiguracaoRavJooqDao(Configuration configuration, DSLContext context) {
        super(CONFIGURACAO_RAV, ConfiguracaoRav.class, configuration, context);
    }

    @Override
    protected Integer getId(ConfiguracaoRav bean) {
        return bean.getId();
    }

    @Override
    public ConfiguracaoRav findByEmpresaCiaProdutoTipoTarifaNacIntGrupo(Integer empresaId, String codCia, String produto, TipoTarifaAcordo tipoTarifa,
                                                                        NacInt nacInt, Integer grupoId) {
        String sql =
                "SELECT " +
                        "cr.id, cr.nome, cr.valor_minimo, cr.fator_rav, cr.empresa_id, cr.percentual_rav, cr.tipo_tarifa, cr.ativo, cr.nac_int, " +
                        "cr.status_publicacao, cr.id_origem, cr.editavel, cr.versao, cr.tipo_config, cr.prioridade, " +
                        "crcia.id, crcia.configuracao_rav_id, crcia.cia_aerea_id, crcia.ativo, crcia.valor_minimo, crcia.fator_rav, " +
                        "crcia.percentual_rav, crcia.moeda, crcia.variavel, crcia.nac_int " +
                        "FROM configuracao_rav cr " +
                        "JOIN configuracao_rav_cia_aerea crcia ON cr.id = crcia.configuracao_rav_id " +
                        "JOIN cia_aerea cia ON crcia.cia_aerea_id = cia.id " +
                        "LEFT JOIN configuracao_rav_grupo crg ON cr.id = crg.configuracao_rav_id " +
                        "WHERE cr.ativo = true " +
                        this.tipoTarifaCondition(tipoTarifa) +
                        this.nacIntCondition(nacInt) +
                        "AND cr.status_publicacao = 'PUBLICADA' " +
                        "AND cia.codigo = ? " +
                        "AND EXISTS ( " +
                        "SELECT '1' " +
                        "FROM configuracao_rav_empresa cre " +
                        "JOIN configuracao_rav_produto crp ON crp.configuracao_rav_id = cre.configuracao_rav_id " +
                        "JOIN produto p ON crp.produto_id = p.id " +
                        "WHERE cre.empresa_id = ? AND p.nome = ? " +
                        "AND cre.configuracao_rav_id = cr.id " +
                        "LIMIT 1 " +
                        ") " +
                        "AND (crg.grupo_id = ? OR cr.tipo_config = 'PADRAO') " +
                        "ORDER BY cr.tipo_config ASC, crg.grupo_id ASC, cr.prioridade ASC " +
                        "LIMIT 1";

        Result<Record> result = this.getContext().fetch(sql, codCia, empresaId, produto, grupoId);

        final List<ConfiguracaoRav> configs = this.mapper.map(result);
        return configs.isEmpty() ? null : configs.get(0);
    }

    private String tipoTarifaCondition(TipoTarifaAcordo tipoTarifaAcordo) {
        switch (tipoTarifaAcordo) {
            case PRIVADA:
                return "AND cr.tipo_tarifa <> 'PUBLICA' ";
            case PUBLICA:
                return "AND cr.tipo_tarifa <> 'PRIVADA' ";
            default:
                return "AND cr.tipo_tarifa = 'AMBAS' ";
        }
    }

    private String nacIntCondition(NacInt nacInt) {
        switch (nacInt) {
            case INT:
                return "AND crcia.nac_int <> 'NAC' ";
            case NAC:
                return "AND crcia.nac_int <> 'INT' ";
            default:
                return "AND crcia.nac_int = 'AMBOS' ";
        }
    }

    @Override
    public ConfiguracaoRav findEagerByKey(Integer id) {
        Result<Record> result = this.getContext().select()
                .from(CONFIGURACAO_RAV)
                .leftJoin(CONFIGURACAO_RAV_CIA_AEREA)
                .on(CONFIGURACAO_RAV.ID.eq(CONFIGURACAO_RAV_CIA_AEREA.CONFIGURACAO_RAV_ID))
                .leftJoin(CIA_AEREA)
                .on(CIA_AEREA.ID.eq(CONFIGURACAO_RAV_CIA_AEREA.CIA_AEREA_ID))
                .where(CONFIGURACAO_RAV.ID.eq(id))
                .fetch();
        final List<ConfiguracaoRav> configs = this.mapper.map(result);
        if (configs.size() > 1) {
            throw new RuntimeException("Foi encontrado mais de um registro de configuração de Rav. A base pode estar inconsistente!");
        }
        final ConfiguracaoRav configuracaoRav = configs.isEmpty() ? null : configs.get(0);
        Optional.ofNullable(configuracaoRav).ifPresent(this::carregaDadosConfiguracaoRav);
        return configuracaoRav;
    }

    private void carregaDadosConfiguracaoRav(ConfiguracaoRav configuracaoRav) {
        configuracaoRav.setEmpresas(this.getEmpresasByIdConfiguracaoRav(configuracaoRav.getId()));
        configuracaoRav.setProdutos(this.getProdutosByIdConfiguracaoRav(configuracaoRav.getId()));
    }

    private List<Produto> getProdutosByIdConfiguracaoRav(Integer id) {
        return this.getContext().select(PRODUTO.fields())
                .from(CONFIGURACAO_RAV_PRODUTO)
                .join(PRODUTO)
                .on(CONFIGURACAO_RAV_PRODUTO.PRODUTO_ID.eq(PRODUTO.ID))
                .where(CONFIGURACAO_RAV_PRODUTO.CONFIGURACAO_RAV_ID.eq(id))
                .fetch()
                .map(record -> this.produtoMapper.map(record.into(PRODUTO)));
    }

    private List<Empresa> getEmpresasByIdConfiguracaoRav(Integer id) {
        return this.getContext().select(EMPRESA.fields())
                .from(CONFIGURACAO_RAV_EMPRESA)
                .join(EMPRESA)
                .on(CONFIGURACAO_RAV_EMPRESA.EMPRESA_ID.eq(EMPRESA.ID))
                .where(CONFIGURACAO_RAV_EMPRESA.CONFIGURACAO_RAV_ID.eq(id))
                .fetch()
                .map(record -> this.empresaMapper.map(record.into(EMPRESA)));
    }

    @Override
    public List<ConfiguracaoRav> findByParams(int page, Integer pageSize, String orderBy, Map<String, String> params) {
        SelectJoinStep<Record> selectJoinStep = this.getContext().select().from(CONFIGURACAO_RAV);

        Result<Record> result;
        if (params != null) {
            SelectConditionStep<Record> queryByParams = createQueryByParameters(params, selectJoinStep);
            Field<?> fieldOrderBy = this.getFieldOrderBy(orderBy);
            if (fieldOrderBy != null) {
                queryByParams.orderBy(fieldOrderBy);
            }
            result = queryByParams.limit(pageSize).offset((page - 1) * pageSize).fetch();
        } else {
            result = selectJoinStep.fetch();
        }
        List<ConfiguracaoRav> configuracaoRavs = this.mapper.map(result);
        configuracaoRavs.stream().forEach(this::carregaDadosConfiguracaoRav);
        return configuracaoRavs;
    }

    private void fazJoinsSeNecessario(SelectJoinStep<Record> selectJoinStep, Map<String, String> params) {
        if (params.containsKey(PARAM_EMPRESA)) {
            selectJoinStep.join(CONFIGURACAO_RAV_EMPRESA)
                    .on(CONFIGURACAO_RAV.ID.eq(CONFIGURACAO_RAV_EMPRESA.CONFIGURACAO_RAV_ID))
                    .join(EMPRESA)
                    .on(EMPRESA.ID.eq(CONFIGURACAO_RAV_EMPRESA.EMPRESA_ID));
        }
        if (params.containsKey(PARAM_CIA)) {
            selectJoinStep.join(CONFIGURACAO_RAV_CIA_AEREA)
                    .on(CONFIGURACAO_RAV.ID.eq(CONFIGURACAO_RAV_CIA_AEREA.CONFIGURACAO_RAV_ID))
                    .innerJoin(CIA_AEREA)
                    .on(CIA_AEREA.ID.eq(CONFIGURACAO_RAV_CIA_AEREA.CIA_AEREA_ID));
        }
    }

    @Override
    public Integer countByParameters(Map<String, String> params) {
        SelectOnConditionStep<Record> selectOnConditionStep = this.getContext().select((Collections.singletonList(CONFIGURACAO_RAV.ID.count()))).from(CONFIGURACAO_RAV)
                .innerJoin(CONFIGURACAO_RAV_CIA_AEREA)
                .on(CONFIGURACAO_RAV.ID.eq(CONFIGURACAO_RAV_CIA_AEREA.CONFIGURACAO_RAV_ID))
                .innerJoin(CIA_AEREA)
                .on(CIA_AEREA.ID.eq(CONFIGURACAO_RAV_CIA_AEREA.CIA_AEREA_ID));
        return this.createQueryByParameters(params, selectOnConditionStep).fetchOne(0, Integer.class);
    }

    @Override
    public List<ConfiguracaoRav> findByEmpresa(Integer empresaId, Integer grupoId, boolean isBuscaDeTeste) {
//        String sql =
//                "SELECT " +
//                        "cr.id, cr.nome, cr.valor_minimo, cr.fator_rav, cr.empresa_id, cr.percentual_rav, cr.tipo_tarifa, cr.ativo, cr.nac_int, " +
//                        "cr.status_publicacao, cr.id_origem, cr.editavel, cr.versao, cr.tipo_config, cr.prioridade, " +
//                        "crcia.id, crcia.configuracao_rav_id, crcia.cia_aerea_id, cia.codigo, crcia.ativo, crcia.valor_minimo, crcia.fator_rav, " +
//                        "crcia.percentual_rav, crcia.moeda, crcia.variavel, crcia.nac_int, p.id, p.nome, crg.grupo_id, g.* " +
//                        "FROM configuracao_rav cr " +
//                        "JOIN configuracao_rav_cia_aerea crcia ON cr.id = crcia.configuracao_rav_id " +
//                        "JOIN cia_aerea cia ON crcia.cia_aerea_id = cia.id " +
//                        "JOIN configuracao_rav_produto crp ON crp.configuracao_rav_id = cr.id " +
//                        "JOIN produto p ON crp.produto_id = p.id " +
//                        "LEFT JOIN configuracao_rav_grupo crg ON cr.id = crg.configuracao_rav_id " +
//                        "LEFT JOIN grupo g ON g.id = crg.grupo_id " +
//                        "WHERE cr.ativo = true " +
//                        "AND cr.status_publicacao = 'PUBLICADA' " +
//                        "AND EXISTS ( " +
//                        "SELECT '1' " +
//                        "FROM configuracao_rav_empresa cre " +
//                        "WHERE cre.empresa_id = ? " +
//                        "AND cre.configuracao_rav_id = cr.id " +
//                        "LIMIT 1 " +
//                        ") " +
//                        "AND (crg.grupo_id = ? OR cr.tipo_config = 'PADRAO') " +
//                        "ORDER BY cr.tipo_config ASC, crg.grupo_id ASC, cr.prioridade ASC ";

//        Result<Record> result = this.getContext().fetch(sql, empresaId, grupoId);

//        return this.mapper.map(result);
        Result<Record> result = this.getContext()
                .select()
                .from(CONFIGURACAO_RAV)
                .innerJoin(CONFIGURACAO_RAV_CIA_AEREA).on(CONFIGURACAO_RAV_CIA_AEREA.CONFIGURACAO_RAV_ID.eq(CONFIGURACAO_RAV.ID))
                .innerJoin(CIA_AEREA).on(CIA_AEREA.ID.eq(CONFIGURACAO_RAV_CIA_AEREA.CIA_AEREA_ID))
                .innerJoin(CONFIGURACAO_RAV_PRODUTO).on(CONFIGURACAO_RAV_PRODUTO.CONFIGURACAO_RAV_ID.eq(CONFIGURACAO_RAV.ID))
                .innerJoin(PRODUTO).on(PRODUTO.ID.eq(CONFIGURACAO_RAV_PRODUTO.PRODUTO_ID))
                .leftJoin(Tables.CONFIGURACAO_RAV_GRUPO).on(Tables.CONFIGURACAO_RAV_GRUPO.CONFIGURACAO_RAV_ID.eq(CONFIGURACAO_RAV.ID))
                .leftJoin(Tables.GRUPO).on(Tables.GRUPO.ID.eq(Tables.CONFIGURACAO_RAV_GRUPO.GRUPO_ID))
                .where(CONFIGURACAO_RAV.ATIVO.isTrue())
                .and(CONFIGURACAO_RAV.STATUS_PUBLICACAO.eq("PUBLICADA"))
                .andExists(this.getContext()
                        .selectOne()
                        .from(CONFIGURACAO_RAV_EMPRESA)
                        .where(CONFIGURACAO_RAV_EMPRESA.EMPRESA_ID.eq(empresaId))
                        .and(CONFIGURACAO_RAV_EMPRESA.CONFIGURACAO_RAV_ID.eq(CONFIGURACAO_RAV.ID))
                        .limit(1)
                )
                .and(Tables.CONFIGURACAO_RAV_GRUPO.GRUPO_ID.eq(grupoId).or(CONFIGURACAO_RAV.TIPO_CONFIG.eq("PADRAO")))
                .orderBy(CONFIGURACAO_RAV.TIPO_CONFIG, Tables.CONFIGURACAO_RAV_GRUPO.GRUPO_ID, CONFIGURACAO_RAV.PRIORIDADE)
                .fetch();
        return this.mapper.map(result);
    }

    private SelectConditionStep<Record> createQueryByParameters(Map<String, String> params, SelectJoinStep<Record> selectOnCondition) {
        this.fazJoinsSeNecessario(selectOnCondition, params);
        List<Condition> conditionsByParameters = this.createConditionsByParameters(params);
        return selectOnCondition.where(conditionsByParameters);
    }

    private List<Condition> createConditionsByParameters(Map<String, String> params) {
        List<Condition> conditions = new ArrayList<>();
        if (params.containsKey(PARAM_EMPRESA)) {
            conditions.add(EMPRESA.NOME.likeIgnoreCase(params.get(PARAM_EMPRESA)));
        }
        if (params.containsKey(PARAM_NOME)) {
            conditions.add(CONFIGURACAO_RAV.NOME.likeIgnoreCase(params.get(PARAM_NOME)));
        }
        if (params.containsKey(PARAM_CIA)) {
            conditions.add(CIA_AEREA.CODIGO.likeIgnoreCase(params.get(PARAM_CIA)));
        }
        if (params.containsKey(PARAM_ATIVO)) {
            conditions.add(CONFIGURACAO_RAV.ATIVO.eq(Boolean.valueOf(params.get(PARAM_ATIVO))));
        } else {
            conditions.add(CONFIGURACAO_RAV.ATIVO.isTrue());
        }

        return conditions;
    }

    private Field<?> getFieldOrderBy(String orderBy) {
        if (StringUtils.isEmpty(orderBy)) {
            orderBy = DEFAULT_FIELD_ORDER_BY;
        }
        return CONFIGURACAO_RAV.field(orderBy);
    }
}

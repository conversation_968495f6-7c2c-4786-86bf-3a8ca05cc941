package br.tur.reservafacil.dominio.tipo;

/**
 * Created by ramon on 12/04/16.
 */
public enum TipoValor {
    MARKUP(TipoTaxaSobretaxa.SOBRETAXA, false, "MK<PERSON>", "<PERSON>up Busca"),
    DU_ZERADA(TipoTaxaSobretaxa.SUBSTITUICAO, false, "DU_ZERADA", "Du Zerada"), //Quando a DU na precificacao é marcada como zerada, é guardado o valor da DU nesse TipoValor
    MARKUP_FORNECEDOR(TipoTaxaSobretaxa.DETALHETARIFA, false, "MKP_FORNECEDOR", "Markup Fornecedor"),
    TAXA_BOLSA_FORNECEDOR(TipoTaxaSobretaxa.DETALHETARIFA, false, "TAXA_BOLSA_FORNECEDOR", "TaxaBolsa/RC Fornecedor"),
    RAV(TipoTaxaSobretaxa.SOBRETAXA, false, "RAV", "Rav"),
    RC(TipoTaxaSobretaxa.SOBRETAXA, false, "RC", "Rc"),
    DU(TipoTaxaSobretaxa.TAXA, false, "DU", "Du"),
    OB_FEE(TipoTaxaSobretaxa.TAXA, false, "OBF", "Taxa Cartão de Crédito"),
    TAXAEMBARQUE(TipoTaxaSobretaxa.TAXA, false, "TE", "Taxa de Embarque"),
    COMISSAO_FORNECEDOR(TipoTaxaSobretaxa.DETALHETARIFA, false, "COM", "Comissão Fornecedor"),
    PORCENTAGEM_COMISSAO_FORNECEDOR(TipoTaxaSobretaxa.DETALHETARIFA, false, "COP", "Porcentagem Comissão Fornecedor", true),
    PERCENTAGE_FEE(TipoTaxaSobretaxa.DETALHETARIFA, false, "FEEP", "Fee's Percentage", true),
    PERCENTAGE_RAV(TipoTaxaSobretaxa.DETALHETARIFA, false, "RAVP", "Rav's Percentage", true),
    PERCENTAGE_RC(TipoTaxaSobretaxa.DETALHETARIFA, false, "RCP", "Rc's Percentage", true),
    PERCENTAGE_TAXA_BOLSA(TipoTaxaSobretaxa.DETALHETARIFA, false, "TBP", "Bag's Tax Percentage", true),
    INCENTIVO_FORNECEDOR(TipoTaxaSobretaxa.DETALHETARIFA, false, "ICT", "Incentivo Fornecedor"),
    FEE(TipoTaxaSobretaxa.SOBRETAXA, false, "CFEE", "Fee"),
    PORCENTAGEM_INCENTIVO_FORNECEDOR(TipoTaxaSobretaxa.DETALHETARIFA, false, "ICP", "Porcentagem Incentivo Fornecedor", true),
    COMISSAO_CLIENTE(TipoTaxaSobretaxa.DETALHETARIFA, false, "COMC", "Comissão Cliente"),
    INCENTIVO_CLIENTE(TipoTaxaSobretaxa.DETALHETARIFA, false, "INCC", "Incentivo Cliente"),
    REPASSE_CLIENTE(TipoTaxaSobretaxa.DETALHETARIFA, false, "REPC", "Repasse Cliente"),
    PORCENTAGEM_COMISSAO_CLIENTE(TipoTaxaSobretaxa.DETALHETARIFA, false, "PCC", "Porcentagem Comissão Cliente"),
    PORCENTAGEM_INCENTIVO_CLIENTE(TipoTaxaSobretaxa.DETALHETARIFA, false, "PIC", "Porcentagem Incentivo Cliente"),
    PORCENTAGEM_REPASSE_CLIENTE(TipoTaxaSobretaxa.DETALHETARIFA, false, "PRC", "Porcentagem Repasse Cliente"),
    MULTA(TipoTaxaSobretaxa.TAXA, false, "MULTA", "Multa"), //O Valor da multa aplicada (Presente quendo houver reemissão ou cancelamento de reserva)
    INFORMACAO_MULTA(TipoTaxaSobretaxa.DETALHETARIFA, false, "INFO_MULTA", "Informação de Multa"), //Quando a multa não foi aplicada (Ou seja, não houve reemissão nem cancelamento de reserva)
    TAXA_BAGAGEM(TipoTaxaSobretaxa.TAXA, true, "TAXA_BAGAGEM", "Taxa da Bagagem"),
    TAXA_ASSENTO(TipoTaxaSobretaxa.TAXA, true, "TAXA_ASSENTO", "Taxa do Assento"),

    //TODO Esses valores são referentes ao processo de reemissão. Talvez seja melhor criar um tipo para eles, mas por enquanto ficam no detalhe
    TARIFA_BILHETE_ORIGINAL(TipoTaxaSobretaxa.DETALHETARIFA, false, "TARIFA_BILHETE_ORIGINAL", "Tarifa Bilhete Original"),
    TAXA_EMBARQUE_BILHETE_ORIGINAL(TipoTaxaSobretaxa.DETALHETARIFA, false, "TAXA_EMBARQUE_BILHETE_ORIGINAL", "Taxa Embarque Bilhete Original"),
    DU_BILHETE_ORIGINAL(TipoTaxaSobretaxa.DETALHETARIFA, false, "DU_BILHETE_ORIGINAL", "Du Bilhete Original"),
    TARIFA_RESERVA_REEMISSAO(TipoTaxaSobretaxa.DETALHETARIFA, false, "TARIFA_RESERVA_REEMISSAO", "Tarifa Reserva Reemissão"),
    TAXA_EMBARQUE_RESERVA_REEMISSAO(TipoTaxaSobretaxa.DETALHETARIFA, false, "TAXA_EMBARQUE_RESERVA_REEMISSAO", "Taxa Embarque Reserva Reemissão"),
    TROCO_TARIFA(TipoTaxaSobretaxa.DETALHETARIFA, false, "TROCO_TARIFA", "Troco Tarifa"),
    TROCO_TAXA(TipoTaxaSobretaxa.DETALHETARIFA, false, "TROCO_TAXA", "Troco Taxa"),
    MARKUP_BILHETE_ORIGINAL(TipoTaxaSobretaxa.DETALHETARIFA, false, "MARKUP_BILHETE_ORIGINAL", "Markup Bilhete Original"),
    MARKUP_TARIFA_REEMISSAO(TipoTaxaSobretaxa.DETALHETARIFA, false, "MARKUP_TARIFA_REEMISSAO", "Markup Tarifa Reemissao"),
    SALDO_REEMISSAO(TipoTaxaSobretaxa.DETALHETARIFA, false, "SALDO_REEMISSAO", "Saldo reemissao"),

    TAXA_BOLSA(TipoTaxaSobretaxa.SOBRETAXA, false, "TB", "Taxa bolsa"),
    TAXA_MENOR_ACOMPANHADO(TipoTaxaSobretaxa.DETALHETARIFA, false, "UMR", "UMR"),//https://en.wikipedia.org/wiki/Unaccompanied_minor
    OUTRAS_TAXAS(TipoTaxaSobretaxa.DETALHETARIFA, false, "OTH_TX", "Outras Taxas"),

    TARIFA_BAGAGEM(TipoTaxaSobretaxa.DETALHETARIFA, true, "TARIFA_BAGAGEM", "Tarifa de Bagagem"),
    TARIFA_ASSENTO(TipoTaxaSobretaxa.DETALHETARIFA, true, "TARIFA_ASSENTO", "Tarifa de Assento"),
    TROCO_BAGAGEM(TipoTaxaSobretaxa.DETALHETARIFA, true, "TROCO_BAGAGEM", "Troco da Bagagem"),
    TROCO_ASSENTO(TipoTaxaSobretaxa.DETALHETARIFA, true, "TROCO_ASSENTO", "Troco do Assento");

    private TipoTaxaSobretaxa tipoTaxaSobretaxa;
    private String            codigo;
    private String            descricao;
    private Boolean           isAncillaryType;
    private boolean percent;

    TipoValor(TipoTaxaSobretaxa tipoTaxaSobretaxa, Boolean isAncillaryType, String codigo, String descricao) {
        this(tipoTaxaSobretaxa, isAncillaryType, codigo, descricao,false);
    }

    TipoValor(TipoTaxaSobretaxa tipoTaxaSobretaxa, Boolean isAncillaryType, String codigo, String descricao, boolean percent) {
        this.tipoTaxaSobretaxa = tipoTaxaSobretaxa;
        this.isAncillaryType = isAncillaryType;
        this.codigo = codigo;
        this.descricao = descricao;
        this.percent = percent;
    }

    public boolean isPercent(){
        return this.percent;
    }

    public String getCodigo() {
	return codigo;
    }

    public static TipoValor getByCodigo(String codigo) {
        for (TipoValor tipoValor : TipoValor.values()) {
            if (tipoValor.getCodigo().equals(codigo)) {
                return tipoValor;
            }
        }

        throw new IllegalArgumentException("Não existe o TipoValor com o código - " + codigo);
    }

    public static TipoValor getByDescricao(String descricao) {
	for (TipoValor tipoValor : TipoValor.values()) {
	    if (tipoValor.getDescricao().equals(descricao)) {
		return tipoValor;
	    }
	}

	throw new IllegalArgumentException("Não existe o TipoValor com a descrição - " + descricao);
    }

    public static Boolean isTaxDetailRelativeToSurcharge(TipoValor tipoValorDetalhe, TipoValor tipoValorSobreTaxa) {

        boolean isRelative = false;

        if (TipoValor.FEE == tipoValorSobreTaxa && TipoValor.PERCENTAGE_FEE == tipoValorDetalhe) {
            isRelative = true;
        } else if (TipoValor.RAV  == tipoValorSobreTaxa  && TipoValor.PERCENTAGE_RAV == tipoValorDetalhe) {
            isRelative = true;
        } else if (TipoValor.RC == tipoValorSobreTaxa && TipoValor.PERCENTAGE_RC == tipoValorDetalhe) {
            isRelative = true;
        } else if (TipoValor.TAXA_BOLSA == tipoValorSobreTaxa && TipoValor.PERCENTAGE_TAXA_BOLSA == tipoValorDetalhe) {
            isRelative = true;
        }
        return isRelative;
    }


    public String getDescricao() {
	return descricao;
    }

    public TipoTaxaSobretaxa getTipoTaxaSobretaxa() {
        return this.tipoTaxaSobretaxa;
    }

    public Boolean isAncillaryType() {
        return isAncillaryType;
    }
}

package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.configuracaoRc.historico;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.HistoricoConfiguracaoRcRecord;
import br.tur.reservafacil.dominio.ApplicationUser;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRc;
import br.tur.reservafacil.dominio.aereo.HistoricoConfiguracaoRc;
import org.jooq.RecordMapper;
import org.springframework.stereotype.Component;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
@Component
public class HistoricoConfiguracaoRcRecord2HistoricoConfiguracaoRcMapper
		implements RecordMapper<HistoricoConfiguracaoRcRecord, HistoricoConfiguracaoRc> {

    @Override
    public HistoricoConfiguracaoRc map(HistoricoConfiguracaoRcRecord record) {
        HistoricoConfiguracaoRc historico = new HistoricoConfiguracaoRc();
	historico.setId(record.getId());
	historico.setIdOrigem(record.getConfiguracaoRcIdOrigem());
	historico.setConfiguracaoRc(this.getConfPrecificacaoLazy(record.getConfiguracaoRcId()));
        historico.setDataEdicao(record.getDataEdicao() != null ? record.getDataEdicao().toLocalDateTime() : null);
        historico.setUsuario(this.getUsuarioLazy(record.getUsuarioId()));
        historico.setPublicador(getUsuarioLazy(record.getPublicadorId()));
        historico.setDataAtualizacao(record.getDataAtualizacao().toLocalDateTime());

	return historico;
    }

    private ApplicationUser getUsuarioLazy(Integer usuarioId) {
		ApplicationUser usuario = null;
	if (usuarioId != null) {
	    usuario = new ApplicationUser();
	    usuario.setId(usuarioId);
	}
	return usuario;
    }

    private ConfiguracaoRc getConfPrecificacaoLazy(Integer configuracaoPrecificacaoId) {
	ConfiguracaoRc configuracao = null;
	if (configuracaoPrecificacaoId != null) {
	    configuracao = new ConfiguracaoRc();
	    configuracao.setId(configuracaoPrecificacaoId);
	}
	return configuracao;
    }
}

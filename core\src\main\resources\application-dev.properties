jedis.host=cache-search-cvc-02-hom.reservafacil.tur.br
jedis.port=6379
jedis.password=
jedis.useSSL=true

database.config.filename=/psql-cvc-hom.properties

banner.image.invert=true
spring.output.ansi.enabled=always

#Quando true ele loga cada clone para stdout
cloner.dump.cloned.classes=false

#captura o stdout e joga no log
logging.capture.stdout=true

http.log.traffic=false

aws.secretsmanager.region=us-east-1

gwapi.appkey.url=https://gw-api.k8s-qa-cvc.com.br/gwapi/rs/v0/config/appkeys?app={app}&profile={profile}
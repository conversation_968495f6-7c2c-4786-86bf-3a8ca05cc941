package br.tur.reservafacil.precificador.application.service;

import br.tur.reservafacil.dominio.aereo.*;
import br.tur.reservafacil.dominio.aereo.voosExcluidos.MotivoExclusaoVoo;
import br.tur.reservafacil.dominio.aereo.voosExcluidos.RestricaoBarrada;
import br.tur.reservafacil.dominio.intermediario.v0.to.precificacao.ConfiguracaoPrecificacaoComMotivoExclusao;
import br.tur.reservafacil.dominio.intermediario.v0.to.precificacao.ParametroSessaoPrecificacao;
import br.tur.reservafacil.dominio.intermediario.v0.to.precificacao.Precificavel;
import br.tur.reservafacil.dominio.tipo.TipoConfigPrecificacao;
import br.tur.reservafacil.dominio.tipo.TipoMarkup;
import br.tur.reservafacil.precificador.application.service.cache.ConfiguracaoPrecificacaoRedisCacheComponent;
import br.tur.reservafacil.precificador.domain.component.restricao.RestricaoTreeComponent;
import br.tur.reservafacil.precificador.domain.component.restricao.RestricaoValidatorComponent;
import br.tur.reservafacil.precificador.domain.repository.ConfiguracaoPrecificacaoRepository;
import br.tur.reservafacil.precificador.infrastructure.performance.TimeAudit;
import com.newrelic.api.agent.Trace;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static br.tur.reservafacil.dominio.tipo.TipoConfigPrecificacao.*;

/**
 * Created by davidson on 6/10/16.
 */
@Service
public class ConfiguracaoPrecificacaoService extends AbstractService<ConfiguracaoPrecificacao, Integer, ConfiguracaoPrecificacaoRepository> {

    private static final Logger LOG = LoggerFactory.getLogger(ConfiguracaoPrecificacaoService.class);

    @Autowired
    private ConfiguracaoPrecificacaoRetriever retriever;

    @Autowired
    private RestricaoTreeComponent restricaoTreeComponent;

    @Autowired
    private ConfiguracaoPrecificacaoRedisCacheComponent configuracaoPrecificacaoRedisCacheComponent;

    /**
     * Busca uma configuração de precificação para o precificavel informado
     *
     * @param precificavel precificavel
     * @param sessaoPrecificacao sessaoPrecificacao
     * @return a {@link ConfiguracaoPrecificacao} a ser utilizada para precificação
     */
    @Trace
    public Optional<ConfiguracaoPrecificacao> obterConfigPrecificacaoRestritaPorPrecificavel(List<Restricao> restricoes, Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao, boolean markupDinamicoFirst) {
        FiltroConfiguracao filtro = criaFiltroPorPrecificavel(restricoes, precificavel, sessaoPrecificacao, RESTRITA, false);
        filtro.setMarkupDinamicoFirst(markupDinamicoFirst);
        return retriever.getFirstMatchConfiguracaoPrecificacao(filtro);
    }

    @Trace
    public List<ConfiguracaoPrecificacaoComMotivoExclusao> obterConfiguracoesPrecificacaoRestritaPorPrecificavel(List<Restricao> restricoes, Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao) {
        FiltroConfiguracao filtro = criaFiltroPorPrecificavel(restricoes, precificavel, sessaoPrecificacao, RESTRITA, false);
        return retriever.getAllConfiguracaoPrecificacao(filtro);
    }

    /**
     * Busca os motivos para uma exclusão de um precificavel.
     * Esse método será chamado logo após a execução de precificação de um precificavel, onde não exista nenhuma configuração do tipo RESTRITA para precificar a recomendação em questão,
     * ou quando encontrar alguma configuração de exclusão.
     *
     * @param precificavel precificavel
     * @param sessaoPrecificacao sessaoPrecificacao
     * @return a {@link ConfiguracaoPrecificacao} a ser utilizada para precificação
     */
    @Trace
    public List<MotivoExclusaoVoo> obterMotivosExclusaoVoo(List<Restricao> restricoes, Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao) {
        if (precificavel.isExcludedByFilter()) {
            return Collections.singletonList(new MotivoExclusaoVoo(precificavel.getExcludedByFilter()));
        }
        FiltroConfiguracao filtro = criaFiltroPorPrecificavel(restricoes, precificavel, sessaoPrecificacao, EXCLUSAO, false);
        Optional<ConfiguracaoPrecificacao> configuracaoPrecificacao = retriever.getFirstMatchConfiguracaoPrecificacao(filtro);
        if(configuracaoPrecificacao.isPresent()) {
            ConfiguracaoPrecificacao config = configuracaoPrecificacao.get();
            List<RestricaoBarrada> restricoesDeExclusao = config.getRestricoes().stream()
                                                                .map(restricao -> new RestricaoBarrada(restricao.getValor(), restricao.getTipoRestricao()))
                                                                .collect(Collectors.toList());

            return Collections.singletonList(new MotivoExclusaoVoo(config.getNome(), config.getId(), restricoesDeExclusao, Collections.emptyList()));
        }
        //Caso não tenha encontrado uma configuração de exclusão, coleta os motivos das configurações de tipo restrita
        filtro.setTipoConfigPrecificacao(RESTRITA);
        return retriever.findByFiltroConfiguracao(filtro);
    }

    /**
     * Busca todas configurações de precificação para a reservaAerea informada, com o tipo padrão
     *
     * @param precificavel precificavel
     * @param sessaoPrecificacao sessaoPrecificacao
     * @return a {@link ConfiguracaoPrecificacao} a ser utilizada para precificação
     */
    @Trace
    public List<ConfiguracaoPrecificacao> findPadraoByPrecificavel(List<Restricao> restricoes, Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao) {
        return findPadraoByPrecificavel(restricoes, precificavel, sessaoPrecificacao, false);
    }

    /**
     * Busca todas configurações de precificação para a reservaAerea informada, com o tipo padrão
     *
     * @param precificavel precificavel
     * @param sessaoPrecificacao sessaoPrecificacao
     * @return a {@link ConfiguracaoPrecificacao} a ser utilizada para precificação
     */
    @Trace
    public List<ConfiguracaoPrecificacao> findPadraoByPrecificavel(List<Restricao> restricoes, Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao, boolean markupDinamicoFirst) {
        FiltroConfiguracao filtro = criaFiltroPorPrecificavel(restricoes, precificavel, sessaoPrecificacao, PADRAO, false);
        filtro.setMarkupDinamicoFirst(markupDinamicoFirst);
        return retriever.getAllMatchConfiguracaoPrecificacao(filtro);
    }

    /**
     * Busca todas configurações de precificação para a reservaAerea informada, com o tipo sucessor e retornar a primeira (ou seja, a de maior prioridade)
     *
     * @param precificavel precificavel
     * @param sessaoPrecificacao sessaoPrecificacao
     * @return a {@link ConfiguracaoPrecificacao} a ser utilizada para precificação
     */
    @Trace
    public Optional<ConfiguracaoPrecificacao> findFirstSucessorByPrecificavel(List<Restricao> restricoes, Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao, boolean markupDinamicoFirst) {
        FiltroConfiguracao filtro = criaFiltroPorPrecificavel(restricoes, precificavel, sessaoPrecificacao, SUCESSOR, false);
        filtro.setMarkupDinamicoFirst(markupDinamicoFirst);
        return retriever.getAllMatchConfiguracaoPrecificacao(filtro).stream().findFirst();
    }

    @Trace
    @Cacheable(cacheNames = "ConfiguracaoPrecificacaoService::findByIds", cacheManager = "ehCacheManager", sync = true) // sync = true TODO
    public List<ConfiguracaoPrecificacao> findByIds(List<Integer> keys) {
        return configuracaoPrecificacaoRedisCacheComponent.findByIds(keys);
    }

    public boolean deveExcluirVooPorConfiguracaoDeExclusao(List<Restricao> restricoes, Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao) {
        FiltroConfiguracao filtro = criaFiltroPorPrecificavel(restricoes, precificavel, sessaoPrecificacao, EXCLUSAO, false);
        Optional<ConfiguracaoPrecificacao> configExclusao = retriever.getFirstMatchConfiguracaoPrecificacao(filtro);
        if (configExclusao.isPresent()) {
            LOG.debug("Precificavel excluido devido a config de exclusão com id: " + configExclusao.get().getId());
        }
        return configExclusao.isPresent();
    }

    @Trace
    @Cacheable(cacheNames = "ConfiguracaoPrecificacaoService::findByKey", cacheManager = "ehCacheManager", sync = true) // sync = true TODO
    public ConfiguracaoPrecificacao findByKey(Integer key) {
        return configuracaoPrecificacaoRedisCacheComponent.findByKey(key);
    }

    public Optional<ConfiguracaoPrecificacao> obterConfigPrecificacaoRestritaPorPrecificavelBestPrice(List<Restricao> restricoes, Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao) {
        FiltroConfiguracao filtro = criaFiltroPorPrecificavel(restricoes, precificavel, sessaoPrecificacao, RESTRITA, true);
        return retriever.getFirstMatchConfiguracaoPrecificacao(filtro);
    }

    private FiltroConfiguracao criaFiltroPorPrecificavel(List<Restricao> restricoes, Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao, TipoConfigPrecificacao tipoConfigPrecificacao, Boolean isBestPrice) {
        final String codigoCiaViagem = precificavel.getCiaAereaViagem();
        final Integer empresaId = sessaoPrecificacao.getEmpresa().getId();
        final Boolean isBuscaDeTeste = sessaoPrecificacao.isBuscaDeTeste();
        final String produto = !isBestPrice ? precificavel.getProdutoPrecificacao() : "BPRICE";

        return criarFiltro(codigoCiaViagem, restricoes, empresaId, isBuscaDeTeste, tipoConfigPrecificacao, produto, isBestPrice);
    }

    private FiltroConfiguracao criarFiltro(String codigoCia, List<Restricao> restricoes, Integer empresaId, Boolean isBuscaDeTeste, TipoConfigPrecificacao tipoConfigPrecificacao, String produto, Boolean isBestPrice) {
        FiltroConfiguracao filtro = new FiltroConfiguracao();
        filtro.setCodigoCia(codigoCia);
        filtro.setRestricoes(restricoes);
        filtro.setEmpresaId(empresaId);
        filtro.setTipoConfigPrecificacao(tipoConfigPrecificacao);
        filtro.setBuscaDeTeste(isBuscaDeTeste);
        filtro.setProduto(produto);
        filtro.setBestPrice(isBestPrice);
        return filtro;
    }

    @Trace
    @Cacheable(cacheNames = "ConfiguracaoPrecificacaoService::findEagerByKey", cacheManager = "ehCacheManager", sync = true) // sync = true TODO
    public ConfiguracaoPrecificacao findEagerByKey(Integer id) {
        final ConfiguracaoPrecificacao configuracaoPrecificacao = this.getRepository().findEaggerByKey(id);
        //        configuracaoPrecificacao.setRestricoes(restricaoTreeComponent.desagruparRestricoes(restricaoTreeComponent.gerarArvoreDeRestricoes(configuracaoPrecificacao.getRestricoes())));
        //        configuracaoPrecificacao.agrupaRestricoes();
        return configuracaoPrecificacao;
    }

    public List<ConfiguracaoPrecificacao> findByParameters(int page, Integer pageSize, String orderBy, Map<String, String> params) {
        List<ConfiguracaoPrecificacao> resultados = this.getRepository().findByParameters(page, pageSize, orderBy, params);
        resultados.forEach(ConfiguracaoPrecificacao::agrupaRestricoes);
        return resultados;
    }

    public Integer countByParameters(Map<String, String> params) {
        return this.getRepository().countByParameters(params);
    }

    public ConfiguracaoPrecificacaoIsaCondicao getConfiguracaoPrecificacaoRestritaByIds(List<Integer> ids) {
        return this.getRepository().findConfiguracaoPrecificacaoRestritaByIds(ids);
    }

    /**
     *
     * FIXME: Existe apenas por causa que utilizando apenas Spring AOP, sem AspectJ, o @Cacheable não funciona quando chamando métodos da
     * própria classe. Como a idéia é eventualmente migrar para compile-time weaving com AspectJ, essa classe deve sumir e a classe voltar
     * ao que era antes.
     *
     */
    @Component
    static class ConfiguracaoPrecificacaoRetriever {

        @Autowired
        private RestricaoValidatorComponent restricaoValidatorComponent;

        @Autowired
        private ConfiguracaoPrecificacaoRepositoryRetriever configuracaoPrecificacaoRetriever;

        @Autowired
        private MarkupProdutoService markupProdutoService;

        @Autowired
        private ExcecaoService excecaoService;

        @Cacheable(cacheNames = "ConfiguracaoPrecificacaoService::findByFiltroConfiguracao", cacheManager="ehCacheManager")
        @Trace
        public List<MotivoExclusaoVoo> findByFiltroConfiguracao(FiltroConfiguracao filtro) {
            final LocalTime time = LocalTime.now();
            int second = time.getSecond();
            String timeToCache = filtro.isBuscaDeTeste() ? ", time=" + DateTimeFormatter.ofPattern("HH:mm:").format(time) + second / 10 : "";

            List<ConfiguracaoPrecificacao> configsPrecificacao = configuracaoPrecificacaoRetriever.findByCiaTipoConfigEmpresaIdProduto(
                    filtro.getCodigoCia(),
                    filtro.getTipoConfigPrecificacao(),
                    filtro.getEmpresaId(),
                    filtro.getProduto(),
                    filtro.isBuscaDeTeste(),
                    timeToCache,
                    filtro.isMarkupDinamicoFirst());
            List<MotivoExclusaoVoo> motivos = new ArrayList<>();

            if (configsPrecificacao.isEmpty()) {
                MotivoExclusaoVoo motivoExclusaoVoo = new MotivoExclusaoVoo();
                motivoExclusaoVoo.setNomeConfiguracaoPrecificacao("Nenhuma configuração encontrada para empresa: " + filtro.getEmpresaId() + " - cia: " + filtro.getCodigoCia() + " - produto: " + filtro.getProduto());
                motivos.add(motivoExclusaoVoo);
                return motivos;
            }

            for (ConfiguracaoPrecificacao configuracaoPrecificacao : configsPrecificacao) {
                final boolean processaAteOFim = true;

                final MotivoExclusaoVoo motivoExclusaoVoo = restricaoValidatorComponent.validaRestricoes(filtro.getRestricoes(), configuracaoPrecificacao.getRestricoes(), processaAteOFim);
                //Essa verificação é necessário, pois pode haver alguma alteração no banco de dados entre a hora que a configuração foi buscada para precificação e o momento que o precificavel foi jogada para exclusão
                if(motivoExclusaoVoo != null) {
                    motivoExclusaoVoo.setIdConfiguracaoPrecificacao(configuracaoPrecificacao.getId());
                    motivoExclusaoVoo.setNomeConfiguracaoPrecificacao(configuracaoPrecificacao.getNome());
                    motivos.add(motivoExclusaoVoo);
                }
            }
            return motivos;
        }


        /**
         *
         * FIXME: Renomear. Eu ia renomear estes dois métodos para seguir o padrão de nome dos outros métodos (pelo menos um dos padrões... :/ ) mas
         * juro que não consegui entender o que esse método faz, baseado só nele e no nome. Não vou pesquisar contexto agora...
         *
         */
        //	@Cacheable(cacheNames = "ConfiguracaoPrecificacaoService::getFirstMatchConfiguracaoPrecificacao", cacheManager="redisCacheManager")
        @Trace
        public Optional<ConfiguracaoPrecificacao> getFirstMatchConfiguracaoPrecificacao(FiltroConfiguracao filtro) {
            final LocalTime time = LocalTime.now();
            int second = time.getSecond();
            String timeToCache = filtro.isBuscaDeTeste() ? ", time=" + DateTimeFormatter.ofPattern("HH:mm:").format(time) + second / 10 : "";

            List<ConfiguracaoPrecificacao> configsPrecificacao = new ArrayList<>();
            if (filtro.isBestPrice()) {
                configsPrecificacao = configuracaoPrecificacaoRetriever.findByCiaTipoConfigEmpresaIdProdutoBPRICEMarkupLimite(
                        filtro.getCodigoCia(),
                        filtro.getTipoConfigPrecificacao(),
                        filtro.getEmpresaId(),
                        filtro.getProduto());

            } else {
                configsPrecificacao = configuracaoPrecificacaoRetriever.findByCiaTipoConfigEmpresaIdProduto(
                        filtro.getCodigoCia(),
                        filtro.getTipoConfigPrecificacao(),
                        filtro.getEmpresaId(),
                        filtro.getProduto(),
                        filtro.isBuscaDeTeste(),
                        timeToCache,
                        filtro.isMarkupDinamicoFirst());
            }

            for (ConfiguracaoPrecificacao configuracaoPrecificacao : configsPrecificacao) {
                if (TipoMarkup.INTELIGENTE.equals(configuracaoPrecificacao.getTipoMarkup()) && !filtro.isMarkupDinamicoFirst()) {
                    //		    não precisa validar este tipo de precificação se o markup inteligente for falso
                } else {
                    boolean isValid = restricaoValidatorComponent.validaRestricoes(filtro.getRestricoes(), configuracaoPrecificacao.getRestricoes());
                    if (isValid) {
                        this.carregaDadosParaPrecificacao(configuracaoPrecificacao);
                        return Optional.ofNullable(configuracaoPrecificacao);
                    }
                }
            }
            return Optional.empty();
        }

        @Trace
        public List<ConfiguracaoPrecificacaoComMotivoExclusao> getAllConfiguracaoPrecificacao(FiltroConfiguracao filtro) {
            final LocalTime time = LocalTime.now();
            int second = time.getSecond();
            String timeToCache = filtro.isBuscaDeTeste() ? ", time=" + DateTimeFormatter.ofPattern("HH:mm:").format(time) + second / 10 : "";

            List<ConfiguracaoPrecificacao> configsPrecificacao = configuracaoPrecificacaoRetriever.findByCiaTipoConfigEmpresaIdProduto(
                    filtro.getCodigoCia(),
                    filtro.getTipoConfigPrecificacao(),
                    filtro.getEmpresaId(),
                    filtro.getProduto(),
                    filtro.isBuscaDeTeste(),
                    timeToCache,
                    filtro.isMarkupDinamicoFirst());

            List<ConfiguracaoPrecificacaoComMotivoExclusao> configuracoesEMotivos = new ArrayList<>();
            for (ConfiguracaoPrecificacao configuracaoPrecificacao : configsPrecificacao) {
                MotivoExclusaoVoo motivoExclusaoVoo = restricaoValidatorComponent.validaRestricoes(filtro.getRestricoes(), configuracaoPrecificacao.getRestricoes(), true);
                this.carregaDadosParaPrecificacao(configuracaoPrecificacao);
                configuracoesEMotivos.add(ConfiguracaoPrecificacaoComMotivoExclusao.of(configuracaoPrecificacao, motivoExclusaoVoo));
            }

            return configuracoesEMotivos;
        }

        @Trace
        public List<ConfiguracaoPrecificacaoComMotivoExclusao> getConfiguracaoPrecificacaoComMotivoExclusao(List<Restricao> restricoesRec, ConfiguracaoPrecificacao configuracaoPrecificacao) {
            List<ConfiguracaoPrecificacaoComMotivoExclusao> configuracoesEMotivos = new ArrayList<>();
            MotivoExclusaoVoo motivoExclusaoVoo = restricaoValidatorComponent.validaRestricoes(restricoesRec, configuracaoPrecificacao.getRestricoes(), true);
            this.carregaDadosParaPrecificacao(configuracaoPrecificacao);
            configuracoesEMotivos.add(ConfiguracaoPrecificacaoComMotivoExclusao.of(configuracaoPrecificacao, motivoExclusaoVoo));

            return configuracoesEMotivos;
        }

        //	@Cacheable(cacheNames = "ConfiguracaoPrecificacaoService::getAllMatchConfiguracaoPrecificacao", unless = "#result == null")
        @Trace
        public List<ConfiguracaoPrecificacao> getAllMatchConfiguracaoPrecificacao(FiltroConfiguracao filtro) {
            final LocalTime time = LocalTime.now();
            int second = time.getSecond();
            String timeToCache = filtro.isBuscaDeTeste() ? ", time=" + DateTimeFormatter.ofPattern("HH:mm:").format(time) + second / 10 : "";

            List<ConfiguracaoPrecificacao> allMatch = new ArrayList<>();
            List<ConfiguracaoPrecificacao> configsPrecificacao = configuracaoPrecificacaoRetriever.findByCiaTipoConfigEmpresaIdProduto(
                    filtro.getCodigoCia(),
                    filtro.getTipoConfigPrecificacao(),
                    filtro.getEmpresaId(),
                    filtro.getProduto(),
                    filtro.isBuscaDeTeste(),
                    timeToCache,
                    filtro.isMarkupDinamicoFirst());
            for (ConfiguracaoPrecificacao configuracaoPrecificacao : configsPrecificacao) {
                boolean isValid = restricaoValidatorComponent.validaRestricoes(filtro.getRestricoes(), configuracaoPrecificacao.getRestricoes());
                if (isValid) {
                    this.carregaDadosParaPrecificacao(configuracaoPrecificacao);
                    allMatch.add(configuracaoPrecificacao);
                }
            }
            return allMatch;
        }

        /**
         * Carrega as informações necessárias para precificação, como, Markup e Exceção.
         *
         * @param configuracaoPrecificacao configuracaoPrecificacao
         */
        @TimeAudit
        private void carregaDadosParaPrecificacao(ConfiguracaoPrecificacao configuracaoPrecificacao) {
            final Integer idConfiguracao = configuracaoPrecificacao.getId();
            configuracaoPrecificacao.setMarkups(this.markupProdutoService.findByConfiguracaoId(idConfiguracao));
            //configuracaoPrecificacao.setExcecoes(this.excecaoService.findByConfiguracaoId(idConfiguracao));
        }



    }

    /**
     *
     * FIXME: Existe apenas por causa que utilizando apenas Spring AOP, sem AspectJ, o @Cacheable não funciona quando chamando métodos da
     * própria classe. Como a idéia é eventualmente migrar para compile-time weaving com AspectJ, essa classe deve sumir e a classe voltar
     * ao que era antes.
     *
     */
    @Component
    static class ConfiguracaoPrecificacaoRepositoryRetriever {

        @Autowired
        private ConfiguracaoPrecificacaoRedisCacheComponent configuracaoPrecificacaoRedisCacheComponent;

        @Trace
        @Cacheable(cacheNames = "ConfiguracaoPrecificacaoService::findByCiaTipoConfigEmpresaIdProduto", cacheManager = "ehCacheManager") // sync = true TODO
        @TimeAudit
        public List<ConfiguracaoPrecificacao> findByCiaTipoConfigEmpresaIdProduto(String codCia, TipoConfigPrecificacao tipoConfigPrecificacao,
                Integer empresaId, String produto, Boolean isBuscaDeTeste, String timeToCache, Boolean markupDinamicoFirst) {
            return configuracaoPrecificacaoRedisCacheComponent.findByCiaTipoConfigEmpresaIdProduto(codCia, tipoConfigPrecificacao, empresaId, produto, isBuscaDeTeste, timeToCache, markupDinamicoFirst);
        }

        @Trace
        @Cacheable(cacheNames = "ConfiguracaoPrecificacaoService::findByCiaTipoConfigEmpresaIdProdutoBPRICEMarkupLimite", cacheManager = "ehCacheManager") // sync = true TODO
        @TimeAudit
        public List<ConfiguracaoPrecificacao> findByCiaTipoConfigEmpresaIdProdutoBPRICEMarkupLimite(String codCia, TipoConfigPrecificacao tipoConfigPrecificacao, Integer empresaId, String produto) {
            return configuracaoPrecificacaoRedisCacheComponent.findByCiaTipoConfigEmpresaIdProdutoBPRICEMarkupLimite(codCia, tipoConfigPrecificacao, empresaId, produto);
        }
    }
}

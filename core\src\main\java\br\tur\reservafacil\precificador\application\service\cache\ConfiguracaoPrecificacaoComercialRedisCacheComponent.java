package br.tur.reservafacil.precificador.application.service.cache;

import br.tur.reservafacil.dominio.aereo.ConfiguracaoPrecificacaoComercial;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoPrecificacaoComercialReport;
import br.tur.reservafacil.dominio.tipo.TipoConfigPrecificacao;
import br.tur.reservafacil.precificador.domain.repository.ConfiguracaoPrecificacaoComercialRepository;
import com.newrelic.api.agent.Trace;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

/**
 * Created by fepelichero on 08/11/2018.
 */
@Component
public class ConfiguracaoPrecificacaoComercialRedisCacheComponent {

    @Autowired
    private ConfiguracaoPrecificacaoComercialRepository repository;

    @Trace
    @Cacheable(cacheNames = "ConfiguracaoPrecificacaoComercialService::findByCiaTipoConfigEmpresaIdProduto", cacheManager = "redisCacheManager")
    public List<ConfiguracaoPrecificacaoComercial> findByCiaTipoConfigEmpresaIdProduto(String codCia, TipoConfigPrecificacao tipoConfigPrecificacao,
										       Integer empresaId, String produto, Boolean isBuscaDeTeste, String condicaoPrecificacao) {
        return repository.buscaSoComRestricaoByCiaTipoConfigEmpresaIdProduto(codCia, tipoConfigPrecificacao, empresaId, produto, isBuscaDeTeste, condicaoPrecificacao);
    }

    @Trace
    @Cacheable(cacheNames = "ConfiguracaoPrecificacaoComercialService::reportConditionByAgencyBranchCompany", cacheManager = "redisCacheManager")
    public List<ConfiguracaoPrecificacaoComercialReport> reportConditionByAgencyBranchCompany(String agencyRef, String branchRef, Integer companyId) {
	return repository.reportConditionByAgencyBranchCompany(agencyRef, branchRef, companyId);
    }

    @Trace
    @Cacheable(cacheNames = "ConfiguracaoPrecificacaoComercialService::reportConditionByCodigoPrecificacao", cacheManager = "redisCacheManager")
    public List<ConfiguracaoPrecificacaoComercialReport> reportConditionByCodigoPrecificacao(String codigoPrecificacao) {
        return repository.reportConditionByCodigoPrecificacao(codigoPrecificacao);
    }

    @Trace
    @Cacheable(cacheNames = "ConfiguracaoPrecificacaoComercialService::findByEmpresa", cacheManager = "redisCacheManager")
    public List<ConfiguracaoPrecificacaoComercial> findByEmpresa(Integer empresaId, boolean isBuscaDeTeste) {
        return repository.findByEmpresa(empresaId, isBuscaDeTeste);
    }
}

package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.*;
import br.tur.reservafacil.dominio.Grupo;
import br.tur.reservafacil.dominio.Produto;
import br.tur.reservafacil.dominio.aereo.CiaAerea;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRav;
import br.tur.reservafacil.dominio.tipo.Moeda;
import br.tur.reservafacil.dominio.tipo.NacInt;
import br.tur.reservafacil.dominio.tipo.TipoConfigRav;
import org.jooq.Record;
import org.jooq.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.Tables.CONFIGURACAO_RAV_CIA_AEREA;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.CiaAerea.CIA_AEREA;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.ConfiguracaoRav.CONFIGURACAO_RAV;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.Grupo.GRUPO;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.Produto.PRODUTO;

/**
 * Created by davidson on 6/30/16.
 */
@Component
public class ConfiguracaoRavEagerMapper implements CustomMultipleMapper<List<ConfiguracaoRav>, Result<Record>> {

    @Autowired
    private ConfiguracaoRavRecord2ConfiguracaoRavMapper configRavMapper;

    @Autowired
    private CiaAereaRecord2CiaAereaMapper ciaAereaMapper;

    @Autowired
    private ProdutoRecord2ProdutoMapper produtoMapper;

    @Autowired
    private GrupoRecord2GrupoMapper grupoMapper;

    @Override
    public List<ConfiguracaoRav> map(Result<Record> result) {
        List<ConfiguracaoRav> configuracoesRav = new ArrayList<ConfiguracaoRav>();
        Map<ConfiguracaoRavRecord, Result<Record>> configuracaoRavRecordResultMap = result.intoGroups(CONFIGURACAO_RAV);

        for (Map.Entry<ConfiguracaoRavRecord, Result<Record>> entry : configuracaoRavRecordResultMap.entrySet()) {
            final ConfiguracaoRavRecord configRavRecord = entry.getKey();
            ConfiguracaoRav configuracaoRav = configRavMapper.map(configRavRecord);

            configuracaoRav.setCiasAereas(new ArrayList<>(this.mapeiaCiasAereas(entry)));
            configuracaoRav.setProdutos(new ArrayList<>(this.mapeiaProdutos(entry)));
            if (TipoConfigRav.GRUPO.equals(configuracaoRav.getTipoConfigRav())) {
                configuracaoRav.setGrupos(new ArrayList<>(this.mapeiaGrupos(entry)));
            }

            configuracoesRav.add(configuracaoRav);
        }

        return configuracoesRav;
    }

    private Set<CiaAerea> mapeiaCiasAereas(Map.Entry<ConfiguracaoRavRecord, Result<Record>> entry) {
        Set<CiaAerea> ciasAereas = new HashSet<>();
        for (Record record : entry.getValue()) {
            CiaAereaRecord ciaAereaRecord = record.into(CIA_AEREA);
            if (ciaAereaRecord.getId() != null) {
                CiaAerea ciaAerea = ciaAereaMapper.map(ciaAereaRecord);

                ConfiguracaoRavCiaAereaRecord configuracaoRavCiaAereaRecord = record.into(CONFIGURACAO_RAV_CIA_AEREA);
                ciaAerea.setValorMinimoRav(configuracaoRavCiaAereaRecord.getValorMinimo());
                ciaAerea.setFatorRav(configuracaoRavCiaAereaRecord.getFatorRav());
                ciaAerea.setPercentualRav(configuracaoRavCiaAereaRecord.getPercentualRav());
                ciaAerea.setVariavel(configuracaoRavCiaAereaRecord.getVariavel());
                if (configuracaoRavCiaAereaRecord.getMoeda() != null) {
                    ciaAerea.setMoeda(Moeda.valueOf(configuracaoRavCiaAereaRecord.getMoeda()));
                }
                if (configuracaoRavCiaAereaRecord.getNacInt() != null) {
                    ciaAerea.setNacInt(NacInt.valueOf(configuracaoRavCiaAereaRecord.getNacInt()));
                }

                ciasAereas.add(ciaAerea);
            }
        }
        return ciasAereas;
    }

    private Set<Produto> mapeiaProdutos(Map.Entry<ConfiguracaoRavRecord, Result<Record>> entry) {
        Set<Produto> produtos = new HashSet<>();
        for (Record record : entry.getValue()) {
            ProdutoRecord produtoRecord = record.into(PRODUTO);
            if (produtoRecord.getId() != null) {
                Produto produto = produtoMapper.map(produtoRecord);
                produtos.add(produto);
            }
        }
        return produtos;
    }

    private Set<Grupo> mapeiaGrupos(Map.Entry<ConfiguracaoRavRecord, Result<Record>> entry) {
        Set<Grupo> grupos = new HashSet<>();
        for (Record record : entry.getValue()) {
            GrupoRecord grupoRecord = record.into(GRUPO);
            if (grupoRecord.getId() != null) {
                Grupo grupo = grupoMapper.map(grupoRecord);
                grupos.add(grupo);
            }
        }
        return grupos;
    }
}

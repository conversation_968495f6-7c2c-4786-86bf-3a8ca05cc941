package br.tur.reservafacil.precificador.infrastructure.controller.rs;

import br.tur.reservafacil.dominio.aereo.voosExcluidos.MotivoExclusaoVoo;
import br.tur.reservafacil.dominio.aereo.voosExcluidos.RegistroVooExcluido;
import br.tur.reservafacil.dominio.intermediario.v0.to.precificacao.*;
import br.tur.reservafacil.dominio.intermediario.v0.to.precificacao.redis.PrecificavelRE;
import br.tur.reservafacil.precificador.Version;
import br.tur.reservafacil.precificador.application.service.PrecificadorService;
import br.tur.reservafacil.precificador.infrastructure.database.redis.PrecificavelRedisService;
import br.tur.reservafacil.precificador.infrastructure.performance.TimeAudit;
import br.tur.reservafacil.utils.cache.ChaveCache;
import br.tur.reservafacil.utils.cache.async.AsyncCacheComponent;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

import static br.tur.reservafacil.precificador.application.service.util.precificacao.ExcludedResultCreator.createExcludedResultForFront;
import static org.apache.http.HttpStatus.SC_OK;

/**
 * Created by davidson on 5/16/17.
 */
@RestController
@Api(tags = "Pricing", description = "Pricing endpoint")
@RequestMapping(value = "/rs/v1/aereo/precificacao", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class PrecificacaoResource {

    private static final Logger LOG = LoggerFactory.getLogger(PrecificacaoResource.class);

    @Autowired
    private PrecificadorService precificadorService;

    @Autowired
    private AsyncCacheComponent cacheComponent;

    @Autowired
    private Version version;

    @TimeAudit
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST)
//    @LogMethod(logParameters = true, logResult = true, preExecutionMessage = "Iniciando precificação.", postExecutionMessage = "Resposta da precificação.")
    @ApiResponses({@ApiResponse(response = PrecificacaoResponse.class, code = SC_OK, message = "Success")})
    public ResponseEntity<PrecificacaoResponse> aplicaPrecificacao(@RequestBody PrecificacaoRequest request) {
        List<ResultadoPrecificacao> resultados = precificadorService.precifica(request.getPrecificaveis(), request.isBuscaDeTeste(), request.getLoadInstallments(), request.getParametroBuscaAerea(), false);
        return new ResponseEntity<>(new PrecificacaoResponse(resultados), HttpStatus.OK);
    }


    @TimeAudit
    @ResponseBody
    @RequestMapping(value = "/bestPrice", method = RequestMethod.POST)
//    @LogMethod(logParameters = true, logResult = true, preExecutionMessage = "Iniciando precificação BEST.", postExecutionMessage = "Resposta da precificação BEST.")
    @ApiResponses({@ApiResponse(response = PrecificacaoResponse.class, code = SC_OK, message = "Success")})
    public ResponseEntity<PrecificacaoResponse> aplicaBestPrice(@RequestBody PrecificacaoRequest request) {
        List<ResultadoPrecificacao> resultados = precificadorService.precifica(request.getPrecificaveis(), request.isBuscaDeTeste(), request.getLoadInstallments(), request.getParametroBuscaAerea(), true);
        return new ResponseEntity<>(new PrecificacaoResponse(resultados), HttpStatus.OK);
    }

    @ResponseBody
    @RequestMapping(value = "/precificacoesExcluidas", params = {"correlationId", "hashVoos"}, produces = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.GET)
    @ApiResponses({@ApiResponse(response = RegistroVooExcluido.class, code = SC_OK, message = "Success")})
    public ResponseEntity<List<RegistroVooExcluido>> precificacoesExcluidas(@RequestParam(value = "correlationId") String correlationId,
                                                                            @RequestParam(value = "hashVoos") String hashVoos) {
        List<RegistroVooExcluido> motivosExclusaoVooCache = (List<RegistroVooExcluido>) this.cacheComponent.get(this.montaChaveCacheSessao(correlationId, hashVoos));
        HttpStatus status = motivosExclusaoVooCache == null || motivosExclusaoVooCache.isEmpty() ? HttpStatus.NO_CONTENT : HttpStatus.OK;
        return new ResponseEntity<>(motivosExclusaoVooCache, status);
    }

    @TimeAudit
    @ResponseBody
    @RequestMapping(value = "/aplicaPerfilTarifario", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
//    @LogMethod(logParameters = true, logResult = true, preExecutionMessage = "Iniciando aplicação do perfil tarifario.", postExecutionMessage = "Resposta do perfil tarifario.")
    @ApiResponses({@ApiResponse(response = PerfilTarifarioResponse.class, code = SC_OK, message = "Success")})
    public ResponseEntity<PerfilTarifarioResponse> aplicaPerfilTarifario(@RequestBody PrecificacaoRequest request) {
        List<RegraTarifaAerea> resultados = precificadorService.aplicaPerfilTarifario(request.getPrecificaveis());
        return new ResponseEntity<>(new PerfilTarifarioResponse(resultados), HttpStatus.OK);
    }

    @TimeAudit
    @ResponseBody
    @PostMapping(value = "/testaConfiguracaoPrecificacao", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<PrecificacaoTesteResponse> testaConfiguracaoPrecificacao(@RequestBody PrecificaConfiguracaoEspecifica precificaConfiguracaoEspecifica) {
        if (precificaConfiguracaoEspecifica != null && precificaConfiguracaoEspecifica.getPrecificavelRE() != null) {
            PrecificavelRE precificavelRE = precificaConfiguracaoEspecifica.getPrecificavelRE();
            Precificavel precificavel = precificavelRE.toPrecificavel();
            Pair<ResultadoPrecificacaoView, List<MotivoExclusaoVoo>> resultadoPrecificacao = this.getResultadoPrecificacaoListPair(precificavelRE, precificavel);

            return new ResponseEntity<>(PrecificacaoTesteResponse.of(resultadoPrecificacao.getLeft(), resultadoPrecificacao.getRight()), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(new PrecificacaoTesteResponse(), HttpStatus.BAD_REQUEST);
        }
    }

    @Autowired
    private PrecificavelRedisService precificavelRedisService;

    @TimeAudit
    @ResponseBody
    @PostMapping(value = "/debug", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<PrecificacaoTesteResponse> testaConfiguracaoPrecificacaoViaHash(@RequestBody PrecificaConfiguracaoEspecifica precificaConfiguracaoEspecifica) {

        PrecificavelRE precificavelRE = precificavelRedisService.findById(precificaConfiguracaoEspecifica.getPrecificavelHash());

        if (precificavelRE == null) {
            LOG.error("Recomendação não encontrada!.");
            return new ResponseEntity<>(new PrecificacaoTesteResponse(), HttpStatus.BAD_REQUEST);
        }

        Precificavel precificavel = precificavelRE.toPrecificavel();
        Pair<ResultadoPrecificacaoView, List<MotivoExclusaoVoo>> resultadoPrecificacao = this.getResultadoPrecificacaoListPair(precificavelRE, precificavel);

        return new ResponseEntity<>(PrecificacaoTesteResponse.of(resultadoPrecificacao.getLeft(), resultadoPrecificacao.getRight()),
                HttpStatus.OK);
    }

    private Pair<ResultadoPrecificacaoView, List<MotivoExclusaoVoo>> getResultadoPrecificacaoListPair(PrecificavelRE precificavelRE, Precificavel precificavel) {
        if (precificavel.isExcludedByFilter()) {
            return Pair.of(createExcludedResultForFront(precificavel), Collections.singletonList(new MotivoExclusaoVoo(precificavel.getExcludedByFilter())));
        }

        ParametroSessaoPrecificacao sessaoPrecificacao = precificavelRE.getParametroSessaoPrecificacao().toParametroSessaoPrecificacao();
        return precificadorService.precificaComConfiguracaoEspecifica(precificavel, sessaoPrecificacao);
    }

    private ChaveCache montaChaveCacheSessao(String correlationID, String hashVoo) {
        return new ChaveCache(RegistroVooExcluido.class.getSimpleName(), correlationID, hashVoo);
    }

}

package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.configuracaoRc.agencia;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoRcAgenciaRecord;
import br.tur.reservafacil.dominio.Agencia;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRc;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcAgencia;
import org.jooq.RecordMapper;
import org.springframework.stereotype.Component;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
@Component
public class ConfiguracaoRcAgenciaRecord2ConfiguracaoRcAgenciaMapper
		implements RecordMapper<ConfiguracaoRcAgenciaRecord, ConfiguracaoRcAgencia> {

    @Override
    public ConfiguracaoRcAgencia map(ConfiguracaoRcAgenciaRecord record) {
        ConfiguracaoRcAgencia configuracaoRcAgencia = new ConfiguracaoRcAgencia();
	configuracaoRcAgencia.setId(record.getId());
        configuracaoRcAgencia.setAtivo(record.getAtivo());

        Agencia agencia = new Agencia();
        agencia.setId(record.getAgenciaId());
        configuracaoRcAgencia.setAgencia(agencia);

        ConfiguracaoRc configuracaoRc = new ConfiguracaoRc();
        configuracaoRc.setId(record.getConfiguracaoRcId());
        configuracaoRcAgencia.setConfiguracaoRc(configuracaoRc);

	return configuracaoRcAgencia;
    }

}

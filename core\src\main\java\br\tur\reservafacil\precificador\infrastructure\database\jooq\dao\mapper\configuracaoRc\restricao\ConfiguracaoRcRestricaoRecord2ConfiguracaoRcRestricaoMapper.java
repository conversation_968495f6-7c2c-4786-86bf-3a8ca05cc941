package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.configuracaoRc.restricao;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoRcRestricaoRecord;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRc;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcRestricao;
import br.tur.reservafacil.dominio.aereo.Restricao;
import org.jooq.RecordMapper;
import org.springframework.stereotype.Component;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
@Component
public class ConfiguracaoRcRestricaoRecord2ConfiguracaoRcRestricaoMapper
		implements RecordMapper<ConfiguracaoRcRestricaoRecord, ConfiguracaoRcRestricao> {

    @Override
    public ConfiguracaoRcRestricao map(ConfiguracaoRcRestricaoRecord record) {
        ConfiguracaoRcRestricao configuracaoPrecificacaoRestricao = new ConfiguracaoRcRestricao();
	configuracaoPrecificacaoRestricao.setId(record.getId());
	configuracaoPrecificacaoRestricao.setRestricao(this.getCiaAereaLazy(record.getRestricaoId()));
	configuracaoPrecificacaoRestricao.setRestringivel(this.getConfiguracaoLazy(record.getConfiguracaoRcId()));
	return configuracaoPrecificacaoRestricao;
    }

    private ConfiguracaoRc getConfiguracaoLazy(Integer configuracaoId) {
	if (configuracaoId != null) {
	    ConfiguracaoRc configuracaoPrecificacao = new ConfiguracaoRc();
	    configuracaoPrecificacao.setId(configuracaoId);
	    return configuracaoPrecificacao;
	}
	return null;
    }

    private Restricao getCiaAereaLazy(Integer retricaoId) {
	if (retricaoId != null) {
	    Restricao restricao = new Restricao();
	    restricao.setId(retricaoId);
	    return restricao;
	}
	return null;
    }

}

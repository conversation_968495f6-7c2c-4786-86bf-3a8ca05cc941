package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.configuracaoRc.restricao;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoRcRestricaoRecord;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcRestricao;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.CustomMapper;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON><PERSON> on 20/08/25 - GA-12251.
 */
@Component
public class ConfiguracaoRcRestricao2ConfiguracaoRcRestricaoRecordMapper
		implements CustomMapper<ConfiguracaoRcRestricao, ConfiguracaoRcRestricaoRecord> {

    @Override
    public ConfiguracaoRcRestricaoRecord map(ConfiguracaoRcRestricao input, ConfiguracaoRcRestricaoRecord output) {
	if (input.getId() != null) {
	    output.setId(input.getId());
	}
	output.setRestricaoId(input.getRestricao() != null ? input.getRestricao().getId() : null);
	output.setConfiguracaoRcId(input.getRestringivel() != null ? input.getRestringivel().getId() : null);

	return output;
    }
}

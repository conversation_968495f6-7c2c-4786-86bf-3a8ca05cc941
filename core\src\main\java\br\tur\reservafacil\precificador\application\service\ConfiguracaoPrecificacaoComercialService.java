package br.tur.reservafacil.precificador.application.service;

import br.tur.reservafacil.dominio.Produto;
import br.tur.reservafacil.dominio.aereo.CiaAerea;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoPrecificacao;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoPrecificacaoComercial;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoPrecificacaoComercialProduto;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoPrecificacaoComercialReport;
import br.tur.reservafacil.dominio.aereo.FiltroConfiguracao;
import br.tur.reservafacil.dominio.aereo.Restricao;
import br.tur.reservafacil.dominio.aereo.v0.to.common.PrecificacaoComercialTesteResponse;
import br.tur.reservafacil.dominio.aereo.voosExcluidos.MotivoExclusaoVoo;
import br.tur.reservafacil.dominio.intermediario.v0.to.precificacao.ParametroSessaoPrecificacao;
import br.tur.reservafacil.dominio.intermediario.v0.to.precificacao.Precificavel;
import br.tur.reservafacil.dominio.tipo.TipoConfigPrecificacao;
import br.tur.reservafacil.dominio.tipo.TipoOperador;
import br.tur.reservafacil.dominio.tipo.TipoRestricao;
import br.tur.reservafacil.precificador.application.service.cache.ConfiguracaoPrecificacaoComercialRedisCacheComponent;
import br.tur.reservafacil.precificador.domain.component.restricao.RestricaoTreeComponent;
import br.tur.reservafacil.precificador.domain.component.restricao.RestricaoValidatorComponent;
import br.tur.reservafacil.precificador.domain.repository.ConfiguracaoPrecificacaoComercialRepository;
import com.newrelic.api.agent.Trace;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import static br.tur.reservafacil.dominio.tipo.TipoConfigPrecificacao.RESTRITA;

/**
 * Created by paulo on 08/01/18.
 */
@Service
public class ConfiguracaoPrecificacaoComercialService
        extends AbstractService<ConfiguracaoPrecificacaoComercial, Integer, ConfiguracaoPrecificacaoComercialRepository> {

    @Autowired
    private ConfiguracaoPrecificacaoComercialRetriever retriever;

    @Autowired
    private ConfiguracaoPrecificacaoService configuracaoPrecificacaoService;

    @Autowired
    private RestricaoTreeComponent restricaoTreeComponent;

    @Autowired
    private RestricaoService restricaoService;

    /**
     * Busca uma configuração de precificação comercial para o precificavel informado
     *
     * @param precificavel               precificavel
     * @param sessaoPrecificacao         sessaoPrecificacao
     * @param condicaoPrecificacao	 condicaoPrecificacao
     * @return a {@link ConfiguracaoPrecificacaoComercial} a ser utilizada para precificação
     */
    @Trace
    public Optional<ConfiguracaoPrecificacaoComercial> obterConfigPrecificacaoRestritaPorPrecificavel(List<Restricao> restricoes, Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao,
            String condicaoPrecificacao) {
        FiltroConfiguracao filtro = criaFiltroPorPrecificavel(restricoes, precificavel, sessaoPrecificacao, RESTRITA);
        return retriever.getFirstMatchConfiguracaoPrecificacao(filtro, condicaoPrecificacao);
    }

    @Trace
    public Optional<ConfiguracaoPrecificacaoComercial> filterConfiguracaoComercialPorPrecificavel(List<ConfiguracaoPrecificacaoComercial> configsPrecificacaoComercial, List<Restricao> restricoes, Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao,
            String condicaoPrecificacao) {
        FiltroConfiguracao filtro = criaFiltroPorPrecificavel(restricoes, precificavel, sessaoPrecificacao, RESTRITA);
        return retriever.filterConfiguracaoComercialPorPrecificavel(configsPrecificacaoComercial, filtro, condicaoPrecificacao);
    }

    @Trace
    public List<ConfiguracaoPrecificacaoComercial> findByIds(List<Integer> keys) {

        //TODO: Renomear o nome do método do repo. Se é Eager ou Lazy não é preocupação de um Service. O método deve indicar contexto, não implementação
        return this.getRepository().findEagerByKeys(keys);
    }

    public List<ConfiguracaoPrecificacaoComercial> findByKeys(Set<Integer> ids) {
        return this.getRepository().findByKeys(ids);
    }

    private FiltroConfiguracao criaFiltroPorPrecificavel(List<Restricao> restricoes, Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao, TipoConfigPrecificacao tipoConfigPrecificacao) {
        final String codigoCiaViagem = precificavel.getCiaAereaViagem();
        final Integer empresaId = sessaoPrecificacao.getEmpresa().getId();
        final Boolean isBuscaDeTeste = sessaoPrecificacao.isBuscaDeTeste();
        final String produto = precificavel.getProdutoPrecificacao();

        return criarFiltro(codigoCiaViagem, restricoes, empresaId, isBuscaDeTeste, tipoConfigPrecificacao,
                produto);
    }

    private FiltroConfiguracao criarFiltro(String codigoCia, List<Restricao> restricoes, Integer empresaId, Boolean isBuscaDeTeste, TipoConfigPrecificacao tipoConfigPrecificacao, String produto) {
        FiltroConfiguracao filtro = new FiltroConfiguracao();
        filtro.setCodigoCia(codigoCia);
        filtro.setRestricoes(restricoes);
        filtro.setEmpresaId(empresaId);
        filtro.setTipoConfigPrecificacao(tipoConfigPrecificacao);
        filtro.setBuscaDeTeste(isBuscaDeTeste);
        filtro.setProduto(produto);
        return filtro;
    }

    @Trace
    public ConfiguracaoPrecificacaoComercial findEaggerByKey(Integer id) {
        final ConfiguracaoPrecificacaoComercial configuracaoPrecificacao = this.getRepository().findEaggerByKey(id);
        //        configuracaoPrecificacao.setRestricoes(restricaoTreeComponent.desagruparRestricoes(restricaoTreeComponent.gerarArvoreDeRestricoes(configuracaoPrecificacao.getRestricoes())));
        configuracaoPrecificacao.agrupaRestricoes();
        return configuracaoPrecificacao;
    }

    public List<ConfiguracaoPrecificacaoComercialReport> reportConditionByAgencyBranchCompany(String agencyRef, String branchRef, Integer companyId){
        return retriever.reportConditionByAgencyBranchCompany(agencyRef, branchRef, companyId);
    }
    public List<ConfiguracaoPrecificacaoComercialReport> reportConditionByCodigoPrecificacao(String codigoPrecificacao) {
        return retriever.reportConditionByCodigoPrecificacao(codigoPrecificacao);
    }

    public PrecificacaoComercialTesteResponse testaPrecificacaoComercial(Precificavel precificavel, ParametroSessaoPrecificacao parametroSessaoPrecificacao) {
        PrecificacaoComercialTesteResponse response = new PrecificacaoComercialTesteResponse();

        List<Restricao> restricoesByPrecificavel = restricaoService.getRestricoesByPrecificavel(precificavel);
        List<Restricao> restricoesPosPrecificacao = restricaoService.getRestricoesPosPrecificacao(precificavel);
        if (CollectionUtils.isNotEmpty(restricoesPosPrecificacao)) {
            restricoesByPrecificavel.addAll(restricoesPosPrecificacao);
        }

        FiltroConfiguracao filtro = criaFiltroPorPrecificavel(restricoesByPrecificavel, precificavel, parametroSessaoPrecificacao, RESTRITA);
        filtro.setBuscaDeTeste(true);

        String condicaoPrecificacao = null;
        if (StringUtils.isEmpty(precificavel.getCondicaoPrecificacao())) {
            Integer idPrecificacao = precificavel.getIdConfigRestrita();
            if (idPrecificacao == null) {
                List<Integer> ids = Optional.ofNullable(precificavel.getPrecificacaoIds()).orElse(Collections.emptyList());
                idPrecificacao = ids.get(0);
            }
            if (idPrecificacao != null) {
                ConfiguracaoPrecificacao configuracaoPrecificacao = configuracaoPrecificacaoService.findEagerByKey(idPrecificacao);
                if (configuracaoPrecificacao != null && StringUtils.isNotEmpty(configuracaoPrecificacao.getCondicaoPrecificacao())) {
                    condicaoPrecificacao = configuracaoPrecificacao.getCondicaoPrecificacao();
                }
            }
        } else {
            condicaoPrecificacao = precificavel.getCondicaoPrecificacao();
        }
        retriever.getFirstMatchConfiguracaoPrecificacao(filtro, condicaoPrecificacao)
                 .ifPresent(configuracaoPrecificacaoComercial -> response.setConfiguracaoPrecificacaoComercial(configuracaoPrecificacaoComercial));

        List<MotivoExclusaoVoo> motivoExclusaoVoos = retriever.findByFiltroConfiguracao(filtro, condicaoPrecificacao);
        if (CollectionUtils.isNotEmpty(motivoExclusaoVoos)) {
            response.setMotivosExclusaoVoos(motivoExclusaoVoos);
        }

        return response;
    }

    @Trace
    public List<ConfiguracaoPrecificacaoComercial> findByEmpresa(Integer empresaId, boolean isBuscaDeTeste) {
        return retriever.findByEmpresa(empresaId, isBuscaDeTeste);
    }

    /**
     *
     * FIXME: Existe apenas por causa que utilizando apenas Spring AOP, sem AspectJ, o @Cacheable não funciona quando chamando métodos da
     * própria classe. Como a idéia é eventualmente migrar para compile-time weaving com AspectJ, essa classe deve sumir e a classe voltar
     * ao que era antes.
     *
     */
    @Component
    static class ConfiguracaoPrecificacaoComercialRetriever {

        @Autowired
        private RestricaoValidatorComponent restricaoValidatorComponent;

        @Autowired
        private ConfiguracaoPrecificacaoComercialRepositoryRetriever configuracaoPrecificacaoRetriever;

        @Autowired
        private ConfiguracaoPrecificacaoComercialProdutoService configuracaoPrecificacaoComercialProdutoService;

        @Trace
        public List<MotivoExclusaoVoo> findByFiltroConfiguracao(FiltroConfiguracao filtro, String condicaoPrecificacao) {
            final LocalTime time = LocalTime.now();
            int second = time.getSecond();
            String timeToCache = filtro.isBuscaDeTeste() ? ", time=" + DateTimeFormatter.ofPattern("HH:mm:").format(time) + second / 10 : "";

            List<ConfiguracaoPrecificacaoComercial> configsPrecificacao = configuracaoPrecificacaoRetriever.findByCiaTipoConfigEmpresaIdProduto(
                    filtro.getCodigoCia(),
                    filtro.getTipoConfigPrecificacao(),
                    filtro.getEmpresaId(),
                    filtro.getProduto(),
                    filtro.isBuscaDeTeste(),
                    timeToCache, condicaoPrecificacao);

            List<String> agencias = filtro.getRestricoes().stream()
                                          .filter(restricao -> TipoRestricao.AGENCIA.equals(restricao.getTipoRestricao()))
                                          .flatMap(restricao -> Stream.of(restricao.getValor()))
                                          .collect(Collectors.toList());

            List<ConfiguracaoPrecificacaoComercial> configuracoesFiltradas = configsPrecificacao.stream()
                                                                                                .filter(filterAgencia(agencias))
                                                                                                .collect(Collectors.toList());

            List<MotivoExclusaoVoo> motivos = new ArrayList<>();
            final boolean processaAteOFim = true;
            for (ConfiguracaoPrecificacaoComercial configuracaoPrecificacao : configuracoesFiltradas) {
                List<Restricao> restricoes = Collections.synchronizedList(new ArrayList<>());
                restricoes.addAll(configuracaoPrecificacao.getRestricoes());
                restricoes.add(new Restricao(configuracaoPrecificacao.getRentabilidadeMin()
                        + "-"
                        + configuracaoPrecificacao.getRentabilidadeMax(),
                        TipoOperador.DENTRO, TipoRestricao.RENTABILIDADE));

                MotivoExclusaoVoo motivoExclusaoVoo = restricaoValidatorComponent.validaRestricoes(filtro.getRestricoes(), restricoes, processaAteOFim);
                if(motivoExclusaoVoo != null && (CollectionUtils.isNotEmpty(motivoExclusaoVoo.getRestricoesNaoGeradas()) ||
                        CollectionUtils.isNotEmpty(motivoExclusaoVoo.getRestricoesBarradas()))) {
                    motivoExclusaoVoo.setIdConfiguracaoPrecificacao(configuracaoPrecificacao.getId());
                    motivoExclusaoVoo.setNomeConfiguracaoPrecificacao(configuracaoPrecificacao.getNome());
                    motivos.add(motivoExclusaoVoo);
                }
            }

            return motivos;
        }

        @Trace
        public Optional<ConfiguracaoPrecificacaoComercial> getFirstMatchConfiguracaoPrecificacao(FiltroConfiguracao filtro, String condicaoPrecificacao) {
            final LocalTime time = LocalTime.now();
            int second = time.getSecond();
            String timeToCache = filtro.isBuscaDeTeste() ? ", time=" + DateTimeFormatter.ofPattern("HH:mm:").format(time) + second / 10 : "";

            List<ConfiguracaoPrecificacaoComercial> configsPrecificacao = configuracaoPrecificacaoRetriever.findByCiaTipoConfigEmpresaIdProduto(
                    filtro.getCodigoCia(),
                    filtro.getTipoConfigPrecificacao(),
                    filtro.getEmpresaId(),
                    filtro.getProduto(),
                    filtro.isBuscaDeTeste(),
                    timeToCache,
                    condicaoPrecificacao);

            List<String> agencias = filtro.getRestricoes().stream()
                                          .filter(restricao -> TipoRestricao.AGENCIA.equals(restricao.getTipoRestricao()))
                                          .flatMap(restricao -> Stream.of(restricao.getValor()))
                                          .collect(Collectors.toList());

            List<ConfiguracaoPrecificacaoComercial> configuracoesFiltradas = configsPrecificacao.stream()
                                                                                                .filter(filterAgencia(agencias))
                                                                                                .collect(Collectors.toList());

            for (ConfiguracaoPrecificacaoComercial configuracaoPrecificacao : configuracoesFiltradas) {
                List<Restricao> restricoes = Collections.synchronizedList(new ArrayList<>());
                restricoes.addAll(configuracaoPrecificacao.getRestricoes());
                restricoes.add(new Restricao(configuracaoPrecificacao.getRentabilidadeMin()
                        + "-"
                        + configuracaoPrecificacao.getRentabilidadeMax(),
                        TipoOperador.DENTRO, TipoRestricao.RENTABILIDADE));

                boolean isValid = restricaoValidatorComponent.validaRestricoes(filtro.getRestricoes(), restricoes);
                if (isValid) {
                    this.carregaDadosParaPrecificacao(configuracaoPrecificacao);
                    return Optional.ofNullable(configuracaoPrecificacao);
                }
            }
            return Optional.empty();
        }

        private Predicate<ConfiguracaoPrecificacaoComercial> filterAgencia(List<String> agencias) {
            return confComercial -> CollectionUtils.isEmpty(confComercial.getAgencias()) ||
                    confComercial.getAgencias().stream().anyMatch(agencia -> agencia != null && Objects.nonNull(agencia.getId()) && CollectionUtils.isNotEmpty(agencias) && agencias.contains(agencia.getId().toString()));
        }

        /**
         * Carrega as informações necessárias para precificação, como, Markup e Exceção.
         *
         * @param configuracaoPrecificacaoComercial configuracaoPrecificacaoComercial
         */
        @Trace
        @Cacheable(cacheNames = "ConfiguracaoPrecificacaoComercialService::carregaDadosParaPrecificacao", cacheManager = "ehCacheManager")
        private void carregaDadosParaPrecificacao(ConfiguracaoPrecificacaoComercial configuracaoPrecificacaoComercial) {
            final Integer idConfiguracao = configuracaoPrecificacaoComercial.getId();
            configuracaoPrecificacaoComercial.setProdutos(this.configuracaoPrecificacaoComercialProdutoService.findByConfigPrecificacaoComercialId(idConfiguracao));
            //configuracaoPrecificacao.setExcecoes(this.excecaoService.findByConfiguracaoId(idConfiguracao));
        }

        @Cacheable(cacheNames = "ConfiguracaoPrecificacaoComercialService::reportConditionByAgencyBranchCompany", cacheManager = "ehCacheManager")
        public List<ConfiguracaoPrecificacaoComercialReport> reportConditionByAgencyBranchCompany(String agencyRef, String branchRef, Integer companyId) {
            return this.configuracaoPrecificacaoRetriever.reportConditionByAgencyBranchCompany(agencyRef, branchRef, companyId);
        }
        @Cacheable(cacheNames = "ConfiguracaoPrecificacaoComercialService::reportConditionByCodigoPrecificacao", cacheManager = "ehCacheManager")
        public List<ConfiguracaoPrecificacaoComercialReport> reportConditionByCodigoPrecificacao(String codigoPrecificacao) {
            return this.configuracaoPrecificacaoRetriever.reportConditionByCodigoPrecificacao(codigoPrecificacao);
        }

        public List<ConfiguracaoPrecificacaoComercial> findByEmpresa(Integer empresaId, boolean isBuscaDeTeste) {
            return configuracaoPrecificacaoRetriever.findByEmpresa(empresaId, isBuscaDeTeste);
        }

        public Optional<ConfiguracaoPrecificacaoComercial> filterConfiguracaoComercialPorPrecificavel(List<ConfiguracaoPrecificacaoComercial> configsPrecificacaoComercial, FiltroConfiguracao filtro, String condicaoPrecificacao) {
            List<String> agencias = filtro.getRestricoes().stream()
                                          .filter(restricao -> TipoRestricao.AGENCIA.equals(restricao.getTipoRestricao()))
                                          .filter(Objects::nonNull)
                                          .flatMap(restricao -> Stream.of(restricao.getValor()))
                                          .collect(Collectors.toList());

            List<ConfiguracaoPrecificacaoComercial> configuracoesFiltradas = configsPrecificacaoComercial.stream()
                                                                                                         .filter(f -> StringUtils.isBlank(condicaoPrecificacao) || condicaoPrecificacao.equalsIgnoreCase(f.getCodigoPrecificacaoComercial()))
                                                                                                         .filter(f -> CollectionUtils.isNotEmpty(f.getCiasAereas()) && f.getCiasAereas().stream().map(CiaAerea::getCodigo).collect(Collectors.toList()).contains(filtro.getCodigoCia()))
                                                                                                         .filter(f -> CollectionUtils.isNotEmpty(f.getProdutos()) && f.getProdutos().stream().map(ConfiguracaoPrecificacaoComercialProduto::getProduto).map(Produto::getNome).collect(Collectors.toList()).contains(filtro.getProduto()))
                                                                                                         .filter(filterAgencia(agencias))
                                                                                                         .collect(Collectors.toList());

            for (ConfiguracaoPrecificacaoComercial configuracaoPrecificacao : configuracoesFiltradas) {
                List<Restricao> restricoes = Collections.synchronizedList(new ArrayList<>());
                restricoes.addAll(configuracaoPrecificacao.getRestricoes());
                restricoes.add(new Restricao(configuracaoPrecificacao.getRentabilidadeMin()
                        + "-"
                        + configuracaoPrecificacao.getRentabilidadeMax(),
                        TipoOperador.DENTRO, TipoRestricao.RENTABILIDADE));

                boolean isValid = restricaoValidatorComponent.validaRestricoes(filtro.getRestricoes(), restricoes);
                if (isValid) {
                    this.carregaDadosParaPrecificacao(configuracaoPrecificacao);
                    return Optional.of(configuracaoPrecificacao);
                }
            }
            return Optional.empty();
        }
    }

    /**
     *
     * FIXME: Existe apenas por causa que utilizando apenas Spring AOP, sem AspectJ, o @Cacheable não funciona quando chamando métodos da
     * própria classe. Como a idéia é eventualmente migrar para compile-time weaving com AspectJ, essa classe deve sumir e a classe voltar
     * ao que era antes.
     *
     */
    @Component
    static class ConfiguracaoPrecificacaoComercialRepositoryRetriever {

        @Autowired
        private ConfiguracaoPrecificacaoComercialRepository repository;

        @Autowired ConfiguracaoPrecificacaoComercialRedisCacheComponent configuracaoPrecificacaoComercialRedisCacheComponent;

        @Trace
        @Cacheable(cacheNames = "ConfiguracaoPrecificacaoComercialService::findByCiaTipoConfigEmpresaIdProduto", cacheManager = "ehCacheManager", sync = true) // sync = true TODO
        public List<ConfiguracaoPrecificacaoComercial> findByCiaTipoConfigEmpresaIdProduto(String codCia, TipoConfigPrecificacao tipoConfigPrecificacao,
                Integer empresaId, String produto, Boolean isBuscaDeTeste, String timeToCache,
                String condicaoPrecificacao) {
            return configuracaoPrecificacaoComercialRedisCacheComponent.findByCiaTipoConfigEmpresaIdProduto(codCia, tipoConfigPrecificacao, empresaId, produto, isBuscaDeTeste, condicaoPrecificacao);
        }

        @Trace
        @Cacheable(cacheNames = "ConfiguracaoPrecificacaoComercialService::reportConditionByAgencyBranchCompany", cacheManager = "ehCacheManager", sync = true)
        public List<ConfiguracaoPrecificacaoComercialReport> reportConditionByAgencyBranchCompany(String agencyRef, String branchRef, Integer companyId) {
            return configuracaoPrecificacaoComercialRedisCacheComponent.reportConditionByAgencyBranchCompany(agencyRef, branchRef, companyId);
        }
        @Trace
        @Cacheable(cacheNames = "ConfiguracaoPrecificacaoComercialService::reportConditionByCodigoPrecificacao", cacheManager = "ehCacheManager", sync = true)
        public List<ConfiguracaoPrecificacaoComercialReport> reportConditionByCodigoPrecificacao(String codigoPrecificacao) {
            return configuracaoPrecificacaoComercialRedisCacheComponent.reportConditionByCodigoPrecificacao(codigoPrecificacao);
        }

        @Trace
        @Cacheable(cacheNames = "ConfiguracaoPrecificacaoComercialService::findByEmpresa", cacheManager = "ehCacheManager", sync = true) // sync = true TODO
        public List<ConfiguracaoPrecificacaoComercial> findByEmpresa(Integer empresaId, boolean isBuscaDeTeste) {
            return configuracaoPrecificacaoComercialRedisCacheComponent.findByEmpresa(empresaId, isBuscaDeTeste);
        }
    }
}

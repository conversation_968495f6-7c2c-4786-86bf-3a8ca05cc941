package br.tur.reservafacil.dominio.aereo;

import br.tur.reservafacil.dominio.Empresa;
import br.tur.reservafacil.dominio.Grupo;
import br.tur.reservafacil.dominio.Produto;
import br.tur.reservafacil.dominio.aereo.interfaces.Identificavel;
import br.tur.reservafacil.dominio.aereo.interfaces.Versionavel;
import br.tur.reservafacil.dominio.tipo.NacInt;
import br.tur.reservafacil.dominio.tipo.StatusPublicacao;
import br.tur.reservafacil.dominio.tipo.TipoConfigRav;
import br.tur.reservafacil.dominio.tipo.TipoTarifaAcordo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Created by <PERSON><PERSON><PERSON> on 6/30/16.
 */
public class ConfiguracaoRav implements Serializable, Identificavel<Integer>, Versionavel<Integer> {

    private static final long serialVersionUID = 1845123117259865741L;

    public static final ConfiguracaoRav UNDEF = new ConfiguracaoRav();

    private Integer id;

    private String nome;

    private BigDecimal valorMinimo;

    private BigDecimal percentualRav;

    private List<Empresa> empresas = new ArrayList<>();

    private List<Produto> produtos = new ArrayList<>();

    private List<CiaAerea> ciasAereas = new ArrayList<>();

    private TipoTarifaAcordo tipoTarifaAcordo;

    private NacInt nacInt;

    private Boolean ativo = Boolean.TRUE;

    private Integer          versao           = 0;
    private Boolean          editavel         = Boolean.TRUE;
    private StatusPublicacao statusPublicacao = StatusPublicacao.NAO_PUBLICADA;
    private Integer          idOrigem;
    private Integer          idEditavel;
    private Integer          prioridade;

    private List<Grupo> grupos = new ArrayList<>();

    private TipoConfigRav tipoConfigRav;

    public Integer getId() {
	return id;
    }

    public void setId(Integer id) {
	this.id = id;
    }

    public String getNome() {
	return nome;
    }

    public void setNome(String nome) {
	this.nome = nome;
    }

    public BigDecimal getValorMinimo() {
	return valorMinimo;
    }

    public void setValorMinimo(BigDecimal valorMinimo) {
	this.valorMinimo = valorMinimo;
    }

    /**
     *
     * @return o fator de multiplicação. Ex.: 0.07 = 7 %
     */
    public BigDecimal getPercentualRav() {
	return percentualRav;
    }

    public void setPercentualRav(BigDecimal percentualRav) {
	this.percentualRav = percentualRav;
    }

    public List<Empresa> getEmpresas() {
	return empresas;
    }

    public void setEmpresas(List<Empresa> empresas) {
	this.empresas = empresas;
    }

    public List<CiaAerea> getCiasAereas() {
	return ciasAereas;
    }

    public void addCiaAerea(CiaAerea ciaAerea) {
	this.ciasAereas.add(ciaAerea);
    }

    public void setCiasAereas(List<CiaAerea> ciasAereas) {
	this.ciasAereas = ciasAereas;
    }

    public List<Produto> getProdutos() {
	return produtos;
    }

    public void setProdutos(List<Produto> produtos) {
	this.produtos = produtos;
    }

    public TipoTarifaAcordo getTipoTarifaAcordo() {
	return tipoTarifaAcordo;
    }

    public void setTipoTarifaAcordo(TipoTarifaAcordo tipoTarifaAcordo) {
	this.tipoTarifaAcordo = tipoTarifaAcordo;
    }

    public NacInt getNacInt() {
	return nacInt;
    }

    public void setNacInt(NacInt nacInt) {
	this.nacInt = nacInt;
    }

    public Boolean getAtivo() {
	return ativo;
    }

    public void setAtivo(Boolean ativo) {
	this.ativo = ativo;
    }

    public List<Grupo> getGrupos() {
	return grupos;
    }

    public void setGrupos(List<Grupo> grupos) {
	this.grupos = grupos;
    }

    public TipoConfigRav getTipoConfigRav() {
	return tipoConfigRav;
    }

    public void setTipoConfigRav(TipoConfigRav tipoConfigRav) {
	this.tipoConfigRav = tipoConfigRav;
    }

    public Integer getPrioridade() {
	return prioridade;
    }

    public void setPrioridade(Integer prioridade) {
	this.prioridade = prioridade;
    }

    @Override
    public boolean equals(Object o) {
	if (this == o)
	    return true;
	if (o == null || getClass() != o.getClass())
	    return false;
	ConfiguracaoRav that = (ConfiguracaoRav)o;
	return Objects.equals(id, that.id) &&
			Objects.equals(nome, that.nome) &&
			Objects.equals(valorMinimo, that.valorMinimo) &&
			Objects.equals(percentualRav, that.percentualRav) &&
			Objects.equals(empresas, that.empresas) &&
			Objects.equals(produtos, that.produtos) &&
			Objects.equals(ciasAereas, that.ciasAereas) &&
			nacInt == that.nacInt &&
			tipoTarifaAcordo == that.tipoTarifaAcordo &&
			Objects.equals(tipoConfigRav, that.tipoConfigRav) &&
			Objects.equals(grupos, that.grupos) &&
			Objects.equals(prioridade, that.prioridade);
    }

    @Override
    public int hashCode() {
	return Objects.hash(id, nome, valorMinimo, percentualRav, empresas, produtos, ciasAereas, tipoTarifaAcordo, nacInt, tipoConfigRav, grupos, prioridade);
    }

    @Override
    public String toString() {
	return "ConfiguracaoRav{" +
			"id=" + id +
			", nome='" + nome + '\'' +
			", valorMinimo=" + valorMinimo +
			", percentualRav=" + percentualRav +
			", empresas=" + empresas +
			", produtos=" + produtos +
			", ciasAereas=" + ciasAereas +
			", nacInt=" + nacInt +
			", tipoTarifaAcordo=" + tipoTarifaAcordo +
			", tipoConfigRav=" + tipoConfigRav +
			", grupos=" + grupos +
			", prioridade=" + prioridade +
			'}';
    }

    @Override public void setEditavel(Boolean editavel) {
	this.editavel = editavel;
    }

    @Override public Boolean isEditavel() {
	return editavel;
    }

    @Override public Integer getVersao() {
	return versao;
    }

    @Override public void setVersao(Integer versao) {
	this.versao = versao;
    }

    @Override public Integer getIdOrigem() {
	return idOrigem != null ? idOrigem : id;
    }

    @Override public void setIdOrigem(Integer idOrigem) {
	this.idOrigem = idOrigem;
    }

    @Override public boolean isConfiguracaoOrigem() {
	if(this.getIdOrigem() != null) {
	    return this.getIdOrigem().equals(id);
	}
	return true;
    }

    @Override public StatusPublicacao getStatusPublicacao() {
	return statusPublicacao;
    }

    @Override public void setStatusPublicacao(StatusPublicacao statusPublicacao) {
	this.statusPublicacao = statusPublicacao;
    }
}

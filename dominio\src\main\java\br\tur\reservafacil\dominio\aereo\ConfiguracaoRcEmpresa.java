package br.tur.reservafacil.dominio.aereo;

import br.tur.reservafacil.dominio.Empresa;
import br.tur.reservafacil.dominio.aereo.interfaces.Identificavel;

import java.io.Serializable;
import java.util.Objects;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
public class ConfiguracaoRcEmpresa
		implements Serializable, Identificavel<Integer> {

    private static final long serialVersionUID = -1194032517724864944L;

    private Integer id;

    private ConfiguracaoRc configuracaoRc;

    private Empresa empresa;

    private Boolean ativo = Boolean.TRUE;

    public ConfiguracaoRcEmpresa() {
    }

    public ConfiguracaoRcEmpresa(Integer id, ConfiguracaoRc configuracaoRc, Empresa empresa) {
	this.id = id;
	this.configuracaoRc = configuracaoRc;
	this.empresa = empresa;
    }

    public Integer getId() {
	return id;
    }

    public void setId(Integer id) {
	this.id = id;
    }

    public ConfiguracaoRc getConfiguracaoRc() {
	return configuracaoRc;
    }

    public void setConfiguracaoRc(ConfiguracaoRc configuracaoRc) {
	this.configuracaoRc = configuracaoRc;
    }

    public Empresa getEmpresa() {
	return empresa;
    }

    public void setEmpresa(Empresa empresa) {
	this.empresa = empresa;
    }

    public Boolean getAtivo() {
	return ativo;
    }

    public void setAtivo(Boolean ativo) {
	this.ativo = ativo;
    }

    @Override
    public String toString() {
	return "ConfiguracaoRcEmpresa{" +
			"id=" + id +
			", configuracaoRc=" + configuracaoRc +
			", empresa=" + empresa +
			'}';
    }

    @Override
    public boolean equals(Object o) {
	if (this == o)
	    return true;
	if (o == null || getClass() != o.getClass())
	    return false;
	ConfiguracaoRcEmpresa that = (ConfiguracaoRcEmpresa)o;
	return Objects.equals(id, that.id) &&
			Objects.equals(configuracaoRc, that.configuracaoRc) &&
			Objects.equals(empresa, that.empresa);
    }

    @Override
    public int hashCode() {
	return Objects.hash(id, configuracaoRc, empresa);
    }
}

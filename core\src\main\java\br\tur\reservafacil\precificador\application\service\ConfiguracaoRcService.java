package br.tur.reservafacil.precificador.application.service;

import br.tur.reservafacil.dominio.Empresa;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRc;
import br.tur.reservafacil.dominio.aereo.Restricao;
import br.tur.reservafacil.dominio.intermediario.v0.to.precificacao.TipoPrecificavel;
import br.tur.reservafacil.precificador.application.service.cache.ConfiguracaoRcRedisCacheComponent;
import br.tur.reservafacil.precificador.domain.component.restricao.RestricaoTreeComponent;
import br.tur.reservafacil.precificador.domain.component.restricao.RestricaoValidatorComponent;
import br.tur.reservafacil.precificador.domain.repository.ConfiguracaoRcRepository;
import com.newrelic.api.agent.Trace;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Objects.nonNull;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
@Service
public class ConfiguracaoRcService
        extends AbstractService<ConfiguracaoRc, Integer, ConfiguracaoRcRepository> {

    @Autowired
    private ConfiguracaoRcRetriever retriever;

    @Autowired
    private ConfiguracaoPrecificacaoService configuracaoPrecificacaoService;

    @Autowired
    private RestricaoTreeComponent restricaoTreeComponent;

    @Autowired
    private RestricaoService restricaoService;

    @Autowired
    private RestricaoValidatorComponent restricaoValidatorComponent;

    public ConfiguracaoRc findById(Integer configRcId) {
        return retriever.findById(configRcId);
    }

    private static String CODIGO_RC_SICA_REEMISSAO = "RC_REI";

    @Trace
    public Optional<ConfiguracaoRc> filterConfiguracaoRcPorPrecificavel(List<ConfiguracaoRc> configsRc, List<Restricao> restricoesRec, Empresa empresa, Long agenciaId, String codigoCia, String produto, TipoPrecificavel tipoPrecificavel) {
        List<ConfiguracaoRc> configuracoesFiltradas = configsRc.stream()
                .filter(config -> CollectionUtils.isEmpty(config.getAgencias()) || (nonNull(agenciaId) && CollectionUtils.isNotEmpty(config.getAgencias()) && config.getAgencias().stream().anyMatch(agencia -> agencia != null && Objects.nonNull(agencia.getId()) && agencia.getId() == agenciaId.intValue())))
                .filter(config -> nonNull(empresa) && CollectionUtils.isNotEmpty(config.getEmpresas()) && config.getEmpresas().stream().anyMatch(emp -> Objects.equals(empresa.getId(), emp.getId())))
                .filter(config -> nonNull(produto) && CollectionUtils.isNotEmpty(config.getConfiguracaoRcProdutos()) && config.getConfiguracaoRcProdutos().stream().anyMatch(prod -> nonNull(prod.getProduto()) && produto.equalsIgnoreCase(prod.getProduto().getNome())))
                .filter(config -> CollectionUtils.isEmpty(config.getCiasAereas()) || (nonNull(codigoCia) && CollectionUtils.isNotEmpty(config.getCiasAereas()) && config.getCiasAereas().stream().anyMatch(cia -> codigoCia.equalsIgnoreCase(cia.getCodigo()) || cia.getId() == 0)))
                .collect(Collectors.toList());

        for (ConfiguracaoRc configuracaoRc : configuracoesFiltradas) {

            boolean isConfigRCReemissao = configuracaoRc.getConfiguracaoRcProdutos().stream().anyMatch(configuracaoRcProduto -> CODIGO_RC_SICA_REEMISSAO.equalsIgnoreCase(configuracaoRcProduto.getCodigoRc()));
            if ((TipoPrecificavel.REEMISSAO.equals(tipoPrecificavel) && !isConfigRCReemissao) || (!TipoPrecificavel.REEMISSAO.equals(tipoPrecificavel) && isConfigRCReemissao)) {
                continue;
            }

            List<Restricao> restricoesConfigSync = Collections.synchronizedList(new ArrayList<>());
            restricoesConfigSync.addAll(configuracaoRc.getRestricoes());

            boolean isValid = restricaoValidatorComponent.validaRestricoes(restricoesRec, restricoesConfigSync);
            if (isValid) {
                return Optional.of(configuracaoRc);
            }
        }
        return Optional.empty();
    }

    @Trace
    public List<ConfiguracaoRc> findByEmpresa(Integer empresaId, boolean isBuscaDeTeste) {
        return retriever.findByEmpresa(empresaId, isBuscaDeTeste);
    }

    /**
     *
     * FIXME: Existe apenas por causa que utilizando apenas Spring AOP, sem AspectJ, o @Cacheable não funciona quando chamando métodos da
     * própria classe. Como a idéia é eventualmente migrar para compile-time weaving com AspectJ, essa classe deve sumir e a classe voltar
     * ao que era antes.
     *
     */
    @Component
    static class ConfiguracaoRcRetriever {

        @Autowired
        private ConfiguracaoRcRepositoryRetriever configuracaoRcRetriever;

        public List<ConfiguracaoRc> findByEmpresa(Integer empresaId, boolean isBuscaDeTeste) {
            final LocalTime time = LocalTime.now();
            int second = time.getSecond();
            String timeToCache = isBuscaDeTeste ? ", time=" + DateTimeFormatter.ofPattern("HH:mm:").format(time) + second / 10 : "";
            return configuracaoRcRetriever.findByEmpresa(empresaId, isBuscaDeTeste, timeToCache);
        }

        public ConfiguracaoRc findById(Integer configRcId) {
            return configuracaoRcRetriever.findById(configRcId);
        }

    }

    /**
     * FIXME: Existe apenas por causa que utilizando apenas Spring AOP, sem AspectJ, o @Cacheable não funciona quando chamando métodos da
     * própria classe. Como a idéia é eventualmente migrar para compile-time weaving com AspectJ, essa classe deve sumir e a classe voltar
     * ao que era antes.
     */
    @Component
    static class ConfiguracaoRcRepositoryRetriever {

        @Autowired ConfiguracaoRcRedisCacheComponent configuracaoRcRedisCacheComponent;

        @Trace
        @Cacheable(cacheNames = "ConfiguracaoRcService::findByEmpresa", cacheManager = "ehCacheManager", sync = true) // sync = true TODO
        public List<ConfiguracaoRc> findByEmpresa(Integer empresaId, boolean isBuscaDeTeste, String timeToCache) {
            return configuracaoRcRedisCacheComponent.findByEmpresa(empresaId, isBuscaDeTeste, timeToCache);
        }

        @Trace
        @Cacheable(cacheNames = "ConfiguracaoRcService::findById", cacheManager = "ehCacheManager", sync = true) // sync = true TODO
        public ConfiguracaoRc findById(Integer configRcId) {
            return configuracaoRcRedisCacheComponent.findById(configRcId);
        }
    }
}

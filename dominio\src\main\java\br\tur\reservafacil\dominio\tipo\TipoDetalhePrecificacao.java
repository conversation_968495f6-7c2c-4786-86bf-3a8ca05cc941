package br.tur.reservafacil.dominio.tipo;

public enum TipoDetalhePrecificacao {
    ID_CONF_BUSCA("ID_SRC", "Id da configuração de busca"),
    ID_CONF_PRECIFICACAO("ID_PREC", "Id da configuração de precificação"),
    ID_CONF_PRECIFICACAO_COMERCIAL("ID_COM", "Id da configuração de precificação comercial"),
    CD_PRECIFICACAO("CD_PRECIFICACAO", "Código da precificação"),
    CD_PRECIFICACAO_COMERCIAL("CD_PRECIFICACAO", "Código da precificação comercial"),
    ID_CONF_RAV("ID_PREC_RAV", "Id da configuração de rav"),
    ID_CONF_FEE("ID_PREC_FEE", "Id da configuração de fee"),
    ID_CONF_DU("ID_PREC_DU", "Id da configuração de du");

    private String            codigo;
    private String            descricao;

    TipoDetalhePrecificacao(String codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static TipoDetalhePrecificacao getByCodigo(String codigo) {
	for (TipoDetalhePrecificacao tipo : TipoDetalhePrecificacao.values()) {
	    if (tipo.getCodigo().equalsIgnoreCase(codigo)) {
		return tipo;
	    }
	}

	throw new IllegalArgumentException("Não existe o TipoDetalhePrecificacao com o código - " + codigo);
    }

    public String getCodigo() {
	return codigo;
    }

    public String getDescricao() {
	return descricao;
    }
}

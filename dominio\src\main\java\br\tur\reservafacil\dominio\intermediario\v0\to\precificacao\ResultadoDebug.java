package br.tur.reservafacil.dominio.intermediario.v0.to.precificacao;

import br.tur.reservafacil.dominio.aereo.ConfiguracaoPrecificacao;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoPrecificacaoComercial;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRav;
import br.tur.reservafacil.dominio.aereo.Restricao;
import br.tur.reservafacil.dominio.aereo.configuracaofee.ConfiguracaoFee;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by fepelichero on 19/11/2018.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor(staticName = "of")
public class ResultadoDebug {

    private String depuracaoHash;
    private boolean excluido = false;
    private List<Restricao>                                         restricoesGeradas;
    private List<ConfiguracaoPrecificacao>                          configuracoesPrecificacao;
    private List<ConfiguracaoPrecificacaoComMotivoExclusao>         configuracoesEMotivos;
    private List<ConfiguracaoFeeComMotivoExclusao>                  configuracoesFeeEMotivos;
    private ConfiguracaoFee                                         configuracaoFee;
    private ConfiguracaoRav                                         configuracaoRav;
    private ConfiguracaoPrecificacaoComercial                       configuracaoPrecificacaoComercial;

}

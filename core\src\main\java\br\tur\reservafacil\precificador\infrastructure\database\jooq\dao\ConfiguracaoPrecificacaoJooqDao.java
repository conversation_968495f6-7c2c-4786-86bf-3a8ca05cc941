package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoPrecificacaoRecord;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoPrecificacao;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoPrecificacaoIsaCondicao;
import br.tur.reservafacil.dominio.aereo.tipo.OrigemNotificacao;
import br.tur.reservafacil.dominio.tipo.TipoConfigPrecificacao;
import br.tur.reservafacil.dominio.tipo.TipoMarkup;
import br.tur.reservafacil.dominio.tipo.TipoOperador;
import br.tur.reservafacil.dominio.tipo.TipoRestricao;
import br.tur.reservafacil.precificador.domain.repository.*;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.ConfiguracaoPrecificacaoComRestricaoEAcordoMapper;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.ConfiguracaoPrecificacaoComRestricaoMapper;
import com.newrelic.api.agent.Trace;
import org.jooq.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Stream;

import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.Tables.CONFIGURACAO_COMERCIAL_PRECIFICACAO;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.AcordoComercial.ACORDO_COMERCIAL;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.CiaAerea.CIA_AEREA;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.ConfiguracaoPrecificacao.CONFIGURACAO_PRECIFICACAO;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.ConfiguracaoPrecificacaoCiaAerea.CONFIGURACAO_PRECIFICACAO_CIA_AEREA;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.ConfiguracaoPrecificacaoEmpresa.CONFIGURACAO_PRECIFICACAO_EMPRESA;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.ConfiguracaoPrecificacaoRestricao.CONFIGURACAO_PRECIFICACAO_RESTRICAO;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.Empresa.EMPRESA;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.MarkupProduto.MARKUP_PRODUTO;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.Notificacao.NOTIFICACAO;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.Produto.PRODUTO;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.Restricao.RESTRICAO;
import static br.tur.reservafacil.dominio.tipo.StatusPublicacao.*;

/**
 * Created by davidson on 6/9/16.
 */
@Repository
public class ConfiguracaoPrecificacaoJooqDao extends AbstractJooqDao<ConfiguracaoPrecificacaoRecord, ConfiguracaoPrecificacao, Integer>
		implements ConfiguracaoPrecificacaoRepository {

    private static final String PARAM_CIA = "ciaAerea.nome";
    private static final String PARAM_PRODUTO = "produto";
    private static final String PARAM_STATUS           = "status";
    private static final String PARAM_EMPRESA                = "empresa";
    private static final String PARAM_EDITAVEL               = "editavel";
    private static final String PARAM_NOME                   = "nome";
    private static final String PARAM_TIPO_CONFIG            = "tipoConfigPrecificacao";
    private static final String PARAM_ATIVO                  = "ativo";
    private static final String DEFAULT_FIELD_ORDER_BY        = "nome";
    private static final String PARAM_AGENCIA = "agencia";
    private static final String PARAM_GRUPO = "grupo";
    private static final String TODAS_AS_CIAS = "*";
	private static final String TIPO_MARKUP_LIMITE = "LIMITE";

	@Autowired
    private ConfiguracaoPrecificacaoComRestricaoMapper eagerMapper;

    @Autowired
    private ConfiguracaoPrecificacaoComRestricaoEAcordoMapper configPrecificacaoRestricaoEAcordoMapper;

    @Autowired
    private MarkupProdutoRepository markupProdutoRepository;

    @Autowired
    private ExcecaoRepository excecaoRepository;

    @Autowired
    private CiaAereaRepository ciaAereaRepository;

    @Autowired
    private EmpresaRepository empresaRepository;

    @Autowired
    public ConfiguracaoPrecificacaoJooqDao(Configuration configuration, DSLContext context) {
	super(CONFIGURACAO_PRECIFICACAO, ConfiguracaoPrecificacao.class, configuration, context);
    }

    @Override
    protected Integer getId(ConfiguracaoPrecificacao configuracao) {
	return configuracao.getId();
    }

    @Override
    @Trace
    public List<ConfiguracaoPrecificacao> buscaSoComRestricaoByCiaTipoConfigEmpresaIdProduto(String codigoCia, TipoConfigPrecificacao tipoConfigPrecificacao,
											     Integer empresaId, String produto, Boolean isBuscaDeTeste, Boolean markupDinamicoFirst) {

        Field[] columns = Stream.concat(Stream.of(CONFIGURACAO_PRECIFICACAO.fields()), Stream.of(RESTRICAO.fields())).toArray(Field[]::new);
        SelectOnConditionStep<Record> joins = this.getContext().select(columns)
		        .from(CONFIGURACAO_PRECIFICACAO)
		        .leftJoin(CONFIGURACAO_PRECIFICACAO_RESTRICAO).on(CONFIGURACAO_PRECIFICACAO.ID.eq(CONFIGURACAO_PRECIFICACAO_RESTRICAO.CONFIGURACAO_PRECIFICACAO_ID))
		        .leftJoin(RESTRICAO).on(CONFIGURACAO_PRECIFICACAO_RESTRICAO.RESTRICAO_ID.eq(RESTRICAO.ID))
		        .join(CONFIGURACAO_PRECIFICACAO_CIA_AEREA).on(CONFIGURACAO_PRECIFICACAO_CIA_AEREA.CONFIGURACAO_PRECIFICACAO_ID.eq(CONFIGURACAO_PRECIFICACAO.ID))
		        .join(CIA_AEREA).on(CONFIGURACAO_PRECIFICACAO_CIA_AEREA.CIA_AEREA_ID.eq(CIA_AEREA.ID))
		        .join(CONFIGURACAO_PRECIFICACAO_EMPRESA).on(CONFIGURACAO_PRECIFICACAO_EMPRESA.CONFIGURACAO_PRECIFICACAO_ID.eq(CONFIGURACAO_PRECIFICACAO.ID));

	if (tipoConfigPrecificacao != TipoConfigPrecificacao.EXCLUSAO) {
	    joins.join(MARKUP_PRODUTO).on(MARKUP_PRODUTO.CONFIGURACAO_PRECIFICACAO_ID.eq(CONFIGURACAO_PRECIFICACAO.ID)).join(PRODUTO).on(MARKUP_PRODUTO.PRODUTO_ID.eq(PRODUTO.ID));
	} else {
	    joins.leftJoin(MARKUP_PRODUTO).on(MARKUP_PRODUTO.CONFIGURACAO_PRECIFICACAO_ID.eq(CONFIGURACAO_PRECIFICACAO.ID))
			    .leftJoin(PRODUTO).on(MARKUP_PRODUTO.PRODUTO_ID.eq(PRODUTO.ID));
	}


        SelectConditionStep selectConditionStep = joins
			.where(CONFIGURACAO_PRECIFICACAO.TIPO_CONFIG_PRECIFICACAO.eq(tipoConfigPrecificacao.name()))
			.and(CONFIGURACAO_PRECIFICACAO.ATIVO.isTrue())
		        .and(CIA_AEREA.CODIGO.eq(codigoCia).or(CIA_AEREA.CODIGO.eq(TODAS_AS_CIAS)))
		        .and(CONFIGURACAO_PRECIFICACAO_EMPRESA.EMPRESA_ID.eq(empresaId));

        if (tipoConfigPrecificacao != TipoConfigPrecificacao.EXCLUSAO) {
            selectConditionStep.and(PRODUTO.NOME.eq(produto));
        } else {
	    selectConditionStep.and(PRODUTO.NOME.eq(produto).or(PRODUTO.NOME.isNull()));
	}

	if (isBuscaDeTeste) {
	    selectConditionStep.and(CONFIGURACAO_PRECIFICACAO.EDITAVEL.isTrue());
	    selectConditionStep.and(CONFIGURACAO_PRECIFICACAO.STATUS_PUBLICACAO.in(PUBLICADA.name(), NAO_PUBLICADA.name()));
	} else {
	    selectConditionStep.and(CONFIGURACAO_PRECIFICACAO.STATUS_PUBLICACAO.eq(PUBLICADA.name()));
	}

	List<SortField<?>> sortFields = new ArrayList<>();

	if (markupDinamicoFirst) {
	    sortFields.add(CONFIGURACAO_PRECIFICACAO.TIPO_MARKUP.sortAsc(TipoMarkup.INTELIGENTE.name()));
	}

	sortFields.add(CONFIGURACAO_PRECIFICACAO.PRIORIDADE.asc());

	return this.eagerMapper.map(selectConditionStep.orderBy(sortFields).fetch());
    }

	@Trace
	@Override
	public List<ConfiguracaoPrecificacao> buscaSoComRestricaoByCiaTipoConfigEmpresaIdProdutoBPRICEMarkupLimite(String codigoCia,
																											   TipoConfigPrecificacao tipoConfigPrecificacao,
																											   Integer empresaId, String produto) {

		Field[] columns = Stream.concat(Stream.of(CONFIGURACAO_PRECIFICACAO.fields()), Stream.of(RESTRICAO.fields())).toArray(Field[]::new);
		SelectConditionStep<Record> selectConditionStep = this.getContext().select(columns)
				.from(CONFIGURACAO_PRECIFICACAO)
				.leftJoin(CONFIGURACAO_PRECIFICACAO_RESTRICAO).on(CONFIGURACAO_PRECIFICACAO.ID.eq(CONFIGURACAO_PRECIFICACAO_RESTRICAO.CONFIGURACAO_PRECIFICACAO_ID))
				.leftJoin(RESTRICAO).on(CONFIGURACAO_PRECIFICACAO_RESTRICAO.RESTRICAO_ID.eq(RESTRICAO.ID))
				.join(CONFIGURACAO_PRECIFICACAO_CIA_AEREA).on(CONFIGURACAO_PRECIFICACAO_CIA_AEREA.CONFIGURACAO_PRECIFICACAO_ID.eq(CONFIGURACAO_PRECIFICACAO.ID))
				.join(CIA_AEREA).on(CONFIGURACAO_PRECIFICACAO_CIA_AEREA.CIA_AEREA_ID.eq(CIA_AEREA.ID))
				.join(CONFIGURACAO_PRECIFICACAO_EMPRESA).on(CONFIGURACAO_PRECIFICACAO_EMPRESA.CONFIGURACAO_PRECIFICACAO_ID.eq(CONFIGURACAO_PRECIFICACAO.ID))
				.join(MARKUP_PRODUTO).on(MARKUP_PRODUTO.CONFIGURACAO_PRECIFICACAO_ID.eq(CONFIGURACAO_PRECIFICACAO.ID))
				.join(PRODUTO).on(MARKUP_PRODUTO.PRODUTO_ID.eq(PRODUTO.ID))
				.where(CONFIGURACAO_PRECIFICACAO.TIPO_CONFIG_PRECIFICACAO.eq(tipoConfigPrecificacao.name()))
				.and(CONFIGURACAO_PRECIFICACAO.TIPO_MARKUP.eq(TIPO_MARKUP_LIMITE))
				.and(CONFIGURACAO_PRECIFICACAO.ATIVO.isTrue())
				.and(CIA_AEREA.CODIGO.eq(codigoCia).or(CIA_AEREA.CODIGO.eq(TODAS_AS_CIAS)))
				.and(CONFIGURACAO_PRECIFICACAO_EMPRESA.EMPRESA_ID.eq(empresaId))
				.and(PRODUTO.NOME.eq(produto))
				.and(CONFIGURACAO_PRECIFICACAO.STATUS_PUBLICACAO.eq(PUBLICADA.name()));

		List<SortField<?>> sortFields = new ArrayList<>();
		sortFields.add(CONFIGURACAO_PRECIFICACAO.PRIORIDADE.asc());

		return this.eagerMapper.map(selectConditionStep.orderBy(sortFields).fetch());
	}

	@Override
    @Trace
    public ConfiguracaoPrecificacao findEaggerByKey(Integer idConfiguracaoPrecificacao) {
	List<ConfiguracaoPrecificacao> result = this.findEagerByKeys(Collections.singletonList(idConfiguracaoPrecificacao));
	return result.isEmpty() ? null : result.get(0);
    }

    @Override
    public List<ConfiguracaoPrecificacao> findEagerByKeys(List<Integer> idsConfiguracoesPrecificacao) {
	final List<ConfiguracaoPrecificacao> configuracoes = eagerMapper.map(this.getContext()
											     .select()
											     .from(CONFIGURACAO_PRECIFICACAO)
											     .leftJoin(CONFIGURACAO_PRECIFICACAO_RESTRICAO)
											     .on(CONFIGURACAO_PRECIFICACAO.ID.eq(CONFIGURACAO_PRECIFICACAO_RESTRICAO.CONFIGURACAO_PRECIFICACAO_ID))
											     .leftJoin(RESTRICAO)
											     .on(CONFIGURACAO_PRECIFICACAO_RESTRICAO.RESTRICAO_ID.eq(RESTRICAO.ID))
											     .where(CONFIGURACAO_PRECIFICACAO.ID.in(idsConfiguracoesPrecificacao))
											     .fetch());
	configuracoes.forEach(this::preencheDadosConfiguracao);
	return configuracoes;
    }

    private void preencheDadosConfiguracao(ConfiguracaoPrecificacao configuracao) {
	final Integer configuracaoId = configuracao.getId();
	configuracao.setMarkups(this.markupProdutoRepository.findByConfigPrecificacaoId(configuracaoId));
	configuracao.setExcecoes(this.excecaoRepository.findEaggerByConfigPrecificacaoId(configuracaoId));
	configuracao.setCiasAereas(this.ciaAereaRepository.findByConfigPrecificacaoId(configuracaoId));
	configuracao.setEmpresas(this.empresaRepository.findByConfigPrecificacaoId(configuracaoId));
    }

    @Override public ConfiguracaoPrecificacao ultimoPublicadoPorIdOrigem(Integer idOrigem) {
        return this.mapper().map(this.getContext()
                         .selectFrom(CONFIGURACAO_PRECIFICACAO)
                         .where(CONFIGURACAO_PRECIFICACAO.STATUS_PUBLICACAO.eq(PUBLICADA.name()))
                         .and(CONFIGURACAO_PRECIFICACAO.ID_ORIGEM.eq(idOrigem).or(CONFIGURACAO_PRECIFICACAO.ID.eq(idOrigem))).fetchOne());
    }

    @Override public Integer ultimoEditavelPorIdOrigem(Integer idOrigem) {
        Record1<Integer> idOrigemRecord = this.getContext().select(CONFIGURACAO_PRECIFICACAO.ID)
		        .from(CONFIGURACAO_PRECIFICACAO)
		        .where(CONFIGURACAO_PRECIFICACAO.EDITAVEL.isTrue())
		        .and(CONFIGURACAO_PRECIFICACAO.ATIVO.isTrue())
		        .and(CONFIGURACAO_PRECIFICACAO.ID_ORIGEM.eq(idOrigem))
		        .fetchOne();
        return idOrigemRecord != null ? idOrigemRecord.value1() : null;
    }

    @Override
    public List<ConfiguracaoPrecificacao> findByParameters(int page, int pageSize, String orderBy, Map<String, String> params) {
	final SelectJoinStep<Record> selectJoinStep = this.getContext().selectDistinct(CONFIGURACAO_PRECIFICACAO.fields()).from(CONFIGURACAO_PRECIFICACAO);
	final SelectConditionStep<Record> queryByParameters = this.createQueryByParameters(selectJoinStep, params);


	SelectForUpdateStep<Record> selectLimitado = queryByParameters.limit(pageSize).offset((page - 1) * pageSize);

	this.addOrderBy(queryByParameters, orderBy);

	return this.eagerMapper.map(this.getContext().select(selectLimitado.asTable(CONFIGURACAO_PRECIFICACAO.getName()).fields())
			.select(RESTRICAO.fields())
			.from(selectLimitado.asTable(CONFIGURACAO_PRECIFICACAO.getName()))
			.leftJoin(CONFIGURACAO_PRECIFICACAO_RESTRICAO)
			.on(CONFIGURACAO_PRECIFICACAO_RESTRICAO.CONFIGURACAO_PRECIFICACAO_ID.eq(selectLimitado.asTable(CONFIGURACAO_PRECIFICACAO.getName()).field(CONFIGURACAO_PRECIFICACAO.ID)))
			.leftJoin(RESTRICAO)
			.on(CONFIGURACAO_PRECIFICACAO_RESTRICAO.RESTRICAO_ID.eq(RESTRICAO.ID))
			.fetch());
    }

    @Override
    public Integer countByParameters(Map<String, String> params) {
	final SelectJoinStep<Record> query = this.getContext().select(Arrays.asList(CONFIGURACAO_PRECIFICACAO.ID.count())).from(CONFIGURACAO_PRECIFICACAO);
	return this.createQueryByParameters(query, params).fetchOne(0, Integer.class);
    }

    @Override
    public List<ConfiguracaoPrecificacao> buscaConfiguracoesPrecificacaoParaNotificacao() {
        return this.configPrecificacaoRestricaoEAcordoMapper.map(this.getContext()
		                             .select()
		                             .from(CONFIGURACAO_PRECIFICACAO)
		                             .leftJoin(ACORDO_COMERCIAL)
		                                .on(ACORDO_COMERCIAL.ID.eq(CONFIGURACAO_PRECIFICACAO.ACORDO_COMERCIAL_ID))
		                             .leftJoin(CONFIGURACAO_PRECIFICACAO_RESTRICAO)
		                                .on(CONFIGURACAO_PRECIFICACAO_RESTRICAO.CONFIGURACAO_PRECIFICACAO_ID.eq(CONFIGURACAO_PRECIFICACAO.ID))
		                             .leftJoin(RESTRICAO)
		                                .on(RESTRICAO.ID.eq(CONFIGURACAO_PRECIFICACAO_RESTRICAO.RESTRICAO_ID)
							.and(RESTRICAO.TIPO_OPERADOR.in(Arrays.asList(TipoOperador.IGUAL, TipoOperador.ANTES, TipoOperador.ANTES_OU_IGUAL, TipoOperador.DENTRO)))
				                        .and(RESTRICAO.TIPO_RESTRICAO.like("DATA%").or(RESTRICAO.TIPO_RESTRICAO.like("PERIODO%"))))
		                             .where(CONFIGURACAO_PRECIFICACAO.STATUS_PUBLICACAO.eq(PUBLICADA.name()))
		                                .and(CONFIGURACAO_PRECIFICACAO.ID.notIn(buscaNotificacoesDeConfiguracaoDePrecificacaoRecord()))
		                                .and(RESTRICAO.ID.isNotNull().or(ACORDO_COMERCIAL.DATA_FIM_VIGENCIA.isNotNull()))
		                                .and(CONFIGURACAO_PRECIFICACAO.ATIVO.isTrue())
		                             .fetch());
    }

    @Override
    public ConfiguracaoPrecificacaoIsaCondicao findConfiguracaoPrecificacaoRestritaByIds(List<Integer> ids) {
        Record5<BigDecimal, BigDecimal, String, String, String> configuracaoPrecificacaoIsaCondicaoRecord = this.getContext()
		        .select(CONFIGURACAO_PRECIFICACAO.COMISSAO_FORNECEDOR.multiply(BigDecimal.TEN.pow(2)).round(2).as("comissao"),
		                CONFIGURACAO_PRECIFICACAO.INCENTIVO_FORNECEDOR.multiply(BigDecimal.TEN.pow(2)).round(2).as("incentivo"),
		                CONFIGURACAO_PRECIFICACAO.ENDOSSO.as("endosso"),
		                CONFIGURACAO_PRECIFICACAO.TOURCODE.as("tourCode"),
						CONFIGURACAO_PRECIFICACAO.OSI.as("osi"))
		        .from(CONFIGURACAO_PRECIFICACAO)
		        .where(CONFIGURACAO_PRECIFICACAO.TIPO_CONFIG_PRECIFICACAO.eq(TipoConfigPrecificacao.RESTRITA.name()))
		        .and(CONFIGURACAO_PRECIFICACAO.ID.in(ids)).fetchOne();
        if (configuracaoPrecificacaoIsaCondicaoRecord != null) {
            return configuracaoPrecificacaoIsaCondicaoRecord.into(ConfiguracaoPrecificacaoIsaCondicao.class);
        }
        return null;
    }

    @Override
    public List<ConfiguracaoPrecificacao> findByKeys(Set<Integer> ids) {
        return this.getContext().select()
		        .from(CONFIGURACAO_PRECIFICACAO)
		        .where(CONFIGURACAO_PRECIFICACAO.ID.in(ids)).fetch().into(ConfiguracaoPrecificacao.class);
    }

    /**
     * Devolve as configurações de precificação com todas as informações preenchidas
     *
     * OBS: Esse método é pesado, pois busca muitas informações do banco de dados e caso esteja utilizando muito recurso talves seja necessário
     * trabalhar com cursor ao invés de bater varias vezes no banco.
     *
     * @param params os parametros a serem filtrados
     * @return um {@link List} de {@link ConfiguracaoPrecificacao}.
     */
    @Override
    public List<ConfiguracaoPrecificacao> findEagerByParameters(Map<String, String> params) {
	final SelectJoinStep<Record> selectJoinStep = this.getContext().select()
			.from(CONFIGURACAO_PRECIFICACAO)
			.leftJoin(CONFIGURACAO_PRECIFICACAO_RESTRICAO)
			.on(CONFIGURACAO_PRECIFICACAO.ID.eq(CONFIGURACAO_PRECIFICACAO_RESTRICAO.CONFIGURACAO_PRECIFICACAO_ID))
			.leftJoin(RESTRICAO)
			.on(CONFIGURACAO_PRECIFICACAO_RESTRICAO.RESTRICAO_ID.eq(RESTRICAO.ID));
	final SelectConditionStep<Record> queryByParameters = this.createQueryByParameters(selectJoinStep, params);
	this.addOrderBy(queryByParameters, DEFAULT_FIELD_ORDER_BY);
	Result<Record> result = queryByParameters.fetch();

	List<ConfiguracaoPrecificacao> configuracoes = this.eagerMapper.map(result);
	configuracoes.stream().forEach(this::preencheDadosConfiguracao);
	return configuracoes;
    }

    protected SelectConditionStep<Record1<Integer>> buscaNotificacoesDeConfiguracaoDePrecificacaoRecord() {
	return this.getContext()
			    .select(NOTIFICACAO.IDENTIFICADOR)
			    .from(NOTIFICACAO)
			    .where(NOTIFICACAO.ORIGEM_NOTIFICACAO.eq(OrigemNotificacao.CONFIGURACAO_PRECIFICACAO.name()));
    }

    public SelectConditionStep<Record> createQueryByParameters(SelectJoinStep<Record> selectJoin, Map<String, String> params){
	if(params.containsKey(PARAM_CIA)) {
	    selectJoin.join(CONFIGURACAO_PRECIFICACAO_CIA_AEREA)
			    .on(CONFIGURACAO_PRECIFICACAO.ID.eq(CONFIGURACAO_PRECIFICACAO_CIA_AEREA.CONFIGURACAO_PRECIFICACAO_ID))
			    .join(CIA_AEREA)
			    .on(CONFIGURACAO_PRECIFICACAO_CIA_AEREA.CIA_AEREA_ID.eq(CIA_AEREA.ID));
	}
	if(params.containsKey(PARAM_EMPRESA)) {
	    selectJoin.join(CONFIGURACAO_PRECIFICACAO_EMPRESA)
			    .on(CONFIGURACAO_PRECIFICACAO.ID.eq(CONFIGURACAO_PRECIFICACAO_EMPRESA.CONFIGURACAO_PRECIFICACAO_ID))
			    .join(EMPRESA)
			    .on(CONFIGURACAO_PRECIFICACAO_EMPRESA.EMPRESA_ID.eq(EMPRESA.ID));
	}

	List<Condition> conditions = this.createConditionsByParameters(params);
	return selectJoin.where(conditions);
    }

    private void addOrderBy(SelectConditionStep<Record> selectConditionStep, String orderBy) {
	if(StringUtils.isEmpty(orderBy)) {
	    orderBy = DEFAULT_FIELD_ORDER_BY;
	}
	final Field<?> fieldOrderBy = CONFIGURACAO_PRECIFICACAO.field(orderBy);
	if(fieldOrderBy != null) {
	    selectConditionStep.orderBy(fieldOrderBy);
	}
    }

    private List<Condition> createConditionsByParameters(Map<String, String> params) {
	List<Condition> conditions = new ArrayList<>();
	if(params.containsKey(PARAM_CIA)) {
	    conditions.add(CIA_AEREA.NOME.eq(params.get(PARAM_CIA)));
	}
	if(params.containsKey(PARAM_PRODUTO)) {
		conditions.add(CONFIGURACAO_PRECIFICACAO.ID.in(this.getContext()
				.select(MARKUP_PRODUTO.CONFIGURACAO_PRECIFICACAO_ID)
				.from(MARKUP_PRODUTO).join(PRODUTO).on(PRODUTO.ID.eq(MARKUP_PRODUTO.PRODUTO_ID))
				.where(PRODUTO.NOME.likeIgnoreCase(params.get(PARAM_PRODUTO)))
			)
		);
	}
	if(params.containsKey(PARAM_STATUS)) {
		conditions.add(CONFIGURACAO_PRECIFICACAO.STATUS_PUBLICACAO.likeIgnoreCase(params.get(PARAM_STATUS)));
		if (EM_HISTORICO.name().equals(params.get(PARAM_STATUS))) {
			conditions.add(CONFIGURACAO_PRECIFICACAO.EDITAVEL.isFalse());
		}
	}
	if(params.containsKey(PARAM_EMPRESA)) {
	    conditions.add(EMPRESA.NOME.eq(params.get(PARAM_EMPRESA)));
	}
	if(params.containsKey(PARAM_NOME)) {
	    conditions.add(CONFIGURACAO_PRECIFICACAO.NOME.likeIgnoreCase(params.get(PARAM_NOME)));
	}
	if(params.containsKey(PARAM_TIPO_CONFIG)) {
	    conditions.add(CONFIGURACAO_PRECIFICACAO.TIPO_CONFIG_PRECIFICACAO.likeIgnoreCase(params.get(PARAM_TIPO_CONFIG)));
	}
	if (!EM_HISTORICO.name().equals(params.get(PARAM_STATUS)) && params.containsKey(PARAM_EDITAVEL)) {
	    conditions.add(CONFIGURACAO_PRECIFICACAO.EDITAVEL.eq(Boolean.valueOf(params.get(PARAM_EDITAVEL).toLowerCase())));
	}

	if(params.containsKey(PARAM_STATUS) && INATIVO.name().equals(params.get(PARAM_STATUS))){
	    conditions.add(CONFIGURACAO_PRECIFICACAO.ATIVO.isFalse());
	}else {
	    if (params.containsKey(PARAM_ATIVO) && params.containsKey(PARAM_STATUS)) {
		conditions.add(CONFIGURACAO_PRECIFICACAO.ATIVO.eq(Boolean.valueOf(params.get(PARAM_ATIVO))));
	    } else {
		conditions.add(CONFIGURACAO_PRECIFICACAO.ATIVO.isTrue());
	    }
	}

        if (params.containsKey(PARAM_AGENCIA)) {
	    conditions.add(CONFIGURACAO_PRECIFICACAO.ID.in(
			    this.getContext().select(CONFIGURACAO_PRECIFICACAO_RESTRICAO.CONFIGURACAO_PRECIFICACAO_ID).from(
					    CONFIGURACAO_PRECIFICACAO_RESTRICAO).innerJoin(RESTRICAO).on(
					    RESTRICAO.ID.eq(CONFIGURACAO_PRECIFICACAO_RESTRICAO.RESTRICAO_ID)).where(
					    RESTRICAO.TIPO_RESTRICAO.eq(
							    TipoRestricao.AGENCIA.name()).and(RESTRICAO.NOME.eq(params.get(PARAM_AGENCIA))))));
        }

        if (params.containsKey(PARAM_GRUPO)) {
	    conditions.add(CONFIGURACAO_PRECIFICACAO.ID.in(
			    this.getContext().select(CONFIGURACAO_PRECIFICACAO_RESTRICAO.CONFIGURACAO_PRECIFICACAO_ID).from(
					    CONFIGURACAO_PRECIFICACAO_RESTRICAO).innerJoin(RESTRICAO).on(
					    RESTRICAO.ID.eq(CONFIGURACAO_PRECIFICACAO_RESTRICAO.RESTRICAO_ID)).where(
					    RESTRICAO.TIPO_RESTRICAO.eq(TipoRestricao.GRUPO.name()).and(RESTRICAO.NOME.eq(params.get(PARAM_GRUPO))))));
        }

        return conditions;
    }

    @Override
    public List<ConfiguracaoPrecificacao> findByConfigPrecificacaoComercialId(Integer configuracaoId) {
	return this.getContext().select(CONFIGURACAO_PRECIFICACAO.fields())
			.from(CONFIGURACAO_PRECIFICACAO)
			.join(CONFIGURACAO_COMERCIAL_PRECIFICACAO)
			.on(CONFIGURACAO_PRECIFICACAO.ID.eq(CONFIGURACAO_COMERCIAL_PRECIFICACAO.CONFIGURACAO_PRECIFICACAO_ID))
			.where(CONFIGURACAO_COMERCIAL_PRECIFICACAO.CONFIGURACAO_PRECIFICACAO_COMERCIAL_ID.eq(configuracaoId))
			.fetch().map(record -> this.mapper().map(record.into(CONFIGURACAO_PRECIFICACAO)));
    }
}

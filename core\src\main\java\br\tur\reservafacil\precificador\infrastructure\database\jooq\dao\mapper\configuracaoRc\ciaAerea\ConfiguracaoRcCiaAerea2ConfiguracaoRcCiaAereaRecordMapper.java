package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.configuracaoRc.ciaAerea;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoRcCiaAereaRecord;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcCiaAerea;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.CustomMapper;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON><PERSON> on 20/08/25 - GA-12251.
 */
@Component
public class ConfiguracaoRcCiaAerea2ConfiguracaoRcCiaAereaRecordMapper
		implements CustomMapper<ConfiguracaoRcCiaAerea, ConfiguracaoRcCiaAereaRecord> {

    @Override
    public ConfiguracaoRcCiaAereaRecord map(ConfiguracaoRcCiaAerea input, ConfiguracaoRcCiaAereaRecord output) {
	if (input.getId() != null) {
	    output.setId(input.getId());
	}
	output.setCiaAereaId(input.getCiaAerea() != null ? input.getCiaAerea().getId() : null);
	output.setConfiguracaoRcId(input.getConfiguracaoRc() != null ? input.getConfiguracaoRc().getId() : null);
	return output;
    }
}

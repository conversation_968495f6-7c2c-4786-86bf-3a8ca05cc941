package br.tur.reservafacil.dominio.aereo;

import br.tur.reservafacil.dominio.aereo.interfaces.Identificavel;

import java.io.Serializable;
import java.util.Objects;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
public class ConfiguracaoRcCiaAerea
		implements Serializable, Identificavel<Integer> {

    private static final long serialVersionUID = -3546699234527739456L;

    private Integer id;

    private ConfiguracaoRc configuracaoRc;

    private CiaAerea ciaAerea;

    private Boolean ativo = Boolean.TRUE;

    public ConfiguracaoRcCiaAerea() {
    }

    public ConfiguracaoRcCiaAerea(ConfiguracaoRc configuracaoRc, CiaAerea ciaAerea) {
	this.configuracaoRc = configuracaoRc;
	this.ciaAerea = ciaAerea;
    }

    public Integer getId() {
	return id;
    }

    public void setId(Integer id) {
	this.id = id;
    }

    public ConfiguracaoRc getConfiguracaoRc() {
	return configuracaoRc;
    }

    public void setConfiguracaoRc(ConfiguracaoRc configuracaoRc) {
	this.configuracaoRc = configuracaoRc;
    }

    public CiaAerea getCiaAerea() {
	return ciaAerea;
    }

    public void setCiaAerea(CiaAerea ciaAerea) {
	this.ciaAerea = ciaAerea;
    }

    public Boolean getAtivo() {
	return ativo;
    }

    public void setAtivo(Boolean ativo) {
	this.ativo = ativo;
    }

    @Override
    public boolean equals(Object o) {
	if (this == o)
	    return true;
	if (o == null || getClass() != o.getClass())
	    return false;
	ConfiguracaoRcCiaAerea that = (ConfiguracaoRcCiaAerea)o;
	return Objects.equals(id, that.id) &&
			Objects.equals(configuracaoRc, that.configuracaoRc) &&
			Objects.equals(ciaAerea, that.ciaAerea);


    }

    @Override
    public int hashCode() {
	return Objects.hash(id, configuracaoRc, ciaAerea);
    }
}

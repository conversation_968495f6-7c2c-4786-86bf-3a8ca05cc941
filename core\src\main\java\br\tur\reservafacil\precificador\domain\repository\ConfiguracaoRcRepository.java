package br.tur.reservafacil.precificador.domain.repository;

import br.tur.reservafacil.dominio.aereo.ConfiguracaoRc;

import java.util.List;
import java.util.Set;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
public interface ConfiguracaoRcRepository
		extends VersionavelRepository<ConfiguracaoRc, Integer>  {


    ConfiguracaoRc findEaggerByKey(Integer idConfiguracaoPrecificacao);

    List<ConfiguracaoRc> findEagerByKeys(List<Integer> idsConfiguracoesPrecificacao);

    List<ConfiguracaoRc> findByKeys(Set<Integer> ids);

    List<ConfiguracaoRc> findByEmpresa(Integer empresaId, boolean isBuscaTeste);
}

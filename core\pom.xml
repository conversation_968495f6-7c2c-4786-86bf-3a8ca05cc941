<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>precificador</artifactId>
        <groupId>br.tur.reservafacil.soa</groupId>
        <version>1.10.343-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <packaging>jar</packaging>
    <artifactId>precificador-core</artifactId>

    <dependencies>

        <dependency>
            <groupId>${project.parent.groupId}</groupId>
            <artifactId>precificador-dominio</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>${project.parent.groupId}</groupId>
            <artifactId>precificador-adapters</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>br.tur.reservafacil.soa</groupId>
            <artifactId>precificador-data</artifactId>
            <version>1.9.136</version>
        </dependency>

        <dependency>
            <groupId>org.fluttercode.datafactory</groupId>
            <artifactId>datafactory</artifactId>
            <version>0.8</version>
            <type>jar</type>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>br.tur.reservafacil.utils</groupId>
            <artifactId>rf-cache</artifactId>
            <version>1.0.11</version>
        </dependency>

        <dependency>
            <groupId>br.tur.reservafacil.ws.client</groupId>
            <artifactId>rf-client-ndc</artifactId>
            <version>1.0.9</version>
            <scope>compile</scope>
        </dependency>


        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk16</artifactId>
            <version>1.46</version>
        </dependency>

        <dependency>
            <groupId>net.sf.ehcache</groupId>
            <artifactId>ehcache</artifactId>
            <version>2.10.1</version>
        </dependency>

        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
            <version>5.8.1</version>
        </dependency>

        <dependency>
            <groupId>com.googlecode.flyway</groupId>
            <artifactId>flyway-maven-plugin</artifactId>
            <version>2.3.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-jdk14</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-nop</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web-services</artifactId>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jetty</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-el</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>net.sourceforge.nekohtml</groupId>
            <artifactId>nekohtml</artifactId>
            <version>1.9.22</version>
        </dependency>

        <!-- Akka -->
        <dependency>
            <groupId>com.typesafe.akka</groupId>
            <artifactId>akka-actor_2.12</artifactId>
            <version>${akka.version}</version>
        </dependency>
        <dependency>
            <groupId>com.typesafe.akka</groupId>
            <artifactId>akka-testkit_2.12</artifactId>
            <version>${akka.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>

        <!-- H2 para testes e geração de arquivos do JOOQ -->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>1.4.187</version>
            <scope>test</scope>
        </dependency>

        <!-- Http Client -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <!-- Hikari CP -->
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
            <version>3.2.0</version>
        </dependency>

        <!-- Apache POI -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${apache.poi.version}</version>
        </dependency>

        <dependency>
            <groupId>com.newrelic.agent.java</groupId>
            <artifactId>newrelic-api</artifactId>
            <version>3.30.1</version>
        </dependency>

        <dependency>
            <groupId>br.tur.reservafacil.javali</groupId>
            <artifactId>javali</artifactId>
            <version>${rf-javali-version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>br.tur.reservafacil</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>br.tur.reservafacil.netcomponents</groupId>
                    <artifactId>NetComponents</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>br.tur.reservafacil.redecard</groupId>
                    <artifactId>RedecardWebServices</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>br.tur.rf.dependencias</groupId>
                    <artifactId>axis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>postgresql</groupId>
                    <artifactId>postgresql</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>br.tur.reservafacil.etkt</groupId>
                    <artifactId>gera-bean</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.mail</groupId>
                    <artifactId>mail</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.velocity</groupId>
                    <artifactId>velocity</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Swagger -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.8.0</version>
        </dependency>

        <!-- Swagger UI -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.8.0</version>
        </dependency>

        <dependency>
            <groupId>io.dropwizard.metrics</groupId>
            <artifactId>metrics-healthchecks</artifactId>
            <version>${dropwizard-metrics.version}</version>
        </dependency>

        <dependency>
            <groupId>br.tur.reservafacil.soa</groupId>
            <artifactId>gw-security</artifactId>
            <version>0.0.5</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.9.4</version>
        </dependency>

        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>5.2</version>
        </dependency>

        <dependency>
            <groupId>com.cwbase</groupId>
            <artifactId>logback-redis-appender</artifactId>
            <version>1.1.6</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${artifactIdForWarFile}##${git.commitsCount}-${project.version}</finalName>
        <directory>../target</directory>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>2.10.3</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.2</version>
                    <configuration>
                        <skip>true</skip>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <!-- Spring Boot <-> Maven Integration -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.0.2.RELEASE</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>build-info</goal>
                        </goals>
                        <configuration>
                            <additionalProperties>
                                <encoding.source>UTF-8</encoding.source>
                                <encoding.reporting>UTF-8</encoding.reporting>
                                <java.source>${maven.compiler.source}</java.source>
                                <java.target>${maven.compiler.target}</java.target>
                            </additionalProperties>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Configuração de testes unitários. Importante quando forem configurados testes de inetgração -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M5</version>
                <configuration>
                    <includes>
                        <include>**/*.java</include>
                    </includes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <version>3.0.0-M5</version>
            </plugin>

            <plugin>
                <groupId>ru.concerteza.buildnumber</groupId>
                <artifactId>maven-jgit-buildnumber-plugin</artifactId>
                <version>1.2.7</version>
                <executions>
                    <execution>
                        <id>jgit-buildnumber</id>
                        <goals>
                            <goal>extract-buildnumber</goal>
                        </goals>
                        <phase>validate</phase>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>4.9.10</version>
                <executions>
                    <execution>
                        <id>git-commit-id</id>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                        <phase>validate</phase>
                        <configuration>
                            <!-- Only changing prefix since properties conflicts with jgit above -->
                            <prefix>git-commit-id</prefix>
                            <generateGitPropertiesFile>false</generateGitPropertiesFile>
                            <generateGitPropertiesFilename>target/classes/build-scm.properties</generateGitPropertiesFilename>
                            <!-- We're using a pom in this example-->
                            <skipPoms>false</skipPoms>
                            <generateGitPropertiesFile>true</generateGitPropertiesFile>
                            <gitDescribe>
                                <!-- Faster to get just branch if skip = true -->
                                <skip>false</skip>
                            </gitDescribe>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.7</version>
                <executions>
                    <execution>
                        <id>echo-properties</id>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <phase>validate</phase>
                        <configuration>
                            <target>
                                <echo message="buildnumber-maven-plugin properties:" />
                                <echo message=" $${scmBranch}: ${scmBranch}" />
                                <echo message=" $${buildNumber}: ${buildNumber}" />
                                <echo message=" $${timestamp}: ${timestamp}" />

                                <echo message="maven-jgit-buildnumber-plugin properties:" />
                                <echo message=" $${git.revision}: ${git.revision}" />
                                <echo message=" $${git.branch}: ${git.branch}" />
                                <echo message=" $${git.tag}: ${git.tag}" />
                                <echo message=" $${git.commitsCount}: ${git.commitsCount}" />
                                <echo message=" $${git.buildnumber}: ${git.buildnumber}" />


                                <echo message="git-commit-id-plugin properties (aliased with git-commit-id):" />
                                <echo message=" $${git.branch}: ${git-commit-id.branch}" />

                                <echo message=" $${git.commit.id.describe}: ${git-commit-id.commit.id.describe}" />

                                <echo message=" $${git.build.user.name}: ${git-commit-id.build.user.name}" />
                                <echo message=" $${git.build.user.email}: ${git-commit-id.build.user.email}" />
                                <echo message=" $${git.build.time}: ${git-commit-id.build.time}" />

                                <echo message=" $${git.commit.id}: ${git-commit-id.commit.id}" />
                                <echo message=" $${git.commit.id.abbrev}: ${git-commit-id.commit.id.abbrev}" />
                                <echo message=" $${git.commit.user.name}: ${git-commit-id.commit.user.name}" />
                                <echo message=" $${git.commit.user.email}: ${git-commit-id.commit.user.email}" />
                                <echo message=" $${git.commit.message.full}: ${git-commit-id.commit.message.full}" />
                                <echo message=" $${git.commit.message.short}: ${git-commit-id.commit.message.short}" />
                                <echo message=" $${git.commit.time}: ${git-commit-id.commit.time}" />

                            </target>
                        </configuration>
                    </execution>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <tasks>
                                <delete dir="home" includeemptydirs="true" />
                                <concat append="yes" destfile="target/classes/build-scm.properties">git.commitsCount=${git.commitsCount}</concat>
                            </tasks>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Compila testes Spock -->
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>1.5</version>
                <configuration>
                    <invokeDynamic>true</invokeDynamic>
                    <sourceEncoding>UTF-8</sourceEncoding>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>addSources</goal>
                            <goal>addTestSources</goal>
                            <goal>generateStubs</goal>
                            <goal>compile</goal>
                            <goal>testGenerateStubs</goal>
                            <goal>testCompile</goal>
                            <!--<goal>removeStubs</goal>-->
                            <!--<goal>removeTestStubs</goal>-->
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy-all</artifactId>
                        <version>${groovy.version}</version>
<!--                        <classifier>indy</classifier>-->
                        <type>pom</type>
                    </dependency>
                    <dependency>
                        <groupId>org.fusesource.jansi</groupId>
                        <artifactId>jansi</artifactId>
                        <version>1.11</version>
                        <type>jar</type>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.0.0</version>
                <executions>
                    <execution>
                        <id>add-groovy-source</id>
                        <!-- before package phase -->
                        <phase>verify</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>${basedir}/src/main/groovy</source>
                            </sources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>add-groovy-test-source</id>
                        <!-- before package phase -->
                        <phase>test</phase>
                        <goals>
                            <goal>add-test-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>${basedir}/src/test/groovy</source>
                            </sources>
                        </configuration>
                    </execution>

                </executions>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <build.script>dev-build</build.script>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <finalName>${artifactIdForWarFile}</finalName>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <configuration>
                            <profiles>
                                <profile>dev</profile>
                            </profiles>
                            <!--
                            mvn clean spring-boot:run -Drun.jvmArguments="-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=y,address=5005"
                            -->
                        </configuration>
                    </plugin>
                </plugins>
            </build>
            <dependencies>
                <dependency>
                    <groupId>org.postgresql</groupId>
                    <artifactId>postgresql</artifactId>
                    <version>${postgresql.version}</version>
                </dependency>
            </dependencies>
        </profile>
    </profiles>
</project>

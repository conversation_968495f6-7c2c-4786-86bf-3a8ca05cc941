package br.tur.reservafacil.dominio.aereo;

import br.tur.reservafacil.dominio.tipo.TipoConfigPrecificacao;

import java.io.Serializable;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Created by <PERSON><PERSON><PERSON> on 6/10/16.
 */
public class FiltroConfiguracao implements Serializable {

    private static final long serialVersionUID = -150210140062493747L;

    private String produto;

    private String codigoCia;

    private List<Restricao> restricoes = new ArrayList<>();

    private TipoConfigPrecificacao tipoConfigPrecificacao = TipoConfigPrecificacao.PADRAO;

    private Integer empresaId;

    private Boolean isBuscaDeTeste = Boolean.FALSE;

    private Boolean markupDinamicoFirst = Boolean.FALSE;

    private Boolean isBestPrice =  Boolean.FALSE;

    public String getProduto() {
        return produto;
    }

    public void setProduto(String produto) {
        this.produto = produto;
    }

    public String getCodigoCia() {
        return codigoCia;
    }

    public void setCodigoCia(String codigoCia) {
        this.codigoCia = codigoCia;
    }

    public List<Restricao> getRestricoes() {
        return restricoes;
    }

    public void setRestricoes(List<Restricao> restricoes) {
        this.restricoes = restricoes;
    }

    public void addRestricao(Restricao restricao) {
        if(restricoes == null) {
            restricoes = new ArrayList<>();
        }
        restricoes.add(restricao);
    }

    public TipoConfigPrecificacao getTipoConfigPrecificacao() {
        return tipoConfigPrecificacao;
    }

    public void setTipoConfigPrecificacao(TipoConfigPrecificacao tipoConfigPrecificacao) {
        this.tipoConfigPrecificacao = tipoConfigPrecificacao;
    }

    public Integer getEmpresaId() {
        return empresaId;
    }

    public void setEmpresaId(Integer empresaId) {
        this.empresaId = empresaId;
    }

    public void setBuscaDeTeste(Boolean isBuscaDeTeste) {
        this.isBuscaDeTeste = isBuscaDeTeste;
    }

    public Boolean isBuscaDeTeste() {
        return isBuscaDeTeste;
    }

    public void setMarkupDinamicoFirst(Boolean markupDinamicoFirst) {
        this.markupDinamicoFirst = markupDinamicoFirst;
    }

    public Boolean isMarkupDinamicoFirst() {
        return markupDinamicoFirst;
    }

    public Boolean isBestPrice() {
        return isBestPrice;
    }

    public void setBestPrice(Boolean bestPrice) {
        isBestPrice = bestPrice;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        FiltroConfiguracao that = (FiltroConfiguracao)o;
        return Objects.equals(produto, that.produto) &&
                Objects.equals(codigoCia, that.codigoCia) &&
                Objects.equals(restricoes, that.restricoes) &&
                Objects.equals(empresaId, that.empresaId) &&
                Objects.equals(isBuscaDeTeste, that.isBuscaDeTeste) &&
                Objects.equals(markupDinamicoFirst, that.markupDinamicoFirst) &&
                Objects.equals(tipoConfigPrecificacao, that.tipoConfigPrecificacao) &&
                Objects.equals(isBestPrice, that.isBestPrice);
    }

    @Override
    public int hashCode() {
        return Objects.hash(produto, codigoCia, restricoes, tipoConfigPrecificacao, empresaId, isBuscaDeTeste, markupDinamicoFirst, isBestPrice);
    }

    @Override
    public String toString() {
        final LocalTime time = LocalTime.now();
        int second = time.getSecond();
        return "FiltroConfiguracao{" +
                "produto='" + produto + '\'' +
                "codigoCia='" + codigoCia + '\'' +
                ", restricoes=" + restricoes +
                ", tipoConfigPrecificacao=" + tipoConfigPrecificacao +
                ", empresaId=" + empresaId +
                ", isBuscaDeTeste=" + isBuscaDeTeste +
                ", markupDinamicoFirst=" + markupDinamicoFirst +
                ", isBestPrice=" + isBestPrice +
                //Isso é para não bater em cache quando for uma busca de teste.
                //Irá bater no cache em escopo de teste, apenas de 10 em 10 segundos (isso é necessário para não deixar a busca de teste muito lenta).
                (isBuscaDeTeste ? ", time=" + DateTimeFormatter.ofPattern("HH:mm:").format(time)  + second/10 : "") +
                "}";
    }
}

package br.tur.reservafacil.precificador.application.service;

import akka.actor.ActorRef;
import br.tur.reservafacil.dominio.Empresa;
import br.tur.reservafacil.dominio.Filial;
import br.tur.reservafacil.dominio.aereo.*;
import br.tur.reservafacil.dominio.aereo.MarkupProduto;
import br.tur.reservafacil.dominio.aereo.common.ObfeeCredencial;
import br.tur.reservafacil.dominio.aereo.configuracaofee.ConfiguracaoFee;
import br.tur.reservafacil.dominio.aereo.disponibilidade.ParametroBuscaAerea;
import br.tur.reservafacil.dominio.aereo.pagamento.CondicaoPagamento;
import br.tur.reservafacil.dominio.aereo.perfilTarifario.PerfilTarifario;
import br.tur.reservafacil.dominio.aereo.tipo.FlagFeatureKeys;
import br.tur.reservafacil.dominio.aereo.voosExcluidos.MotivoExclusaoVoo;
import br.tur.reservafacil.dominio.arquitetura.CriticalPerformance;
import br.tur.reservafacil.dominio.common.Cambio;
import br.tur.reservafacil.dominio.common.TarifaOriginal;
import br.tur.reservafacil.dominio.common.Valor;
import br.tur.reservafacil.dominio.common.ValorTarifa;
import br.tur.reservafacil.dominio.intermediario.v0.to.precificacao.*;
import br.tur.reservafacil.dominio.intermediario.v0.to.precificacao.Merchant;
import br.tur.reservafacil.dominio.tipo.*;
import br.tur.reservafacil.dominio.util.JsonConverterUtil;
import br.tur.reservafacil.precificador.application.service.akka.PrecificavelActor;
import br.tur.reservafacil.precificador.application.service.features.FlagFeatureService;
import br.tur.reservafacil.precificador.application.service.util.precificacao.ExcludedResultCreator;
import br.tur.reservafacil.precificador.domain.component.aereo.InternacionalizacaoComponent;
import br.tur.reservafacil.precificador.domain.exception.BusinessException;
import br.tur.reservafacil.precificador.domain.repository.EmpresaRepository;
import br.tur.reservafacil.precificador.domain.repository.FilialRepository;
import br.tur.reservafacil.precificador.infrastructure.security.RequestMetadata;
import br.tur.reservafacil.precificador.infrastructure.security.SessaoComponent;
import br.tur.reservafacil.precificador.infrastructure.security.service.AutenticacaoService;
import br.tur.reservafacil.precificador.infrastructure.security.service.RecomendacaoAereaFilter;
import br.tur.reservafacil.precificador.util.CalculadorUtil;
import com.newrelic.api.agent.Trace;
import com.rits.cloning.Cloner;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static br.tur.reservafacil.dominio.tipo.TipoConfigPrecificacao.*;
import static br.tur.reservafacil.dominio.tipo.TipoValor.*;
import static br.tur.reservafacil.precificador.util.CalculadoraValorTarifaUtil.*;
import static java.text.MessageFormat.format;

/**
 * Created by ramon on 03/05/16.
 */
@Service
public class PrecificadorService {

    private static final Logger LOG = LoggerFactory.getLogger(PrecificadorService.class);
    private static String MSG_EMPRESA_FILIAL_NULL = "Dados de autenticação (empresaId={0} e/ou filialId={1}) inválidos.";

    private AutenticacaoService autenticacaoService;

    private CambioService cambioService;

    private FilialRepository filialRepository;

    private EmpresaRepository empresaRepository;

    private EmpresaService empresaService;

    private SessaoComponent sessaoComponent;

    private ActorRef precificavelActor;

    private PerfilTarifarioService perfilTarifarioService;

    private PrecificadorService.PrecificacaoRetriever precificacaoRetriever;

    private ConfiguracaoRavService configuracaoRavService;

    private ConfiguracaoFeeService configuracaoFeeService;

    private ConfiguracaoPrecificacaoComercialService configuracaoPrecificacaoComercialService;

    private ConfiguracaoDuService configuracaoDuService;

    private CondicaoPagamentoService condicaoPagamentoService;

    private FlagFeatureService flagFeatureService;

    private ConfiguracaoFiltroRecomendacaoService configuracaoFiltroRecomendacaoService;

    private ConfiguracaoProdutoService configuracaoProdutoService;

    @Autowired
    public PrecificadorService(AutenticacaoService autenticacaoService, CambioService cambioService, FilialRepository filialRepository, EmpresaRepository empresaRepository,
                               EmpresaService empresaService, SessaoComponent sessaoComponent, @Qualifier("precificavelActorRef") ActorRef precificavelActor, PerfilTarifarioService perfilTarifarioService,
                               PrecificacaoRetriever precificacaoRetriever, ConfiguracaoRavService configuracaoRavService, ConfiguracaoFeeService configuracaoFeeService, ConfiguracaoPrecificacaoComercialService configuracaoPrecificacaoComercialService,
                               ConfiguracaoDuService configuracaoDuService, CondicaoPagamentoService condicaoPagamentoService, FlagFeatureService flagFeatureService,
                               ConfiguracaoFiltroRecomendacaoService configuracaoFiltroRecomendacaoService,
                               ConfiguracaoProdutoService configuracaoProdutoService) {
        this.autenticacaoService = autenticacaoService;
        this.cambioService = cambioService;
        this.filialRepository = filialRepository;
        this.empresaRepository = empresaRepository;
        this.empresaService = empresaService;
        this.sessaoComponent = sessaoComponent;
        this.precificavelActor = precificavelActor;
        this.perfilTarifarioService = perfilTarifarioService;
        this.precificacaoRetriever = precificacaoRetriever;
        this.configuracaoRavService = configuracaoRavService;
        this.configuracaoFeeService = configuracaoFeeService;
        this.configuracaoPrecificacaoComercialService = configuracaoPrecificacaoComercialService;
        this.configuracaoDuService = configuracaoDuService;
        this.condicaoPagamentoService = condicaoPagamentoService;
        this.flagFeatureService = flagFeatureService;
        this.configuracaoFiltroRecomendacaoService = configuracaoFiltroRecomendacaoService;
        this.configuracaoProdutoService = configuracaoProdutoService;
    }

    @Trace

    public List<ResultadoPrecificacao> precifica(List<Precificavel> precificaveis, Boolean isBuscaDeTeste, Boolean loadInstallments, ParametroBuscaAerea parametroBuscaAerea, Boolean isBestPrice) {

        final Long empresaId = autenticacaoService.currentEmpresaId();
        final Long filialId = autenticacaoService.currentFilialId();
        final String loginUsuario = autenticacaoService.currentUsuario().getLogin();
        final Integer groupId = autenticacaoService.currentGrupoId() != null ? autenticacaoService.currentGrupoId().intValue() : null;

        if (empresaId == null || filialId == null) {
            throw new BusinessException(format(MSG_EMPRESA_FILIAL_NULL, empresaId, filialId));
        }

        Empresa empresa = empresaService.findByKey(empresaId.intValue());
        final Cambio cambio = cambioService.buscaUltimoCambio(LocalDate.now()).clone();

        if (CollectionUtils.isNotEmpty(precificaveis)) {
            precificaveis.stream().findFirst().map(Precificavel::getTarifacoes).orElse(new ArrayList<>()).stream().findFirst().map(Tarifacao::getValorTarifa).map(
                    ValorTarifa::getTarifaOriginal).map(TarifaOriginal::getCotacaoCambio).ifPresent(cotacaoCambio -> {
                if (cotacaoCambio.getMoedaBase().equals(cambio.getMoeda()) && cotacaoCambio.getTaxaConversao().compareTo(BigDecimal.ONE) > 0) {
                    cambio.setValor(cotacaoCambio.getTaxaConversao());
                }
            });
        }

        final ParametroSessaoPrecificacao sessaoPrecificacao = new ParametroSessaoPrecificacao(empresa, groupId, loginUsuario, isBuscaDeTeste, cambio, loadInstallments, parametroBuscaAerea);
        return !isBestPrice ? _precifica(precificaveis, sessaoPrecificacao) : _bestPrice(precificaveis, sessaoPrecificacao);
    }

    private List<ResultadoPrecificacao> _bestPrice(List<Precificavel> precificaveis, ParametroSessaoPrecificacao sessaoPrecificacao) {

        List<ResultadoPrecificacao> resultados = precificaveis
                .stream().filter(prec -> !prec.isExcludedByFilter())
                .map(precificavel -> {
                    Optional<ResultadoPrecificacao> result = null;
                    try {
                        result = precificacaoRetriever.doPrecificacaoBest(precificavel, sessaoPrecificacao);
                    } catch (Throwable e) {
                        // tratativa apenas para conseguir logar os erros no kibana
                        result = Optional.empty();
                    }

                    if (result.isPresent() && sessaoPrecificacao.isBuscaDeTeste()) {
                        precificavel.setPrecificacaoIds(result.get().getIdsPrecificacao());
                        precificavel.setIdConfigRestrita(result.get().getIdConfigRestrita());
                        precificavel.setIdConfigSucessor(result.get().getIdConfigSucessor());
                        precificavel.setCondicaoPrecificacao(result.get().getCondicaoPrecificacao());
                        precificavel.setConfiguracaoFeeId(result.get().getConfiguracaoFeeId());
                        precificavel.setConfiguracaoRavId(result.get().getConfiguracaoRavId());
                    }
                    return result;
                })
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());

        return resultados;
    }

    @Trace
    public List<ResultadoPrecificacao> precifica(List<Precificavel> precificaveis, Boolean isBuscaDeTeste, String referencia, String empresaRef, Boolean loadInstallments) {
        Empresa empresa = empresaRepository.buscaEmpresaPorReferencia(empresaRef);
        Filial filial = filialRepository.buscarPorReferenciaEEmpresa(referencia, empresa.getId());
        final Integer groupId = autenticacaoService.currentGrupoId() != null ? autenticacaoService.currentGrupoId().intValue() : null;
        final Cambio cambio = cambioService.buscaUltimoCambio(LocalDate.now()).clone();

        if (precificaveis == null || precificaveis.isEmpty()) {
            throw new RuntimeException("Lista de recomendações para cálculo de markup está vazia");
        }

        final ParametroSessaoPrecificacao sessaoPrecificacao = new ParametroSessaoPrecificacao(filial.getEmpresa(), groupId, "", isBuscaDeTeste, cambio, loadInstallments, new ParametroBuscaAerea());
        return _precifica(precificaveis, sessaoPrecificacao);
    }

    @Trace
    @CriticalPerformance
    private List<ResultadoPrecificacao> _precifica(List<Precificavel> precificaveis, ParametroSessaoPrecificacao sessaoPrecificacao) {
        long startTime = System.nanoTime();
        if (CollectionUtils.isEmpty(precificaveis)) {
            throw new RuntimeException("Lista de recomendações para cálculo de markup está vazia");
        }

        final List<Precificavel> excludedResults = precificaveis.stream().filter(Precificavel::isExcludedByFilter).collect(Collectors.toList());

        // RAV
        long startTimeRav = System.nanoTime();
        final List<ConfiguracaoRav> configsRav = this.configuracaoRavService.findByEmpresa(sessaoPrecificacao.getEmpresa().getId(), sessaoPrecificacao.getGrupoId(), sessaoPrecificacao.isBuscaDeTeste());
        long endTimeRav = System.nanoTime();
        long durationRav = (endTimeRav - startTimeRav);
        LOG.debug("Tempo RAV em " + durationRav / 1000000 + "ms");

        // FEE
        long startTimeFee = System.nanoTime();
        final List<ConfiguracaoFee> configsFee = this.configuracaoFeeService.findByEmpresa(sessaoPrecificacao.getEmpresa().getId(), sessaoPrecificacao.isBuscaDeTeste());
        long endTimeFee = System.nanoTime();
        long durationFee = (endTimeFee - startTimeFee);
        LOG.debug("Tempo FEE em " + durationFee / 1000000 + "ms");

        // DU
        long startTimeDu = System.nanoTime();
        final List<ConfiguracaoDu> configsDu = this.configuracaoDuService.findAll();
        long endTimeDu = System.nanoTime();
        long durationDu = (endTimeDu - startTimeDu);
        LOG.debug("Tempo DU em " + durationDu / 1000000 + "ms");

        // Precificação Comercial
        long startTimeCom = System.nanoTime();
        final List<ConfiguracaoPrecificacaoComercial> configsPrecificacaoComercial = configuracaoPrecificacaoComercialService.findByEmpresa(sessaoPrecificacao.getEmpresa().getId(), sessaoPrecificacao.isBuscaDeTeste());
        long endTimeCom = System.nanoTime();
        long durationCom = (endTimeCom - startTimeCom);
        LOG.debug("Tempo prec comercial em " + durationCom / 1000000 + "ms");

        // Parcelamento v2, mesmo que eh usado pelo serviço de installments do Atlas
        long startTimeInstall = System.nanoTime();
        List<CondicaoPagamento> condicoes = null;
        if (sessaoPrecificacao.isLoadInstallments()) {
            condicoes = condicaoPagamentoService.findByEmpresa(sessaoPrecificacao.getEmpresa().getId(), sessaoPrecificacao.isBuscaDeTeste());
        }
        final List<CondicaoPagamento> configsInstallments = sessaoPrecificacao.isLoadInstallments() ? condicoes : Collections.emptyList();
        long endTimeInstall = System.nanoTime();
        long durationInstall = (endTimeInstall - startTimeInstall);
        LOG.debug("Tempo installments em " + durationInstall / 1000000 + "ms");

        // Taxa PERSE
        List<FlagFeature> flagFeatures = flagFeatureService.findAll();
        String taxaPerseStr = flagFeatures.stream()
                .filter(f -> f.getFlag().equalsIgnoreCase(FlagFeatureKeys.TAXA_PERSE.name()) && f.getAtivo())
                .findFirst()
                .map(FlagFeature::getValor)
                .orElse("0");
        BigDecimal taxaPerse = new BigDecimal(taxaPerseStr);

        // Configurações de filtros
        List<ConfiguracaoFiltrosRecomendacao> configsFiltrosRecomendacao = configuracaoFiltroRecomendacaoService.findByEmpresa(sessaoPrecificacao.getEmpresa().getId(), sessaoPrecificacao.isBuscaDeTeste());

        // Configurações de Produtos
        List<ConfiguracaoProduto> configsProdutos = configuracaoProdutoService.findAll();

        AtomicBoolean isError = new AtomicBoolean(false);
        List<Precificavel> excludedByError = new ArrayList<>(Collections.emptyList());
        List<Precificavel> excludedByBusiness = new ArrayList<>(Collections.emptyList());
        List<ResultadoPrecificacao> resultados = precificaveis
                .stream().filter(prec -> !prec.isExcludedByFilter())
                .map(precificavel -> {
                    Optional<ResultadoPrecificacao> result = null;
                    try {
                        isError.set(false);
                        result = precificacaoRetriever.doPrecificacao(precificavel, sessaoPrecificacao, configsRav, configsFee, configsDu, configsPrecificacaoComercial, configsInstallments, configsFiltrosRecomendacao, configsProdutos, taxaPerse);
                    } catch (Throwable e) {
                        // tratativa apenas para conseguir logar os erros no kibana
                        result = Optional.empty();
                        isError.set(true);
                        excludedByError.add(precificavel);
                    }

                    if (!result.isPresent()) {
                        if (!isError.get()) {
                            excludedByBusiness.add(precificavel);
                        }
                        if (sessaoPrecificacao.isBuscaDeTeste()) {
                            excludedResults.add(precificavel);
                            this.persistPrecificavel(precificavel, sessaoPrecificacao);
                        }
                    }

                    if (result.isPresent() && sessaoPrecificacao.isBuscaDeTeste()) {
                        precificavel.setPrecificacaoIds(result.get().getIdsPrecificacao());
                        precificavel.setIdConfigRestrita(result.get().getIdConfigRestrita());
                        precificavel.setIdConfigSucessor(result.get().getIdConfigSucessor());
                        precificavel.setCondicaoPrecificacao(result.get().getCondicaoPrecificacao());
                        precificavel.setConfiguracaoFeeId(result.get().getConfiguracaoFeeId());
                        precificavel.setConfiguracaoRavId(result.get().getConfiguracaoRavId());

                        this.persistPrecificavel(precificavel, sessaoPrecificacao);
                    }
                    return result;
                })
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());

        excludedResults.stream().map(ExcludedResultCreator::createExcludedResult).forEach(resultados::add);

        if (sessaoPrecificacao.isBuscaDeTeste()) {
            excludedResults.forEach(precificavel -> {
                this.persistPrecificavel(precificavel, sessaoPrecificacao);
            });
        }

        long endTime = System.nanoTime();
        long duration = (endTime - startTime);
        LOG.info("Entrada: " + precificaveis.size() + " - Saida: " + resultados.size() + " - excluidosNegocio: " + excludedByBusiness.size() + " - excluidosErro: " + excludedByError.size() + " - Tempo de processamento: " + duration / 1000000 + "ms");

        return resultados;
    }

    @Trace
    @CriticalPerformance
    public List<RegraTarifaAerea> aplicaPerfilTarifario(List<Precificavel> precificaveis) {
        if (CollectionUtils.isEmpty(precificaveis))
            throw new RuntimeException("Lista de recomendações para aplicação de perfil tarifario está vazia");

        //Isso é para não perder as informações de sessão dentro do método de precificação
        final RequestMetadata requestMetadata = sessaoComponent.getRequestMetadata();
        SecurityContext context = SecurityContextHolder.getContext();

        return new ArrayList<>(
                precificaveis.parallelStream().peek(precificavel -> sessaoComponent.setRequestMetadata(requestMetadata))
                        .peek(precificavel -> SecurityContextHolder.setContext(context))
                        .map(this::doPerfilTarifario)
                        .filter(CollectionUtils::isNotEmpty)
                        .flatMap(Collection::stream)
                        .collect(Collectors.toMap(RegraTarifaAerea::obtemChaveRegraTarifaAerea, r -> r, (r, q) -> r, LinkedHashMap::new))
                        .values());
    }

    @Trace
    @CriticalPerformance
    private List<RegraTarifaAerea> doPerfilTarifario(Precificavel precificavel) {
        List<RegraTarifaAerea> regras = new ArrayList<>();
        precificavel.getTarifacoes().stream().map(Tarifacao::getRegrasTarifaAerea).flatMap(Collection::stream).filter(Objects::nonNull).distinct().forEach(regraTarifaAerea -> {
            PerfilTarifario perfilTarifario = this.buscaConfiguracaoPerfilTarifario(regraTarifaAerea, precificavel);
            if (perfilTarifario != null) {
                regraTarifaAerea.setPerfilTarifario(perfilTarifario);
                regras.add(regraTarifaAerea);
            }
        });

        return regras;
    }

    private PerfilTarifario buscaConfiguracaoPerfilTarifario(RegraTarifaAerea regraTarifaAerea, Precificavel precificavel) {
        Optional<PerfilTarifario> perfilTarifarioPorRestricao = this.perfilTarifarioService.getFirstMatchConfiguracaoPerfilTarifarioPorRestricao(regraTarifaAerea, precificavel, false, "");
        return perfilTarifarioPorRestricao.orElse(null);
    }

    /**
     * This method persist the precificaveis to be recovery by front to debug
     */
    private void persistPrecificavel(Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao) {
        this.precificavelActor.tell(new PrecificavelActor.PrecificavelAkka(precificavel, sessaoPrecificacao), ActorRef.noSender());
    }

    public Pair<ResultadoPrecificacaoView, List<MotivoExclusaoVoo>> precificaComConfiguracaoEspecifica(Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao) {
        return precificacaoRetriever.precificaComConfiguracaoEspecifica(precificavel, sessaoPrecificacao);
    }

    @Component
    static class PrecificacaoRetriever {

        private ConfiguracaoPrecificacaoService configPrecificacaoService;

        private ConfiguracaoFiltroRecomendacaoService configuracaoFiltroRecomendacaoService;

        private ConfiguracaoPrecificacaoFretamentoService configPrecificacaoFretamentoService;

        private AutenticacaoService autenticacaoService;

        private CambioService cambioService;

        private AplicadorCodigoContratoService aplicadorCodigoContrato;

        private ConfiguracaoDesativaValorService configuracaoDesativaValorService;

        private Cloner cloner;

        private RestricaoService restricaoService;

        private AplicadorTaxaOBFeeService aplicadorTaxaOBFeeService;

        private FlagFeatureService flagFeatureService;

        private InternacionalizacaoComponent internacionalizacaoComponent;

        private ConfiguracaoRavService configuracaoRavService;

        private ConfiguracaoFeeService configuracaoFeeService;

        private ConfiguracaoPrecificacaoComercialService configuracaoPrecificacaoComercialService;

        private ConfiguracaoDuService configuracaoDuService;

        private CondicaoPagamentoService condicaoPagamentoService;

        private ConfiguracaoProdutoService configuracaoProdutoService;

        @Autowired
        public PrecificacaoRetriever(ConfiguracaoPrecificacaoService configPrecificacaoService, ConfiguracaoFiltroRecomendacaoService configuracaoFiltroRecomendacaoService, ConfiguracaoRavService configuracaoRavService, ConfiguracaoPrecificacaoFretamentoService configPrecificacaoFretamentoService,
                                     ConfiguracaoFeeService configuracaoFeeService, AutenticacaoService autenticacaoService, CambioService cambioService, AplicadorCodigoContratoService aplicadorCodigoContrato,
                                     ConfiguracaoDesativaValorService configuracaoDesativaValorService, @Qualifier("defaultCloner") Cloner cloner, RestricaoService restricaoService, ConfiguracaoPrecificacaoComercialService configuracaoPrecificacaoComercialService,
                                     AplicadorTaxaOBFeeService aplicadorTaxaOBFeeService, FlagFeatureService flagFeatureService, ConfiguracaoDuService configuracaoDuService, InternacionalizacaoComponent internacionalizacaoComponent,
                                     CondicaoPagamentoService condicaoPagamentoService,
                                     ConfiguracaoProdutoService configuracaoProdutoService) {
            this.configPrecificacaoService = configPrecificacaoService;
            this.configuracaoFiltroRecomendacaoService = configuracaoFiltroRecomendacaoService;
            this.configuracaoRavService = configuracaoRavService;
            this.configPrecificacaoFretamentoService = configPrecificacaoFretamentoService;
            this.configuracaoFeeService = configuracaoFeeService;
            this.autenticacaoService = autenticacaoService;
            this.cambioService = cambioService;
            this.aplicadorCodigoContrato = aplicadorCodigoContrato;
            this.configuracaoDesativaValorService = configuracaoDesativaValorService;
            this.cloner = cloner;
            this.restricaoService = restricaoService;
            this.configuracaoPrecificacaoComercialService = configuracaoPrecificacaoComercialService;
            this.aplicadorTaxaOBFeeService = aplicadorTaxaOBFeeService;
            this.flagFeatureService = flagFeatureService;
            this.configuracaoDuService = configuracaoDuService;
            this.internacionalizacaoComponent = internacionalizacaoComponent;
            this.condicaoPagamentoService = condicaoPagamentoService;
            this.configuracaoProdutoService = configuracaoProdutoService;
        }

        @CriticalPerformance
        public Optional<ResultadoPrecificacao> doPrecificacao(Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao, List<ConfiguracaoRav> configsRav,
                                                              List<ConfiguracaoFee> configsFee, List<ConfiguracaoDu> configsDu, List<ConfiguracaoPrecificacaoComercial> configsPrecificacaoComercial,
                                                              List<CondicaoPagamento> configsInstallments, List<ConfiguracaoFiltrosRecomendacao> configsFiltrosRecomendacao,
                                                              List<ConfiguracaoProduto> configuracaoProduto, BigDecimal taxaPerse) throws Throwable {
            try {
                if (isPrecificavelInvalid(precificavel)) {
                    return Optional.empty();
                }

                final Empresa empresa = sessaoPrecificacao.getEmpresa();
                final Integer grupoId = autenticacaoService.currentGrupoId() != null ? autenticacaoService.currentGrupoId().intValue() : null;
                final Long filialId = autenticacaoService.currentFilialId();
                final String codigoCia = precificavel.getCiaAereaViagem();
                final TipoTarifaAcordo tipoTarifaAcordo = precificavel.getTipoTarifaAcordo();
                final NacInt nacInt = this.isNacional(precificavel);
                final String produto = precificavel.getProdutoPrecificacao();

                this.aplicadorCodigoContrato.aplicaCodigoContratoSeNecessario(precificavel);

                List<ConfiguracaoPrecificacao> configsPrecificacao = new ArrayList<>();
                List<Restricao> restricoes = restricaoService.getRestricoesByPrecificavel(precificavel);

                if (CollectionUtils.isEmpty(precificavel.getPrecificacaoIds())) {
                    // Verifica e valida se deve excluir precificavel por configuração de filtro
                    if (CollectionUtils.isNotEmpty(configsFiltrosRecomendacao)) {
                        // Validação das configurações de filtros
                        List<RecomendacaoAereaFilter> recomendacaoAereaFilters = this.configuracaoFiltroRecomendacaoService.filterConfiguracaoFiltroPorPrecificavel(configsFiltrosRecomendacao, restricoes, precificavel, sessaoPrecificacao);
                        if (CollectionUtils.isNotEmpty(recomendacaoAereaFilters)) {
                            for (RecomendacaoAereaFilter filter : recomendacaoAereaFilters) {
                                if (!filter.filter(precificavel)) {
                                    precificavel.setExcludedByFilter(filter.getNomeFiltro());
                                    return Optional.empty();
                                }
                            }
                        }
                    }

                    // Valida se deve excluir precificavel por configuração de exclusão
                    if (configPrecificacaoService.deveExcluirVooPorConfiguracaoDeExclusao(restricoes, precificavel, sessaoPrecificacao)) {
                        return Optional.empty();
                    }

                    if (flagFeatureService.isActive(FlagFeatureKeys.PRECIFICACAO_FRETAMENTO) && (SistEmis.CHARTER.equals(precificavel.getSistEmis()) || SistEmis.FRETAMENTO.equals(precificavel.getSistEmis()))) {
                        configsPrecificacao = this.configPrecificacaoFretamentoService.findByEmpresa(precificavel, sessaoPrecificacao);
                    }
                    if(configsPrecificacao.isEmpty()){
                        configsPrecificacao = this.buscaConfiguracaoPrecificacaoPorRestricoes(restricoes, precificavel, sessaoPrecificacao);
                    }

                } else {
                    for (Integer key : precificavel.getPrecificacaoIds()) {
                        ConfiguracaoPrecificacao prec = configPrecificacaoService.findByKey(key);
                        if (prec != null && prec.getId() != null) {
                            configsPrecificacao.add(prec);
                        }
                    }
                }

                ConfiguracaoPrecificacao configRestrita = this.getConfigRestrita(configsPrecificacao);
                Optional<ConfiguracaoPrecificacao> configSucessor = this.getConfigSucessor(configsPrecificacao);

                if (configRestrita == null) {
                    return Optional.empty();
                }

                List<Integer> idsConfigsPadroes = this.getIdsConfigsPadroes(configsPrecificacao);
                boolean isEmissionOnline = true;
                if (CollectionUtils.isNotEmpty(configsPrecificacao)) {
                    isEmissionOnline = configsPrecificacao.stream().allMatch(ConfiguracaoPrecificacao::isForcarEmissao);
                }

                ResultadoPrecificacao resultadoPrecificacao = new ResultadoPrecificacao();
                resultadoPrecificacao.setIdAgrupamento(precificavel.getIdAgrupamento());
                resultadoPrecificacao.setIdUnidadePrecificavel(precificavel.getIdUnidadePrecificavel());
                resultadoPrecificacao.setCodigoContrato(precificavel.getCodigoContrato());
                resultadoPrecificacao.setContratoProprio(precificavel.getContratoProprio());
                resultadoPrecificacao.setProdutoUtilizado(produto);
                resultadoPrecificacao.setValoresDesativados(this.configuracaoDesativaValorService.findByProdutoEmpresa(empresa.getId(), produto));
                resultadoPrecificacao.setIdsConfigsPadroes(idsConfigsPadroes);
                resultadoPrecificacao.setIdConfigRestrita(configRestrita.getId());
                resultadoPrecificacao.setTourcode(configRestrita.getTourcode());
                resultadoPrecificacao.setEndosso(configRestrita.getEndosso());
                resultadoPrecificacao.setOsi(configRestrita.getOsi());
                resultadoPrecificacao.setEmissionOnline(isEmissionOnline);
                resultadoPrecificacao.setCondicaoPrecificacao(configRestrita.getCondicaoPrecificacao());
                resultadoPrecificacao.getDetalhesPrecificacao().put(TipoDetalhePrecificacao.ID_CONF_PRECIFICACAO, String.valueOf(configRestrita.getId()));

                if (configSucessor.isPresent()) {
                    resultadoPrecificacao.setIdConfigSucessor(configSucessor.get().getId());
                    configsPrecificacao.remove(configRestrita);
                    configRestrita.setMarkups(configSucessor.get().getMarkups());
                }


                List<ConfiguracaoProduto> configProduto = this.configuracaoProdutoService.filterConfiguracaoProdutoPorPrecificavel(configuracaoProduto, precificavel, filialId);
                MarkupProduto markupProduto = configRestrita.getMarkupPorProduto(produto);
                if (markupProduto == null) {
                    return Optional.empty();
                } else {
                    resultadoPrecificacao.setRequiredHotel(!configProduto.isEmpty());
                }

                BigDecimal fatorMarkupLiquidoOuBruto = configRestrita.getTipoMarkup().isLiquido() ?
                        this.getValorEmFatorDivisao(configRestrita.getFatorMarkupLiquido(markupProduto.getMarkupBusca())) :
                        markupProduto.getMarkupEmFatorDivisao();

                resultadoPrecificacao.addMarkupProduto(markupProduto.getId(), markupProduto.getIdReference(), markupProduto.getMarkupEmFatorDivisao(), fatorMarkupLiquidoOuBruto,
                        configRestrita.getComissaoFornecedor(), configRestrita.getIncentivoFornecedor());

                ConfiguracaoRav configuracaoRav = ConfiguracaoRav.UNDEF;
                Optional<ConfiguracaoRav> firstMatchConfiguracaoRav = this.configuracaoRavService.filterConfiguracaoRavPorPrecificavel(configsRav, codigoCia, produto, tipoTarifaAcordo, nacInt, grupoId);
                if (firstMatchConfiguracaoRav.isPresent()) {
                    configuracaoRav = firstMatchConfiguracaoRav.get();
                }

                ConfiguracaoFee configuracaoFee = ConfiguracaoFee.UNDEF;
                Optional<ConfiguracaoFee> optionalConfiguracaoFee = this.configuracaoFeeService.filterConfiguracaoFeePorPrecificavel(configsFee, restricoes, precificavel, sessaoPrecificacao);
                if (optionalConfiguracaoFee.isPresent()) {
                    configuracaoFee = optionalConfiguracaoFee.get();
                }

                for (int i = 0; i < precificavel.getTarifacoes().size(); i++) {
                    Tarifacao tarifacao = precificavel.getTarifacoes().get(i);

                    if (tarifacao.getId() == null) {
                        tarifacao.setId(FaixaEtaria.ADT.name());
                    }
                    if (tarifacao.getFaixaEtaria() == null) {
                        tarifacao.setFaixaEtaria(FaixaEtaria.ADT);
                    }

                    ValorTarifa tarifaPrecificada = this.getTarifaPrecificada(configsPrecificacao, configuracaoRav, configuracaoFee, configsDu,
                            tarifacao.getValorTarifa(), produto, markupProduto, sessaoPrecificacao.getCambio(), codigoCia, nacInt,
                            tarifacao.getFaixaEtaria(), taxaPerse);

                    resultadoPrecificacao.addValorCalculado(tarifacao.getId(), tarifaPrecificada);

                    // detalhes da precificacao
                    if (tarifaPrecificada.getValoresDetalhesTarifa().get(PERCENTAGE_FEE) != null && configuracaoFee.getId() != null) {
                        resultadoPrecificacao.setConfiguracaoFeeId(configuracaoFee.getId());
                        resultadoPrecificacao.getDetalhesPrecificacao().put(TipoDetalhePrecificacao.ID_CONF_FEE, String.valueOf(configuracaoFee.getId()));
                    }

                    if (tarifaPrecificada.getValoresDetalhesTarifa().get(PERCENTAGE_RAV) != null && configuracaoRav.getId() != null) {
                        resultadoPrecificacao.setConfiguracaoRavId(configuracaoRav.getId());
                        resultadoPrecificacao.getDetalhesPrecificacao().put(TipoDetalhePrecificacao.ID_CONF_RAV, String.valueOf(configuracaoRav.getId()));
                    }

                    // Atualiza precificavel com tarifa precificada
                    precificavel.getTarifacoes().get(i).setValorTarifa(tarifaPrecificada);
                }

                // Gera restrições pós precificação
                restricoes.addAll(restricaoService.getRestricoesPosPrecificacao(precificavel));

                // Precificação Comercial
                Optional<ConfiguracaoPrecificacaoComercial> configuracaoPrecificacaoComercialOpt = this.configuracaoPrecificacaoComercialService.filterConfiguracaoComercialPorPrecificavel(configsPrecificacaoComercial, restricoes, precificavel, sessaoPrecificacao, configRestrita.getCondicaoPrecificacao());
                if (configuracaoPrecificacaoComercialOpt.isPresent()) {
                    configuracaoPrecificacaoComercialOpt.filter(confComercial -> CollectionUtils.isNotEmpty(confComercial.getProdutos()))
                            .flatMap(confComercial -> confComercial.getProdutos().stream()
                                    .filter(Objects::nonNull)
                                    .filter(produtoComercial -> produtoComercial.getProduto() != null && produtoComercial.getProduto().getNome() != null && produtoComercial.getProduto().getNome().equalsIgnoreCase(produto))
                                    .filter(Objects::nonNull)
                                    .findFirst())
                            .ifPresent(confComercialProduto -> {
                                resultadoPrecificacao.getDetalhesPrecificacao().put(TipoDetalhePrecificacao.ID_CONF_PRECIFICACAO_COMERCIAL, String.valueOf(confComercialProduto.getConfiguracaoPrecificacaoComercialId()));
                                resultadoPrecificacao.setConfiguracaoPrecificacaoComercial(confComercialProduto.getConfiguracaoPrecificacaoComercialId());

                                precificavel.getTarifacoes().forEach(tarifacao -> {
                                    ValorTarifa tarifaPrecificada = tarifacao.getValorTarifa();
                                    tarifaPrecificada.insereValor(TipoValor.COMISSAO_CLIENTE, this.calculaValoresCliente(tarifaPrecificada, confComercialProduto.getComissaoCliente()));
                                    tarifaPrecificada.insereValor(TipoValor.INCENTIVO_CLIENTE, this.calculaValoresCliente(tarifaPrecificada, confComercialProduto.getIncentivoCliente()));
                                    tarifaPrecificada.insereValor(TipoValor.REPASSE_CLIENTE, this.calculaValoresCliente(tarifaPrecificada, confComercialProduto.getRepasseCliente()));
                                    tarifaPrecificada.insereValor(TipoValor.PORCENTAGEM_COMISSAO_FORNECEDOR, confComercialProduto.getComissaoCliente());
                                    tarifaPrecificada.insereValor(TipoValor.PORCENTAGEM_INCENTIVO_CLIENTE, confComercialProduto.getIncentivoCliente());
                                    tarifaPrecificada.insereValor(TipoValor.PORCENTAGEM_REPASSE_CLIENTE, confComercialProduto.getRepasseCliente());
                                    resultadoPrecificacao.addValorCalculado(tarifacao.getId(), tarifaPrecificada);
                                });
                            });
                }
                resultadoPrecificacao.setMerchant(obtemMerchant(resultadoPrecificacao.getValoresCalculados()));

                //APLICA CALCULO OBFEE SE NECESSÁRIO
                boolean deveAplicarTaxaOb = this.aplicadorTaxaOBFeeService.deveAplicarTaxaOb(precificavel, empresa, resultadoPrecificacao.getMerchant());
                if (!deveAplicarTaxaOb) {
                    resultadoPrecificacao.removeTaxaCalculada(TipoValor.OB_FEE);
                } else {
                    Optional<ObfeeCredencial> obfeeCredencialOptional = this.aplicadorTaxaOBFeeService.buscaObfeeCredencial(precificavel);
                    obfeeCredencialOptional.ifPresent(obfeeCredencial -> {
                        ResultadoCalculoOBFee resultadoCalculoOBFee = this.aplicadorTaxaOBFeeService.aplicaTaxa(obfeeCredencial, resultadoPrecificacao.getValoresCalculados());
                        resultadoPrecificacao.setValoresCalculados(resultadoCalculoOBFee.getValoresCalculados());
                    });
                }

                // Parcelamento v2, mesmo que eh usado pelo serviço de installments do Atlas
                if (sessaoPrecificacao.isLoadInstallments()) {
                    resultadoPrecificacao.setOpcoesParcelamentoResponse(condicaoPagamentoService.obtemOpcaoParcelamentoInterno(configsInstallments, precificavel));
                }

                return Optional.of(resultadoPrecificacao);
            } catch (BusinessException b) {
                StringBuilder recNaoPrecificada = new StringBuilder();
                recNaoPrecificada.append(" - Usuario: ").append(autenticacaoService.currentUsuario().getLogin());
                recNaoPrecificada.append(" - SistEmis: ").append(precificavel.getSistEmis());
                recNaoPrecificada.append(" - CiaAereaViagem: ").append(precificavel.getCiaAereaViagem());
                recNaoPrecificada.append(" - ProdutoPrecificacao: ").append(precificavel.getProdutoPrecificacao());
                if (CollectionUtils.isNotEmpty(precificavel.getSegmentos())) {
                    for (int i = 0; i < precificavel.getSegmentos().size(); i++) {
                        recNaoPrecificada.append(" - Segmento").append(i).append(" aeroportoOrigem: ").append(precificavel.getSegmentos().get(i).getAeroportoOrigem()).append(" - aeroportoDestino: ").append(precificavel.getSegmentos().get(i).getAeroportoDestino());
                    }
                }
                if (CollectionUtils.isNotEmpty(precificavel.getTarifacoes())) {
                    BigDecimal valorTotalViagem = BigDecimal.ZERO;
                    for (int i = 0; i < precificavel.getTarifacoes().size(); i++) {
                        if (precificavel.getTarifacoes().get(i).getValorTarifa() != null) {
                            valorTotalViagem = valorTotalViagem.add(precificavel.getTarifacoes().get(i).getValorTarifa().getTotal().getValorFinal());
                        }
                    }
                    recNaoPrecificada.append(" - TotalTarifaViagem: ").append(valorTotalViagem);
                }

                LOG.info("Precificavel não precificado por causa : " + b.getMessage() + recNaoPrecificada);
                throw new Throwable("Precificavel não precificado por causa : " + b.getMessage() + recNaoPrecificada);
                //            return Optional.empty();
            } catch (Throwable t) {
                String causa = t.getMessage();
                if (causa == null) {
                    causa = t.toString();
                }
                if (ArrayUtils.isNotEmpty(t.getStackTrace())) {
                    causa = causa + " - stackTrace: " + t.getStackTrace()[0].toString();
                }
                LOG.error("Erro precificando Precificavel: {'precificaveis':["
                        + JsonConverterUtil.asJsonStringByObjectWithoutAnnotation(precificavel)
                        + "]} | ParametroSessao: "
                        + JsonConverterUtil.asJsonStringByObjectWithoutAnnotation(sessaoPrecificacao)
                        + " | causa: " + causa, t.getStackTrace());
                //            return Optional.empty();
                throw new Throwable("Erro precificando Precificavel ! - causa: " + causa);
            }
        }

        public Optional<ResultadoPrecificacao> doPrecificacaoBest(Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao) throws Throwable {

            try {
                if (isPrecificavelInvalid(precificavel)) {
                    return Optional.empty();
                }

                final String codigoCia = precificavel.getCiaAereaViagem();
                final NacInt nacInt = this.isNacional(precificavel);
                final String produto = "BPRICE";

                this.aplicadorCodigoContrato.aplicaCodigoContratoSeNecessario(precificavel);

                List<Restricao> restricoes = restricaoService.getRestricoesByPrecificavel(precificavel);

                List<ConfiguracaoPrecificacao> configsPrecificacao = this.buscaConfiguracaoPrecificacaoPorRestricoesBestPrice(restricoes, precificavel, sessaoPrecificacao);

                ConfiguracaoPrecificacao configRestrita = configsPrecificacao != null && !configsPrecificacao.isEmpty() ? this.getConfigRestrita(configsPrecificacao) : null;

                // loop somente para pegar as tarifas originais recebidas
                Map<String, ValorTarifa> tarifasOriginais = new HashMap<>();
                for (int i = 0; i < precificavel.getTarifacoes().size(); i++) {
                    Tarifacao tarifacao = precificavel.getTarifacoes().get(i);
                    tarifasOriginais.put(tarifacao.getId(), cloner.deepClone(tarifacao.getValorTarifa()));
                }

                if (configRestrita == null) {
                    return Optional.of(criaResultadoComDadosOriginais(precificavel, tarifasOriginais));
                }

                ResultadoPrecificacao resultadoPrecificacao = new ResultadoPrecificacao();
                resultadoPrecificacao.setIdAgrupamento(precificavel.getIdAgrupamento());
                resultadoPrecificacao.setIdUnidadePrecificavel(precificavel.getIdUnidadePrecificavel());
                resultadoPrecificacao.setCodigoContrato(precificavel.getCodigoContrato());
                resultadoPrecificacao.setContratoProprio(precificavel.getContratoProprio());
                resultadoPrecificacao.setProdutoUtilizado(precificavel.getProdutoPrecificacao());
                resultadoPrecificacao.setIdConfigRestrita(configRestrita.getId());
                resultadoPrecificacao.setTourcode(configRestrita.getTourcode());
                resultadoPrecificacao.setEndosso(configRestrita.getEndosso());
                resultadoPrecificacao.setOsi(configRestrita.getOsi());
                resultadoPrecificacao.setCondicaoPrecificacao(configRestrita.getCondicaoPrecificacao());
                resultadoPrecificacao.getDetalhesPrecificacao().put(TipoDetalhePrecificacao.ID_CONF_PRECIFICACAO, String.valueOf(configRestrita.getId()));

                MarkupProduto markupProduto = configRestrita.getMarkupPorProduto(produto);
                if (markupProduto == null) {
                    return Optional.of(criaResultadoComDadosOriginais(precificavel, tarifasOriginais));
                }

                BigDecimal fatorMarkupLiquido = this.getValorEmFatorDivisao(configRestrita.getFatorMarkupLiquido(markupProduto.getMarkupBusca()));

                resultadoPrecificacao.addMarkupProduto(markupProduto.getId(), markupProduto.getIdReference(), markupProduto.getMarkupEmFatorDivisao(), fatorMarkupLiquido,
                        configRestrita.getComissaoFornecedor(), configRestrita.getIncentivoFornecedor());

                boolean atendeMarkupMinimo = true;

                for (int i = 0; i < precificavel.getTarifacoes().size(); i++) {
                    Tarifacao tarifacao = precificavel.getTarifacoes().get(i);

                    if (tarifacao.getId() == null) {
                        tarifacao.setId(FaixaEtaria.ADT.name());
                    }
                    if (tarifacao.getFaixaEtaria() == null) {
                        tarifacao.setFaixaEtaria(FaixaEtaria.ADT);
                    }

                    tarifasOriginais.put(tarifacao.getId(), cloner.deepClone(tarifacao.getValorTarifa()));

                    ResultadoBestPrice resultadoBestPrice = this.getTarifaPrecificadaBestPriceComValidacao(configsPrecificacao, tarifacao.getValorTarifa(), produto, markupProduto, sessaoPrecificacao.getCambio(), codigoCia, nacInt, tarifacao.getFaixaEtaria());

                    resultadoPrecificacao.addValorCalculado(tarifacao.getId(), resultadoBestPrice.getTarifaPrecificada());

                    if (!resultadoBestPrice.isAtendeMarkupMinimo()) {
                        atendeMarkupMinimo = false;
                    }

                    // Atualiza precificavel com tarifa precificada
                    precificavel.getTarifacoes().get(i).setValorTarifa(resultadoBestPrice.getTarifaPrecificada());
                }

                if (!atendeMarkupMinimo) {
                    return Optional.of(criaResultadoComDadosOriginais(precificavel, tarifasOriginais));
                }

                resultadoPrecificacao.setMerchant(obtemMerchant(resultadoPrecificacao.getValoresCalculados()));

                return Optional.of(resultadoPrecificacao);
            } catch (BusinessException b) {
                StringBuilder recNaoPrecificada = new StringBuilder();
                recNaoPrecificada.append(" - Usuario: ").append(autenticacaoService.currentUsuario().getLogin());
                recNaoPrecificada.append(" - SistEmis: ").append(precificavel.getSistEmis());
                recNaoPrecificada.append(" - CiaAereaViagem: ").append(precificavel.getCiaAereaViagem());
                recNaoPrecificada.append(" - ProdutoPrecificacao: ").append(precificavel.getProdutoPrecificacao());
                if (CollectionUtils.isNotEmpty(precificavel.getSegmentos())) {
                    for (int i = 0; i < precificavel.getSegmentos().size(); i++) {
                        recNaoPrecificada.append(" - Segmento").append(i).append(" aeroportoOrigem: ").append(precificavel.getSegmentos().get(i).getAeroportoOrigem()).append(" - aeroportoDestino: ").append(precificavel.getSegmentos().get(i).getAeroportoDestino());
                    }
                }
                if (CollectionUtils.isNotEmpty(precificavel.getTarifacoes())) {
                    BigDecimal valorTotalViagem = BigDecimal.ZERO;
                    for (int i = 0; i < precificavel.getTarifacoes().size(); i++) {
                        if (precificavel.getTarifacoes().get(i).getValorTarifa() != null) {
                            valorTotalViagem = valorTotalViagem.add(precificavel.getTarifacoes().get(i).getValorTarifa().getTotal().getValorFinal());
                        }
                    }
                    recNaoPrecificada.append(" - TotalTarifaViagem: ").append(valorTotalViagem);
                }

                LOG.info("Precificavel não precificado por causa : " + b.getMessage() + recNaoPrecificada);
                throw new Throwable("Precificavel não precificado por causa : " + b.getMessage() + recNaoPrecificada);
                //            return Optional.empty();
            } catch (Throwable t) {
                String causa = t.getMessage();
                if (causa == null) {
                    causa = t.toString();
                }
                if (ArrayUtils.isNotEmpty(t.getStackTrace())) {
                    causa = causa + " - stackTrace: " + t.getStackTrace()[0].toString();
                }
                LOG.error("Erro precificando Precificavel: {'precificaveis':["
                        + JsonConverterUtil.asJsonStringByObjectWithoutAnnotation(precificavel)
                        + "]} | ParametroSessao: "
                        + JsonConverterUtil.asJsonStringByObjectWithoutAnnotation(sessaoPrecificacao)
                        + " | causa: " + causa, t.getStackTrace());
                //            return Optional.empty();
                throw new Throwable("Erro precificando Precificavel ! - causa: " + causa);
            }
        }

        private boolean isPrecificavelInvalid(Precificavel precificavel) {
            try {
                if (precificavel == null) {
                    LOG.info("Precificavel nulo !");
                    return true;
                }
                if (StringUtils.isEmpty(precificavel.getProdutoPrecificacao())) {
                    LOG.info("Precificavel sem produto ! - " + precificavel.toString());
                    return true;
                }
                if (CollectionUtils.isEmpty(precificavel.getFaixasEtarias())) {
                    LOG.info("Precificavel sem faixa etaria ! - " + precificavel.toString());
                    return true;
                }
                if (CollectionUtils.isEmpty(precificavel.getTarifacoes())) {
                    LOG.info("Precificavel sem tarifacao ! - " + precificavel.toString());
                    return true;
                }
                if (CollectionUtils.isEmpty(precificavel.getSegmentos())) {
                    LOG.info("Precificavel sem segmento ! - " + precificavel.toString());
                    return true;
                }
                if (precificavel.getSistEmis() == null || SistEmis.UNDEF == precificavel.getSistEmis() || SistEmis.TODOS == precificavel.getSistEmis()) {
                    LOG.info("Precificavel sem sistema emissor ! - " + precificavel.toString());
                    return true;
                }
            } catch (Exception e) {
                return true;
            }
            return false;
        }

        private NacInt isNacional(Precificavel precificavel) {
            return Optional.ofNullable(precificavel.isNacional())
                    .orElseGet(() -> precificavel.getAeroportos().stream().allMatch(a -> a.isNacional(internacionalizacaoComponent.obtemPaisOrigem()))) ? NacInt.NAC : NacInt.INT;
        }

        private Optional<ConfiguracaoPrecificacaoComercial> buscaConfiguracaoPrecificacaoComercial(List<Restricao> restricoes, Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao,
                                                                                                   String condicaoPrecificacao) {
            return this.configuracaoPrecificacaoComercialService.obterConfigPrecificacaoRestritaPorPrecificavel(restricoes, precificavel, sessaoPrecificacao, condicaoPrecificacao);
        }

        private ConfiguracaoPrecificacao getConfigRestrita(List<ConfiguracaoPrecificacao> configsPrecificacao) {
            return configsPrecificacao.stream().filter(config -> config.getTipoConfigPrecificacao() == RESTRITA).findFirst().orElse(null);
        }

        private Optional<ConfiguracaoPrecificacao> getConfigSucessor(List<ConfiguracaoPrecificacao> configsPrecificacao) {
            return configsPrecificacao.stream().filter(config -> config.getTipoConfigPrecificacao() == SUCESSOR).findFirst();
        }

        private List<Integer> getIdsConfigsPadroes(List<ConfiguracaoPrecificacao> configsPrecificacao) {
            return configsPrecificacao.stream().filter(config -> config.getTipoConfigPrecificacao() == PADRAO).map(ConfiguracaoPrecificacao::getId).collect(Collectors.toList());
        }

        private List<ConfiguracaoPrecificacao> buscaConfiguracaoPrecificacaoPorRestricoes(List<Restricao> restricoes, Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao) {
            final boolean hasDesconto = restricoes.stream()
                    .filter(restricao -> TipoRestricao.DESCONTO.equals(restricao.getTipoRestricao()))
                    .anyMatch(restricao -> restricao.getValor() != null && new BigDecimal(restricao.getValor()).compareTo(BigDecimal.ZERO) > 0);

            Optional<ConfiguracaoPrecificacao> configPrecificacaoRestrita = configPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(restricoes, precificavel, sessaoPrecificacao, hasDesconto);
            if (configPrecificacaoRestrita.isPresent()) {

                List<ConfiguracaoPrecificacao> configsPrecificacao = configPrecificacaoService.findPadraoByPrecificavel(restricoes, precificavel, sessaoPrecificacao, hasDesconto);
                configsPrecificacao.add(configPrecificacaoRestrita.get());

                Optional<ConfiguracaoPrecificacao> configPrecificacaoSucessor = configPrecificacaoService.findFirstSucessorByPrecificavel(restricoes, precificavel, sessaoPrecificacao, hasDesconto);
                if(configPrecificacaoSucessor.isPresent()) {
                    configsPrecificacao.add(configPrecificacaoSucessor.get());
                }
                return configsPrecificacao;
            } else {
                throw new BusinessException("Configuração de precificação do tipo restrita não encontrada!");
            }
        }

        private List<ConfiguracaoPrecificacao> buscaConfiguracaoPrecificacaoPorRestricoesBestPrice(List<Restricao> restricoes, Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao) {
            Optional<ConfiguracaoPrecificacao> configPrecificacaoRestrita = configPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavelBestPrice(restricoes, precificavel, sessaoPrecificacao);
            return configPrecificacaoRestrita.map(Collections::singletonList).orElse(null);
        }

        /**
         * Apenas em testes!.
         */
        private List<ConfiguracaoPrecificacaoComMotivoExclusao> buscaConfiguracoesPrecificacaoPorRestricoesEMotivosDaExclusao(List<Restricao> restricoes, Precificavel precificavel,
                                                                                                                              ParametroSessaoPrecificacao sessaoPrecificacao) {
            List<ConfiguracaoPrecificacao> configsPrecificacao = configPrecificacaoService.findPadraoByPrecificavel(restricoes, precificavel, sessaoPrecificacao);

            List<ConfiguracaoPrecificacaoComMotivoExclusao> pairsPadrao = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(configsPrecificacao)) {
                pairsPadrao = configsPrecificacao.stream().map(c -> ConfiguracaoPrecificacaoComMotivoExclusao.of(c, new MotivoExclusaoVoo())).collect(Collectors.toList());
            }

            List<ConfiguracaoPrecificacaoComMotivoExclusao> pairsRestritas = configPrecificacaoService.obterConfiguracoesPrecificacaoRestritaPorPrecificavel(restricoes, precificavel, sessaoPrecificacao);

            pairsPadrao.addAll(pairsRestritas);

            return pairsPadrao;
        }

        /**
         * Calcula os valores de taxas e sobretaxas das configurações de precificação e configuração de rav e devolve uma tarifa precificada contendo esses valores calculados
         *
         * @param configsPrecificacao As configurações de precificação a serem aplicadas
         * @param configuracaoRav     A configuração de rav a ser aplicada
         * @param configsDu
         * @param valorTarifa         valorTarifa com os valores a serem afetados pelas taxas
         * @param produtoPrecificacao produtoPrecificacao o produto utilizado para escolher o markup
         * @param codigoCia
         * @param nacInt
         * @param taxaPerse
         */
        @Trace
        private ValorTarifa getTarifaPrecificada(List<ConfiguracaoPrecificacao> configsPrecificacao, ConfiguracaoRav configuracaoRav, ConfiguracaoFee configuracaoFee, List<ConfiguracaoDu> configsDu,
                                                 ValorTarifa valorTarifa, String produtoPrecificacao, MarkupProduto markupProduto, Cambio cambio, String codigoCia, NacInt nacInt, FaixaEtaria faixaEtaria, BigDecimal taxaPerse) {
            ValorTarifa tarifaFinal = cloner.deepClone(valorTarifa);
            Map<TipoValor, BigDecimal> valoresCalculados = new HashMap<>();

            valoresCalculados.putAll(this.calculaTaxaMarkup(produtoPrecificacao, tarifaFinal, configsPrecificacao, taxaPerse));
            valoresCalculados.putAll(this.calculaTaxaFornecedor(produtoPrecificacao, tarifaFinal, configsPrecificacao));
            valoresCalculados.putAll(this.calculaRC(cambio, configsPrecificacao, produtoPrecificacao, tarifaFinal, faixaEtaria));

            final BigDecimal valorDU = tarifaFinal.getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.DU);
            if (markupProduto.getZeraDu()) {
                /** Coloca o valor da DU na Sobretaxa MarkupDU **/
                valoresCalculados.put(TipoValor.DU_ZERADA, valorDU);

                /** Remove a Taxa DU, para ficar somente o markupDU **/
                tarifaFinal.removeTaxaSobretaxaDetalheTarifaIndividual(TipoValor.DU);
            }

            aplicaValoresNaTarifa(tarifaFinal, valoresCalculados);

            if (configuracaoFee != null && configuracaoFee != ConfiguracaoFee.UNDEF && valorTarifa != null) {
                BigDecimal valorFee = this.calculaTaxaFee(configuracaoFee, tarifaFinal);
                tarifaFinal.insereValor(TipoValor.FEE, valorFee);
                tarifaFinal.insereValor(PERCENTAGE_FEE, getPercentageFee(configuracaoFee, valorFee, tarifaFinal));
            } else {
                tarifaFinal.insereValor(TipoValor.FEE, this.calculaTaxaFee(produtoPrecificacao, tarifaFinal, configsPrecificacao));
                tarifaFinal.insereValor(PERCENTAGE_FEE, getPercentageFee(produtoPrecificacao, configsPrecificacao));
            }

            /** Se tiver valor de Markup e DU e não tiver RAV recalcula o valor da DU considerando o Markup
             * e insere como RAV pois foi recalculado **/
            BigDecimal valorMarkup = tarifaFinal.getTaxaSobretaxaDetalheTarifaIndividual(MARKUP);
            if (!markupProduto.getZeraDu() && valorDU.compareTo(BigDecimal.ZERO) > 0 && valorMarkup.compareTo(BigDecimal.ZERO) > 0 && (configuracaoRav == null
                    || configuracaoRav == ConfiguracaoRav.UNDEF)) {
                BigDecimal valorRavDu = calculaRavDu(configsDu, tarifaFinal, codigoCia, nacInt);
                if (valorRavDu.compareTo(BigDecimal.ZERO) > 0 && valorRavDu.compareTo(valorDU) >= 0) {
                    /** Coloca o valor da DU na Sobretaxa MarkupDU **/
                    tarifaFinal.insereValor(TipoValor.DU_ZERADA, valorDU);
                    /** Remove a Taxa DU, para ficar somente o markupDU **/
                    tarifaFinal.removeTaxaSobretaxaDetalheTarifaIndividual(TipoValor.DU);
                    /** Coloca o valor da DU na Sobretaxa como RAV **/
                    tarifaFinal.insereValor(TipoValor.RAV, valorRavDu);
                    tarifaFinal.insereValor(PERCENTAGE_RAV, getPercentageRav(configuracaoRav, valorRavDu, tarifaFinal));
                }
            }

            if ((markupProduto.getZeraDu() || valorDU.compareTo(BigDecimal.ZERO) <= 0) && (tarifaFinal.getValoresSobretaxas().get(RAV) == null
                    || tarifaFinal.getValoresSobretaxas().get(RAV).compareTo(BigDecimal.ZERO) <= 0)) {
                tarifaFinal.insereValor(RAV, this.getValorRav(configuracaoRav, tarifaFinal));
                tarifaFinal.insereValor(PERCENTAGE_RAV, getPercentageRav(configuracaoRav, this.getValorRav(configuracaoRav, tarifaFinal), tarifaFinal));
            }

            BigDecimal vlrMkpFornecedor = markupProduto.getZeraMkpFornecedor() ? BigDecimal.ZERO : valorMarkup.setScale(2, RoundingMode.HALF_EVEN);
            tarifaFinal.insereValor(MARKUP_FORNECEDOR, vlrMkpFornecedor);

            BigDecimal valorTaxaBolsa = tarifaFinal.getTaxaSobretaxaDetalheTarifaIndividual(TAXA_BOLSA);
            BigDecimal vlrTaxaBolsaRC = markupProduto.getZeraTaxaBolsaRC() ? BigDecimal.ZERO : valorTaxaBolsa.setScale(2, RoundingMode.HALF_EVEN);
            tarifaFinal.insereValor(TAXA_BOLSA_FORNECEDOR, vlrTaxaBolsaRC);

            return tarifaFinal;
        }

        @Trace
        private ResultadoBestPrice getTarifaPrecificadaBestPriceComValidacao(List<ConfiguracaoPrecificacao> configsPrecificacao, ValorTarifa valorTarifa, String produtoPrecificacao,
                                                                             MarkupProduto markupProduto, Cambio cambio, String codigoCia, NacInt nacInt, FaixaEtaria faixaEtaria) {
            ValorTarifa tarifaComNovoMarkup = cloner.deepClone(valorTarifa);
            Map<TipoValor, BigDecimal> valoresCalculados = new HashMap<>();

            valoresCalculados.putAll(this.calculaTaxaMarkup(produtoPrecificacao, tarifaComNovoMarkup, configsPrecificacao, BigDecimal.ZERO));

            aplicaValoresNaTarifa(tarifaComNovoMarkup, valoresCalculados);

            BigDecimal valorMarkupCalculado = tarifaComNovoMarkup.getTaxaSobretaxaDetalheTarifaIndividual(MARKUP);
            BigDecimal markupMinimo = markupProduto.getMarkupMinimo();

            if (atendeMarkupMinimo(valorMarkupCalculado, markupMinimo)) {
                BigDecimal vlrMkpFornecedor = markupProduto.getZeraMkpFornecedor() ? BigDecimal.ZERO : valorMarkupCalculado.setScale(2, RoundingMode.HALF_EVEN);
                tarifaComNovoMarkup.insereValor(MARKUP_FORNECEDOR, vlrMkpFornecedor);

                return new ResultadoBestPrice(tarifaComNovoMarkup, true);
            } else {
                ValorTarifa tarifaAjustada = ajustaTarifaOriginalBestPrice(valorTarifa, markupProduto);
                return new ResultadoBestPrice(tarifaAjustada, false);
            }
        }

        private boolean atendeMarkupMinimo(BigDecimal valorMarkupCalculado, BigDecimal markupMinimo) {
            if (markupMinimo == null || markupMinimo.compareTo(BigDecimal.ZERO) == 0) {
                return true;
            }
            return valorMarkupCalculado.compareTo(markupMinimo) >= 0;
        }

        private ValorTarifa ajustaTarifaOriginalBestPrice(ValorTarifa tarifaOriginal, MarkupProduto markupProduto) {
            ValorTarifa tarifaAjustada = cloner.deepClone(tarifaOriginal);

            BigDecimal valorNeto = tarifaAjustada.getTarifa().getValorNeto();
            BigDecimal valorDesconto = tarifaAjustada.getTarifa().getValorDesconto();

            BigDecimal novoValorTarifa = valorNeto.add(valorDesconto);
            tarifaAjustada.getTarifa().setValorNeto(novoValorTarifa);
            tarifaAjustada.getTarifa().setValorDesconto(BigDecimal.ZERO);

            return tarifaAjustada;
        }

        private ResultadoPrecificacao criaResultadoComDadosOriginais(Precificavel precificavel, Map<String, ValorTarifa> tarifasOriginais) {
            ResultadoPrecificacao resultado = new ResultadoPrecificacao();

            resultado.setIdAgrupamento(precificavel.getIdAgrupamento());
            resultado.setIdUnidadePrecificavel(precificavel.getIdUnidadePrecificavel());
            resultado.setIdConfigRestrita(precificavel.getIdConfigRestrita());
            resultado.setConfiguracaoRavId(precificavel.getConfiguracaoRavId());
            resultado.setConfiguracaoFeeId(precificavel.getConfiguracaoFeeId());
            resultado.setCodigoContrato(precificavel.getCodigoContrato());
            resultado.setContratoProprio(precificavel.getContratoProprio());
            resultado.setProdutoUtilizado(precificavel.getProdutoPrecificacao());
            resultado.setCondicaoPrecificacao(precificavel.getCondicaoPrecificacao());

            if (CollectionUtils.isNotEmpty(precificavel.getPrecificacaoIds())) {
                resultado.setIdsConfigsPadroes(precificavel.getPrecificacaoIds());
            }

            for (Map.Entry<String, ValorTarifa> entry : tarifasOriginais.entrySet()) {
                ValorTarifa tarifaAjustada = cloner.deepClone(entry.getValue());

                BigDecimal valorNeto = tarifaAjustada.getTarifa().getValorNeto();
                BigDecimal valorDesconto = tarifaAjustada.getTarifa().getValorDesconto();

                BigDecimal novoValorTarifa = valorNeto.add(valorDesconto);
                tarifaAjustada.getTarifa().setValorNeto(novoValorTarifa);
                tarifaAjustada.getTarifa().setValorDesconto(BigDecimal.ZERO);

                resultado.addValorCalculado(entry.getKey(), tarifaAjustada);
            }

            return resultado;
        }

        private static class ResultadoBestPrice {
            private final ValorTarifa tarifaPrecificada;
            private final boolean atendeMarkupMinimo;

            public ResultadoBestPrice(ValorTarifa tarifaPrecificada, boolean atendeMarkupMinimo) {
                this.tarifaPrecificada = tarifaPrecificada;
                this.atendeMarkupMinimo = atendeMarkupMinimo;
            }

            public ValorTarifa getTarifaPrecificada() {
                return tarifaPrecificada;
            }

            public boolean isAtendeMarkupMinimo() {
                return atendeMarkupMinimo;
            }
        }

        private BigDecimal calculaRavDu(List<ConfiguracaoDu> configsDu, ValorTarifa tarifaFinal, String codigoCia, NacInt nacInt) {
            BigDecimal valorCalculado = BigDecimal.ZERO;
            ConfiguracaoDu configuracaoDu = configuracaoDuService.filterConfiguracaoDU(configsDu, codigoCia, nacInt);

            if (configuracaoDu != null) {
                BigDecimal valorFinalComMarkup = tarifaFinal.getTarifaComMarkup().getValorFinal();
                BigDecimal percentualDu = configuracaoDu.getPercentualDu();
                BigDecimal valorMinimo = configuracaoDu.getValorMinimo();

                BigDecimal valorRavDu = valorFinalComMarkup.multiply(percentualDu);
                valorCalculado = valorRavDu.max(valorMinimo);
            }
            return valorCalculado;
        }

        protected Map<TipoValor, BigDecimal> calculaRC(Cambio cambio, List<ConfiguracaoPrecificacao> configsPrecificacao, String produtoPrecificacao, ValorTarifa tarifaFinal, FaixaEtaria faixaEtaria) {
            Map<TipoValor, BigDecimal> valoresFinais = new HashMap<>();

            if (CollectionUtils.isNotEmpty(configsPrecificacao)) {
                configsPrecificacao.stream().map(configuracaoPrecificacao -> configuracaoPrecificacao.getMarkupPorProduto(produtoPrecificacao)).forEach(markupProduto -> {

                    BigDecimal taxaBolsa = Optional.ofNullable(valoresFinais.get(TAXA_BOLSA)).orElse(BigDecimal.ZERO);
                    if (markupProduto.isTaxaBolsaFixa(faixaEtaria)) {
                        if (!Moeda.BRL.equals(markupProduto.getMoedaRepasse())) {
                            taxaBolsa = taxaBolsa.add(markupProduto.getTaxaBolsaPorFaixaEtaria(faixaEtaria).multiply(cambio.getValor()));
                        } else {
                            taxaBolsa = taxaBolsa.add(markupProduto.getTaxaBolsaPorFaixaEtaria(faixaEtaria));
                        }
                        tarifaFinal.insereValor(PERCENTAGE_TAXA_BOLSA, getPercentTaxaBolsaRC(taxaBolsa, tarifaFinal));
                    } else {
                        BigDecimal fatorTaxaBolsa = markupProduto.getTaxaBolsaPercPorFaixaEtaria(faixaEtaria);
                        taxaBolsa = this.getValorTaxaBolsa(fatorTaxaBolsa, tarifaFinal);
                        tarifaFinal.insereValor(PERCENTAGE_TAXA_BOLSA, fatorTaxaBolsa);
                    }

                    tarifaFinal.setCodigoRcSica(markupProduto.getCodigoRcSica());
                    valoresFinais.put(TAXA_BOLSA, taxaBolsa);
                });
            }

            return valoresFinais;
        }

        /**
         * Calcula os valores de taxas de markup das configurações de precificação e aplica estes valores no ValorTarifa
         *
         * @param produto             produto o produto utilizado para escolher o markup
         * @param valorTarifa         valorTarifa os valores a serem afetados pelas taxas
         * @param configsPrecificacao As configurações de precificação a serem aplicadas (tipo 'padrão' e 'restrita')
         * @param taxaPerse
         */
        private Map<TipoValor, BigDecimal> calculaTaxaMarkup(String produto, ValorTarifa valorTarifa, List<ConfiguracaoPrecificacao> configsPrecificacao, BigDecimal taxaPerse) {
            Map<TipoValor, BigDecimal> valoresFinais = new HashMap<>();
            if (configsPrecificacao != null && !configsPrecificacao.isEmpty()) {
                for (ConfiguracaoPrecificacao conf : configsPrecificacao) {
                    if (conf != null) {
                        Map<TipoValor, BigDecimal> fatoresMarkup = conf.getFatoresMarkup(produto, conf.getTipoMarkup().isLiquido(), taxaPerse);
                        Map<TipoValor, BigDecimal> resultadoConf = calculaValores(new CalculationData(fatoresMarkup, conf.getMarkupPorProduto(produto), valorTarifa, conf.getTipoMarkup()));
                        valoresFinais = somaValores(resultadoConf, valoresFinais);
                    }
                }
            }
            return valoresFinais;
        }

        /**
         * Calcula os valores de taxas de fornecedores das configurações de precificação e aplica estes valores no ValorTarifa
         *
         * @param produto
         * @param valorTarifa         valorTarifa os valores a serem afetados pelas taxas
         * @param configsPrecificacao As configurações de precificação a serem aplicadas
         */
        private Map<TipoValor, BigDecimal> calculaTaxaFornecedor(String produto, ValorTarifa valorTarifa, List<ConfiguracaoPrecificacao> configsPrecificacao) {
            Map<TipoValor, BigDecimal> valoresFinais = new HashMap<>();
            if (configsPrecificacao != null && !configsPrecificacao.isEmpty()) {
                for (ConfiguracaoPrecificacao conf : configsPrecificacao) {
                    if (conf != null) {
                        Map<TipoValor, BigDecimal> fatoresFornecedor = conf.getFatoresFornecedor();
                        Map<TipoValor, BigDecimal> resultadoConf = calculaValores(new CalculationData(fatoresFornecedor, conf.getMarkupPorProduto(produto), valorTarifa, conf.getTipoMarkup()));
                        valoresFinais = somaValores(resultadoConf, valoresFinais);

                        valoresFinais.put(PORCENTAGEM_COMISSAO_FORNECEDOR, conf.getComissaoFornecedor());
                        valoresFinais.put(PORCENTAGEM_INCENTIVO_FORNECEDOR, conf.getIncentivoFornecedor());
                    }
                }
            }
            return valoresFinais;
        }

        /**
         * Calcula os valores de taxas de cliente das configurações de precificação comercial
         *
         * @param valorTarifa o valor tarifa a ser aplicado
         * @param fator       o valor do fator a calcular
         */
        private BigDecimal calculaValoresCliente(ValorTarifa valorTarifa, BigDecimal fator) {
            if (fator != null && fator.compareTo(BigDecimal.ZERO) > 0) {
                final BigDecimal valorFinal = valorTarifa.getTarifaComMarkup().getValorFinal();
                return CalculadorUtil.calculaValorFator(valorFinal, fator);
            }
            return BigDecimal.ZERO;
        }

        /**
         * Calcula os valores de taxas de fee das configurações de precificação
         *
         * @param configsPrecificacao As configurações de precificação a serem aplicadas
         * @param valorTarifa         o valor tarifa a ser aplicado
         */
        private BigDecimal calculaTaxaFee(String produto, ValorTarifa valorTarifa, List<ConfiguracaoPrecificacao> configsPrecificacao) {
            BigDecimal valorFeeSomado = BigDecimal.ZERO;
            if (configsPrecificacao != null && !configsPrecificacao.isEmpty()) {
                for (ConfiguracaoPrecificacao conf : configsPrecificacao) {
                    if (conf != null) {
                        BigDecimal fatorFee = conf.getFatoresMap(produto).get(TipoValor.FEE);
                        valorFeeSomado = valorFeeSomado.add(this.getValorFee(fatorFee, valorTarifa, conf.getTipoMarkup()));
                    }
                }
            }
            return valorFeeSomado;
        }

        private BigDecimal calculaTaxaFee(ConfiguracaoFee configuracaoFee, ValorTarifa valorTarifa) {
            if (configuracaoFee != null && configuracaoFee != ConfiguracaoFee.UNDEF && valorTarifa != null) {
                BigDecimal valorFinal = valorTarifa.getTarifaComMarkup().getValorFinal();
                if (TipoFee.TAXAS.equals(configuracaoFee.getTipoFee())) {
                    valorFinal = valorTarifa.getTarifa().getValorNeto().add(valorTarifa.getTotalTaxas());
                }
                final BigDecimal perc = CollectionUtils.isNotEmpty(configuracaoFee.getCiasAereas()) && configuracaoFee.getCiasAereas().get(0).getPercentualFee().compareTo(BigDecimal.ZERO) > 0 ?
                        configuracaoFee.getCiasAereas().get(0).getPercentualFee() :
                        configuracaoFee.getPercentualFee();
                final BigDecimal valorFee = CalculadorUtil.calculaValorFator(valorFinal, perc);
                BigDecimal valorMinimoFee = CollectionUtils.isNotEmpty(configuracaoFee.getCiasAereas()) ? configuracaoFee.getCiasAereas().get(0).getValorMinimoFee() : configuracaoFee.getValorMinimo();

                return valorFee.max(calculaValorMinimo(valorMinimoFee, CollectionUtils.isNotEmpty(configuracaoFee.getCiasAereas()) ? configuracaoFee.getCiasAereas().get(0).getMoeda() : Moeda.BRL));
            }
            return BigDecimal.ZERO;
        }

        private BigDecimal calculaValorMinimo(BigDecimal valorMinimo, Moeda moedaConfig) {
            if (!Moeda.BRL.equals(moedaConfig)) {
                final Cambio cambio = cambioService.buscaUltimoCambio(LocalDate.now());
                if (cambio != null && moedaConfig.equals(cambio.getMoeda())) {
                    valorMinimo = valorMinimo.multiply(cambio.getValor());
                }
            }

            return valorMinimo;
        }

        /**
         * Aplica os valores de rav na tarifa
         *
         * @param configuracaoRav a configuração de rav a ser aplicada na tarifa
         * @param valorTarifa     o valor tarifa a ser aplicado
         */
        @Trace
        private BigDecimal getValorRav(ConfiguracaoRav configuracaoRav, ValorTarifa valorTarifa) {
            if (configuracaoRav != null && configuracaoRav != ConfiguracaoRav.UNDEF && valorTarifa != null) {
                final BigDecimal valorFinal = valorTarifa.getTarifaComMarkup().getValorFinal();
                final BigDecimal percRav = CollectionUtils.isNotEmpty(configuracaoRav.getCiasAereas()) && configuracaoRav.getCiasAereas().get(0).getPercentualRav().compareTo(BigDecimal.ZERO) > 0 ?
                        configuracaoRav.getCiasAereas().get(0).getPercentualRav() :
                        configuracaoRav.getPercentualRav();
                final BigDecimal valorRav = CalculadorUtil.calculaValorFator(valorFinal, percRav);
                BigDecimal valorMinimoRav = CollectionUtils.isNotEmpty(configuracaoRav.getCiasAereas()) ? configuracaoRav.getCiasAereas().get(0).getValorMinimoRav() : configuracaoRav.getValorMinimo();

                if (valorMinimoRav == null)
                    valorMinimoRav = new BigDecimal("0");

                return valorRav.max(calculaValorMinimo(valorMinimoRav, CollectionUtils.isNotEmpty(configuracaoRav.getCiasAereas()) ? configuracaoRav.getCiasAereas().get(0).getMoeda() : Moeda.BRL));
            }
            return BigDecimal.ZERO;
        }

        /**
         * Aplica os valores de fee na tarifa
         *
         * @param valorFee    o valor fee a ser aplicada na tarifa
         * @param valorTarifa o valor tarifa a ser aplicado
         * @param tipoMarkup  tipo do markup usado também no calculo de Fee (Avantrip)
         */
        @Trace
        private BigDecimal getValorFee(BigDecimal valorFee, ValorTarifa valorTarifa, TipoMarkup tipoMarkup) {
            if (valorFee != null && valorFee.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal valorFinal = valorTarifa.getTarifaComMarkup().getValorFinal();
                if (TipoMarkup.TOTAL.equals(tipoMarkup)) {
                    valorFinal = valorTarifa.getTarifa().getValorNeto().add(valorTarifa.getTotalTaxas());
                }
                return CalculadorUtil.calculaValorFator(valorFinal, valorFee);
            }
            return BigDecimal.ZERO;
        }

        /**
         * Aplica os valores de taxa bolsa (%) na tarifa
         *
         * @param fatorTaxaBolsa o valor de taxa bolsa a ser aplicada na tarifa
         * @param valorTarifa    o valor tarifa a ser aplicado
         */
        @Trace
        private BigDecimal getValorTaxaBolsa(BigDecimal fatorTaxaBolsa, ValorTarifa valorTarifa) {
            if (fatorTaxaBolsa != null && fatorTaxaBolsa.compareTo(BigDecimal.ZERO) > 0) {
                final BigDecimal valorFinal = valorTarifa.getTarifaComMarkup().getValorFinal();
                return CalculadorUtil.calculaValorFator(valorFinal, fatorTaxaBolsa);
            }
            return BigDecimal.ZERO;
        }

        /**
         * Metodo que obtem o Merchant a partir da seguinte regra
         * Possui Markup, Merchant = OWN
         * Possui Possui alguma sobretaxa que seja diferente do markup, Merchant = BOTH
         * Qualquer outra condição, Merchant = CIA
         *
         * @param valoresCalculados Contem os valores de sobretaxa entre outros
         **/
        protected Merchant obtemMerchant(Map<String, ValorTarifa> valoresCalculados) {
            BigDecimal valorMkp = valoresCalculados.entrySet().stream()
                    .map(Map.Entry::getValue).filter(Objects::nonNull)
                    .map(valorTarifa -> valorTarifa.getTaxaSobretaxaDetalheTarifaIndividual(MARKUP))
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_EVEN);

            boolean hasFare = true;

            if (valoresCalculados.size() > NumberUtils.INTEGER_ZERO && valoresCalculados.values().stream().map(ValorTarifa::getTarifa).allMatch(Objects::nonNull)) {
                hasFare = valoresCalculados.values().stream().map(ValorTarifa::getTarifa).map(Valor::getValorNeto).anyMatch(netAmout -> netAmout.compareTo(BigDecimal.ZERO) > NumberUtils.INTEGER_ZERO);
            }

            if (valorMkp.compareTo(BigDecimal.ZERO) != NumberUtils.INTEGER_ZERO || !hasFare) {
                return Merchant.OWN;
            }

            BigDecimal totalSobreTaxa = valoresCalculados.entrySet().stream()
                    .map(Map.Entry::getValue).map(ValorTarifa::getTotalSobretaxas).reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_EVEN);

            return totalSobreTaxa.compareTo(BigDecimal.ZERO) > NumberUtils.INTEGER_ZERO ? Merchant.BOTH : Merchant.CIA;
        }


        private BigDecimal getValorEmFatorDivisao(BigDecimal valor) {
            return BigDecimal.ONE.divide(valor.add(BigDecimal.ONE), 4, BigDecimal.ROUND_HALF_EVEN);
        }

        private BigDecimal getPercentageFee(ConfiguracaoFee configuracaoFee, BigDecimal valorFee, ValorTarifa tarifaFinal) {
            if (configuracaoFee != null && configuracaoFee != ConfiguracaoFee.UNDEF) {
                BigDecimal percentFee;
                final BigDecimal valorMinimo = CollectionUtils.isNotEmpty(configuracaoFee.getCiasAereas()) && configuracaoFee.getCiasAereas().get(0).getValorMinimoFee().compareTo(BigDecimal.ZERO) > 0 ?
                        configuracaoFee.getCiasAereas().get(0).getValorMinimoFee() :
                        configuracaoFee.getValorMinimo();

                if (valorMinimo != null && valorMinimo.compareTo(BigDecimal.ZERO) > 0 && valorMinimo.compareTo(valorFee) == 0) {
                    BigDecimal valorFinal = tarifaFinal.getTarifaComMarkup().getValorFinal();
                    if (TipoFee.TAXAS.equals(configuracaoFee.getTipoFee())) {
                        valorFinal = tarifaFinal.getTarifa().getValorNeto().add(tarifaFinal.getTotalTaxas());
                    }
                    percentFee = CalculadorUtil.calculaPercentage(valorFinal, valorMinimo);
                } else {
                    percentFee = CollectionUtils.isNotEmpty(configuracaoFee.getCiasAereas()) && configuracaoFee.getCiasAereas().get(0).getPercentualFee().compareTo(BigDecimal.ZERO) > 0 ?
                            configuracaoFee.getCiasAereas().get(0).getPercentualFee() :
                            configuracaoFee.getPercentualFee();
                }
                return percentFee;
            } else {
                return BigDecimal.ZERO;
            }
        }

        private BigDecimal getPercentageFee(String produto, List<ConfiguracaoPrecificacao> configsPrecificacao) {
            if (configsPrecificacao != null && !configsPrecificacao.isEmpty()) {
                int count = 0;
                BigDecimal percentageMedia = BigDecimal.ZERO;
                for (ConfiguracaoPrecificacao conf : configsPrecificacao) {

                    if (conf != null) {
                        BigDecimal fatorFee = conf.getFatoresMap(produto).get(TipoValor.FEE);
                        if (fatorFee.compareTo(BigDecimal.ZERO) > 0) {
                            percentageMedia = percentageMedia.add(fatorFee);
                            count++;
                        }
                    }
                    if (count > 0)
                        percentageMedia = percentageMedia.divide(new BigDecimal(count));

                    return percentageMedia;
                }

            }
            return BigDecimal.ZERO;
        }

        private BigDecimal getPercentageRav(ConfiguracaoRav configuracaoRav, BigDecimal valorRavDu, ValorTarifa tarifaFinal) {
            if (configuracaoRav != null && configuracaoRav != ConfiguracaoRav.UNDEF) {
                BigDecimal percRav;

                final BigDecimal valorMinimo = CollectionUtils.isNotEmpty(configuracaoRav.getCiasAereas()) && configuracaoRav.getCiasAereas().get(0).getPercentualRav().compareTo(BigDecimal.ZERO) > 0 ?
                        configuracaoRav.getCiasAereas().get(0).getValorMinimoRav() :
                        configuracaoRav.getValorMinimo();

                if (valorMinimo != null && valorMinimo.compareTo(BigDecimal.ZERO) > 0 && valorMinimo.compareTo(valorRavDu) == 0) {
                    final BigDecimal valorFinal = tarifaFinal.getTarifaComMarkup().getValorFinal();
                    percRav = CalculadorUtil.calculaPercentage(valorFinal, valorMinimo);

                } else {
                    percRav = CollectionUtils.isNotEmpty(configuracaoRav.getCiasAereas()) && configuracaoRav.getCiasAereas().get(0).getPercentualRav().compareTo(BigDecimal.ZERO) > 0 ?
                            configuracaoRav.getCiasAereas().get(0).getPercentualRav() :
                            configuracaoRav.getPercentualRav();
                }

                return percRav;
            }
            return BigDecimal.ZERO;
        }

        private BigDecimal getPercentTaxaBolsaRC(BigDecimal valorTaxaBolsaRC, ValorTarifa tarifaFinal) {
            final BigDecimal valorFinal = tarifaFinal.getTarifaComMarkup().getValorFinal();
            return CalculadorUtil.calculaPercentage(valorFinal, valorTaxaBolsaRC);
        }

        /**
         * Obtem o resultado de precificação e os motivos de exclusão, apenas para o precificavel que está sendo validado no front
         * Metodo utilizado apenas pelo precificador-front-api, na intenção de ajudar os clientes a encontrarem problemas de precificação
         */
        @CriticalPerformance
        public Pair<ResultadoPrecificacaoView, List<MotivoExclusaoVoo>> precificaComConfiguracaoEspecifica(Precificavel precificavel, ParametroSessaoPrecificacao sessaoPrecificacao) {
            try {
                sessaoPrecificacao.setCambio(cambioService.buscaUltimoCambio(LocalDate.now()).clone());
            } catch (Exception ex) {
                LOG.error(ex.getMessage());
            }

            // RAV
            List<ConfiguracaoRav> configsRav;
            if (precificavel.getConfiguracaoRavId() != null && precificavel.getConfiguracaoRavId() > 0) {
                configsRav = Collections.singletonList(this.configuracaoRavService.findEagerByKey(precificavel.getConfiguracaoRavId()));
            } else {
                configsRav = this.configuracaoRavService.findByEmpresa(sessaoPrecificacao.getEmpresa().getId(), sessaoPrecificacao.getGrupoId(), sessaoPrecificacao.isBuscaDeTeste());
            }
            // FEE
            List<ConfiguracaoFee> configsFee;
            if (precificavel.getConfiguracaoFeeId() != null && precificavel.getConfiguracaoFeeId() > 0) {
                configsFee = Collections.singletonList(this.configuracaoFeeService.findById(precificavel.getConfiguracaoFeeId()));
            } else {
                configsFee = this.configuracaoFeeService.findByEmpresa(sessaoPrecificacao.getEmpresa().getId(), sessaoPrecificacao.isBuscaDeTeste());
            }

            // DU
            final List<ConfiguracaoDu> configsDu = this.configuracaoDuService.findAll();
            // Precificação Comercial
            final List<ConfiguracaoPrecificacaoComercial> configsPrecificacaoComercial = configuracaoPrecificacaoComercialService.findByEmpresa(sessaoPrecificacao.getEmpresa().getId(), sessaoPrecificacao.isBuscaDeTeste());
            // Parcelamento v2, mesmo que eh usado pelo serviço de installments do Atlas
            List<CondicaoPagamento> configsInstallments = null;
            if (sessaoPrecificacao.isLoadInstallments()) {
                configsInstallments = condicaoPagamentoService.findByEmpresa(sessaoPrecificacao.getEmpresa().getId(), sessaoPrecificacao.isBuscaDeTeste());
            }
            // Taxa PERSE
            List<FlagFeature> flagFeatures = flagFeatureService.findAll();
            String taxaPerseStr = flagFeatures.stream()
                    .filter(f -> f.getFlag().equalsIgnoreCase(FlagFeatureKeys.TAXA_PERSE.name()) && f.getAtivo())
                    .findFirst()
                    .map(FlagFeature::getValor)
                    .orElse("0");
            BigDecimal taxaPerse = new BigDecimal(taxaPerseStr);
            // Configurações de filtros
            List<ConfiguracaoFiltrosRecomendacao> configsFiltrosRecomendacao = configuracaoFiltroRecomendacaoService.findByEmpresa(sessaoPrecificacao.getEmpresa().getId(), sessaoPrecificacao.isBuscaDeTeste());

            // Configuração de produtos

            List<ConfiguracaoProduto> configuracaoProduto = configuracaoProdutoService.findAll();
            Optional<ResultadoPrecificacao> resultadoPrecificacaoOpt = null;
            try {
                resultadoPrecificacaoOpt = this.doPrecificacao(precificavel, sessaoPrecificacao, configsRav, configsFee, configsDu, configsPrecificacaoComercial, configsInstallments, configsFiltrosRecomendacao, configuracaoProduto, taxaPerse);
            } catch (Throwable e) {
                // tratativa apenas para conseguir logar os erros no kibana
                resultadoPrecificacaoOpt = Optional.empty();
            }
            List<ConfiguracaoPrecificacao> configPrecLoaded = resultadoPrecificacaoOpt.map(rPrecificacao -> Collections.singletonList(configPrecificacaoService.findEagerByKey(rPrecificacao.getIdConfigRestrita()))).orElse(null);

            List<Restricao> restricoes = restricaoService.getRestricoesByPrecificavel(precificavel);
            List<ConfiguracaoPrecificacaoComMotivoExclusao> configuracoesEMotivos = this.buscaConfiguracoesPrecificacaoPorRestricoesEMotivosDaExclusao(restricoes, precificavel, sessaoPrecificacao);

            boolean isPrecificavelExcluded = !resultadoPrecificacaoOpt.isPresent();

            ResultadoDebug resultadoDebug = new ResultadoDebug();
            resultadoDebug.setExcluido(isPrecificavelExcluded);
            resultadoDebug.setRestricoesGeradas(restricoes);
            resultadoDebug.setConfiguracoesEMotivos(configuracoesEMotivos);
            resultadoDebug.setConfiguracoesPrecificacao(configPrecLoaded);
            resultadoDebug.setConfiguracaoFee(resultadoPrecificacaoOpt.map(ResultadoPrecificacao::getConfiguracaoFeeId).map(configuracaoFeeService::findById).orElse(null));
            resultadoDebug.setConfiguracoesFeeEMotivos(this.configuracaoFeeService.obterConfiguracoesFeePorPrecificavel(restricoes, precificavel, sessaoPrecificacao));
            resultadoDebug.setConfiguracaoRav(resultadoPrecificacaoOpt.map(ResultadoPrecificacao::getConfiguracaoRavId).map(configuracaoRavService::findEagerByKey).orElse(null));
            String condicaoPrecificacao = null;
            if (CollectionUtils.isNotEmpty(configPrecLoaded)) {
                ConfiguracaoPrecificacao configuracaoPrecificacao = configPrecificacaoService.findEagerByKey(configPrecLoaded.get(0).getId());
                if (configuracaoPrecificacao != null && StringUtils.isNotEmpty(configuracaoPrecificacao.getCondicaoPrecificacao())) {
                    condicaoPrecificacao = configuracaoPrecificacao.getCondicaoPrecificacao();
                }
            }
            resultadoDebug.setConfiguracaoPrecificacaoComercial(this.buscaConfiguracaoPrecificacaoComercial(restricoes, precificavel, sessaoPrecificacao, condicaoPrecificacao).orElse(null));

            ResultadoPrecificacao resultadoPrecificacao = resultadoPrecificacaoOpt.orElseGet(ResultadoPrecificacao::new);
            ResultadoPrecificacaoView resultView = new ResultadoPrecificacaoView();
            resultView.setResultadoDebug(resultadoDebug);
            try {
                BeanUtils.copyProperties(resultView, resultadoPrecificacao);
            } catch (IllegalAccessException | InvocationTargetException e) {

            }
            List<MotivoExclusaoVoo> motivosExclusaoVoos = isPrecificavelExcluded ?
                    configPrecificacaoService.obterMotivosExclusaoVoo(restricoes, precificavel, sessaoPrecificacao) :
                    Collections.emptyList();

            return Pair.of(resultView, motivosExclusaoVoos);
        }
    }



}
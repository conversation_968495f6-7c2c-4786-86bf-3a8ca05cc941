package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoRcAgenciaRecord;
import br.tur.reservafacil.dominio.Agencia;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcAgencia;
import br.tur.reservafacil.precificador.domain.repository.ConfiguracaoRcAgenciaRepository;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.AgenciaRecord2AgenciaMapper;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.Tables.CONFIGURACAO_RC_AGENCIA;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.Agencia.AGENCIA;

/**
 * Created by <PERSON><PERSON> on 20/08/25 - GA-12251.
 */
@Repository
public class ConfiguracaoRcAgenciaJooqDao
		extends AbstractJooqDao<ConfiguracaoRcAgenciaRecord, ConfiguracaoRcAgencia, Integer>
		implements ConfiguracaoRcAgenciaRepository {

    @Autowired
    private AgenciaRecord2AgenciaMapper agenciaMapper;

    @Autowired
    public ConfiguracaoRcAgenciaJooqDao(Configuration configuration, DSLContext context) {
	super(CONFIGURACAO_RC_AGENCIA, ConfiguracaoRcAgencia.class, configuration, context);
    }

    @Override
    protected Integer getId(ConfiguracaoRcAgencia configuracaoRcAgencia) {
	return configuracaoRcAgencia.getId();
    }

    @Override
    public void deleteByConfigPrecificacaoId(Integer idConfigPrecificacao) {
	this.getContext().deleteFrom(this.getTable()).where(CONFIGURACAO_RC_AGENCIA.CONFIGURACAO_RC_ID.eq(idConfigPrecificacao)).execute();
    }

    @Override
    public List<Agencia> findAgenciasByConfiguracaoId(Integer configuracaoId) {
        return this.getContext().select(AGENCIA.fields())
                .from(AGENCIA)
                .join(CONFIGURACAO_RC_AGENCIA)
                .on(AGENCIA.ID.eq(CONFIGURACAO_RC_AGENCIA.AGENCIA_ID))
                .where(CONFIGURACAO_RC_AGENCIA.CONFIGURACAO_RC_ID.eq(configuracaoId))
                .fetch().map(record -> this.agenciaMapper.map(record.into(AGENCIA)));
    }
}

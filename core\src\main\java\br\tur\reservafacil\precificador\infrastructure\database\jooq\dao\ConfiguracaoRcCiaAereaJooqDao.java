package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoRcCiaAereaRecord;
import br.tur.reservafacil.dominio.aereo.CiaAerea;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcCiaAerea;
import br.tur.reservafacil.precificador.domain.repository.ConfiguracaoRcCiaAereaRepository;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.CiaAereaRecord2CiaAereaMapper;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.CiaAerea.CIA_AEREA;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.ConfiguracaoRcCiaAerea.CONFIGURACAO_RC_CIA_AEREA;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
@Repository
public class ConfiguracaoRcCiaAereaJooqDao
		extends AbstractJooqDao<ConfiguracaoRcCiaAereaRecord, ConfiguracaoRcCiaAerea, Integer>
		implements ConfiguracaoRcCiaAereaRepository {

    @Autowired
    private CiaAereaRecord2CiaAereaMapper ciaAereaMapper;

    @Autowired
    public ConfiguracaoRcCiaAereaJooqDao(Configuration configuration, DSLContext context) {
	super(CONFIGURACAO_RC_CIA_AEREA, ConfiguracaoRcCiaAerea.class, configuration, context);
    }

    @Override
    protected Integer getId(ConfiguracaoRcCiaAerea configuracaoPrecificacaoCiaAerea) {
	return configuracaoPrecificacaoCiaAerea.getId();
    }

    @Override
    public void deleteByConfigPrecificacaoId(Integer idConfigPrecificacao) {
	this.getContext().deleteFrom(this.getTable()).where(CONFIGURACAO_RC_CIA_AEREA.CONFIGURACAO_RC_ID.eq(idConfigPrecificacao)).execute();
    }

    @Override
    public List<CiaAerea> findCiasAereasByConfiguracaoId(Integer configuracaoId) {
        return this.getContext().select(CIA_AEREA.fields())
                .from(CIA_AEREA)
                .join(CONFIGURACAO_RC_CIA_AEREA)
                .on(CIA_AEREA.ID.eq(CONFIGURACAO_RC_CIA_AEREA.CIA_AEREA_ID))
                .where(CONFIGURACAO_RC_CIA_AEREA.CONFIGURACAO_RC_ID.eq(configuracaoId))
                .fetch().map(record -> this.ciaAereaMapper.map(record.into(CIA_AEREA)));
    }


}

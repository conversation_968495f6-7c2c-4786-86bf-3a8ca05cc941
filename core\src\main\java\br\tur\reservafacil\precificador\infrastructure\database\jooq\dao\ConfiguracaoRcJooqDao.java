package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoRcRecord;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRc;
import br.tur.reservafacil.precificador.domain.repository.ConfiguracaoRcRepository;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.configuracaoRc.restricao.ConfiguracaoRcComRestricaoMapper;
import com.newrelic.api.agent.Trace;
import org.jooq.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;

import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.Tables.*;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.CiaAerea.CIA_AEREA;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.ConfiguracaoRc.CONFIGURACAO_RC;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.ConfiguracaoRcCiaAerea.CONFIGURACAO_RC_CIA_AEREA;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.ConfiguracaoRcEmpresa.CONFIGURACAO_RC_EMPRESA;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.ConfiguracaoRcProduto.CONFIGURACAO_RC_PRODUTO;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.ConfiguracaoRcRestricao.CONFIGURACAO_RC_RESTRICAO;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.Produto.PRODUTO;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.Restricao.RESTRICAO;
import static br.tur.reservafacil.dominio.tipo.StatusPublicacao.NAO_PUBLICADA;
import static br.tur.reservafacil.dominio.tipo.StatusPublicacao.PUBLICADA;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
@Repository
public class ConfiguracaoRcJooqDao extends AbstractJooqDao<ConfiguracaoRcRecord, ConfiguracaoRc, Integer> implements ConfiguracaoRcRepository {

    private static final String PARAM_CIA = "ciaAerea";
    private static final String PARAM_AGENCIA = "agencias";
    private static final String PARAM_PRODUTO = "produto";
    private static final String PARAM_STATUS = "status";
    private static final String PARAM_EMPRESA = "empresa";
    private static final String PARAM_EDITAVEL = "editavel";
    private static final String PARAM_NOME = "nome";
    private static final String PARAM_RENTABILIDADE_MAX = "rentabilidadeMax";
    private static final String PARAM_TIPO_CONFIG = "tipoConfigPrecificacao";
    private static final String PARAM_ATIVO = "ativo";

    private static final String DEFAULT_FIELD_ORDER_BY = "prioridade";

    @Autowired
    private ConfiguracaoRcComRestricaoMapper eagerMapper;

    @Autowired
    public ConfiguracaoRcJooqDao(Configuration configuration, DSLContext context) {
        super(CONFIGURACAO_RC, ConfiguracaoRc.class, configuration, context);
    }

    @Override
    protected Integer getId(ConfiguracaoRc configuracao) {
        return configuracao.getId();
    }

    @Override
    @Trace
    public ConfiguracaoRc findEaggerByKey(Integer idConfiguracaoPrecificacao) {
        List<ConfiguracaoRc> result = this.findEagerByKeys(Collections.singletonList(idConfiguracaoPrecificacao));
        return result.isEmpty() ? null : result.get(0);
    }

    @Override
    public List<ConfiguracaoRc> findByEmpresa(Integer empresaId, boolean isBuscaTeste) {

        SelectConditionStep selectConditionStep = this.getContext().selectDistinct()
                .from(CONFIGURACAO_RC)
                .innerJoin(CONFIGURACAO_RC_EMPRESA).on(CONFIGURACAO_RC.ID.eq(CONFIGURACAO_RC_EMPRESA.CONFIGURACAO_RC_ID))
                .innerJoin(EMPRESA).on(CONFIGURACAO_RC_EMPRESA.EMPRESA_ID.eq(EMPRESA.ID))
                .innerJoin(CONFIGURACAO_RC_PRODUTO).on(CONFIGURACAO_RC.ID.eq(CONFIGURACAO_RC_PRODUTO.CONFIGURACAO_RC_ID))
                .innerJoin(PRODUTO).on(CONFIGURACAO_RC_PRODUTO.PRODUTO_ID.eq(PRODUTO.ID))
                .innerJoin(CONFIGURACAO_RC_AGENCIA).on(CONFIGURACAO_RC.ID.eq(CONFIGURACAO_RC_AGENCIA.CONFIGURACAO_RC_ID))
                .innerJoin(AGENCIA).on(CONFIGURACAO_RC_AGENCIA.AGENCIA_ID.eq(AGENCIA.ID))
                .leftJoin(CONFIGURACAO_RC_RESTRICAO).on(CONFIGURACAO_RC.ID.eq(CONFIGURACAO_RC_RESTRICAO.CONFIGURACAO_RC_ID))
                .leftJoin(RESTRICAO).on(CONFIGURACAO_RC_RESTRICAO.RESTRICAO_ID.eq(RESTRICAO.ID))
                .leftJoin(CONFIGURACAO_RC_CIA_AEREA).on(CONFIGURACAO_RC.ID.eq(CONFIGURACAO_RC_CIA_AEREA.CONFIGURACAO_RC_ID))
                .leftJoin(CIA_AEREA).on(CONFIGURACAO_RC_CIA_AEREA.CIA_AEREA_ID.eq(CIA_AEREA.ID))
                .where(CONFIGURACAO_RC.ATIVO.isTrue())
                .and(CONFIGURACAO_RC_EMPRESA.EMPRESA_ID.eq(empresaId));

        if (isBuscaTeste) {
            selectConditionStep.and(CONFIGURACAO_RC.EDITAVEL.isTrue());
            selectConditionStep.and(CONFIGURACAO_RC.STATUS_PUBLICACAO.in(PUBLICADA.name(), NAO_PUBLICADA.name()));
        } else {
            selectConditionStep.and(CONFIGURACAO_RC.STATUS_PUBLICACAO.eq(PUBLICADA.name()));
        }

        return this.eagerMapper.map(selectConditionStep.orderBy(CONFIGURACAO_RC.PRIORIDADE).fetch());
    }

    @Override
    public List<ConfiguracaoRc> findEagerByKeys(List<Integer> idsConfiguracoesPrecificacao) {
        List<SelectField<?>> fields = new ArrayList<>();
        fields.addAll(Arrays.asList(CONFIGURACAO_RC.fields()));
        fields.addAll(Arrays.asList(RESTRICAO.fields()));

        SelectConditionStep selectConditionStep = this.getContext().select()
                .from(CONFIGURACAO_RC)
                .innerJoin(CONFIGURACAO_RC_EMPRESA).on(CONFIGURACAO_RC.ID.eq(CONFIGURACAO_RC_EMPRESA.CONFIGURACAO_RC_ID))
                .innerJoin(EMPRESA).on(CONFIGURACAO_RC_EMPRESA.EMPRESA_ID.eq(EMPRESA.ID))
                .innerJoin(CONFIGURACAO_RC_PRODUTO).on(CONFIGURACAO_RC.ID.eq(CONFIGURACAO_RC_PRODUTO.CONFIGURACAO_RC_ID))
                .innerJoin(PRODUTO).on(CONFIGURACAO_RC_PRODUTO.PRODUTO_ID.eq(PRODUTO.ID))
                .innerJoin(CONFIGURACAO_RC_AGENCIA).on(CONFIGURACAO_RC.ID.eq(CONFIGURACAO_RC_AGENCIA.CONFIGURACAO_RC_ID))
                .leftJoin(CONFIGURACAO_RC_RESTRICAO).on(CONFIGURACAO_RC.ID.eq(CONFIGURACAO_RC_RESTRICAO.CONFIGURACAO_RC_ID))
                .leftJoin(RESTRICAO).on(CONFIGURACAO_RC_RESTRICAO.RESTRICAO_ID.eq(RESTRICAO.ID))
                .leftJoin(CONFIGURACAO_RC_CIA_AEREA).on(CONFIGURACAO_RC.ID.eq(CONFIGURACAO_RC_CIA_AEREA.CONFIGURACAO_RC_ID))
                .leftJoin(CIA_AEREA).on(CONFIGURACAO_RC_CIA_AEREA.CIA_AEREA_ID.eq(CIA_AEREA.ID))
                .where(CONFIGURACAO_RC.ID.in(idsConfiguracoesPrecificacao));

        return eagerMapper.map(selectConditionStep.fetch());
    }

    @Override
    public List<ConfiguracaoRc> findByKeys(Set<Integer> ids) {
        return this.getContext().select()
                .from(CONFIGURACAO_RC)
                .where(CONFIGURACAO_RC.ID.in(ids)).fetch().into(ConfiguracaoRc.class);
    }

    @Override public ConfiguracaoRc ultimoPublicadoPorIdOrigem(Integer idOrigem) {
        return this.mapper().map(this.getContext()
                .selectFrom(CONFIGURACAO_RC)
                .where(CONFIGURACAO_RC.STATUS_PUBLICACAO.eq(PUBLICADA.name()))
                .and(CONFIGURACAO_RC.ID_ORIGEM.eq(idOrigem).or(CONFIGURACAO_RC.ID.eq(idOrigem))).fetchOne());
    }

    @Override public Integer ultimoEditavelPorIdOrigem(Integer idOrigem) {
        Record1<Integer> idOrigemRecord = this.getContext().select(CONFIGURACAO_RC.ID)
                .from(CONFIGURACAO_RC)
                .where(CONFIGURACAO_RC.EDITAVEL.isTrue())
                .and(CONFIGURACAO_RC.ATIVO.isTrue())
                .and(CONFIGURACAO_RC.ID_ORIGEM.eq(idOrigem))
                .fetchOne();
        return idOrigemRecord != null ? idOrigemRecord.value1() : null;
    }


}

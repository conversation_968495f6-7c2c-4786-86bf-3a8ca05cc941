package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoBuscaRecord;
import br.tur.reservafacil.dominio.Empresa;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoBusca;
import br.tur.reservafacil.dominio.tipo.NacInt;
import br.tur.reservafacil.dominio.tipo.StatusPublicacao;
import br.tur.reservafacil.precificador.application.service.EmpresaService;
import org.apache.commons.collections4.CollectionUtils;
import org.jooq.RecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by da<PERSON><PERSON> on 26/04/16.
 */
@Component
public class ConfiguracaoBuscaRecord2ConfiguracaoBuscaMapper implements RecordMapper<ConfiguracaoBuscaRecord, ConfiguracaoBusca> {

    private EmpresaService empresaService;

	public ConfiguracaoBuscaRecord2ConfiguracaoBuscaMapper(EmpresaService empresaService) {
		this.empresaService = empresaService;
	}

    @Override
    public ConfiguracaoBusca map(ConfiguracaoBuscaRecord record) {
        if (record == null) return null;

	ConfiguracaoBusca configuracaoBusca = new ConfiguracaoBusca();
	configuracaoBusca.setId(record.getId());
        configuracaoBusca.setIdOrigem(record.getIdOrigem());
	configuracaoBusca.setPrioridade(record.getPrioridade());
	configuracaoBusca.setEmpresa(this.buscaEmpresaPorId(record.getEmpresaId()));
	configuracaoBusca.setNome(record.getNome());
	configuracaoBusca.setNacInt(NacInt.getByKey(record.getNacInt()));
	configuracaoBusca.setStatusPublicacao(StatusPublicacao.valueOf(record.getStatusPublicacao()));
	configuracaoBusca.setEditavel(record.getEditavel());
	configuracaoBusca.setVersao(record.getVersao());
	configuracaoBusca.setAgrupaChamadaUnica(record.getAgrupaChamadaUnica());
	configuracaoBusca.setAtivo(record.getAtivo());
        configuracaoBusca.setPeso(record.getPeso());
	configuracaoBusca.setBestPrice(record.getBestPrice());

	return configuracaoBusca;
    }

    public ConfiguracaoBusca map(ConfiguracaoBuscaRecord record, List<Empresa> empresas) {
	if (record == null) return null;

	ConfiguracaoBusca configuracaoBusca = new ConfiguracaoBusca();
	configuracaoBusca.setId(record.getId());
	configuracaoBusca.setIdOrigem(record.getIdOrigem());
	configuracaoBusca.setPrioridade(record.getPrioridade());
	configuracaoBusca.setNome(record.getNome());
	configuracaoBusca.setNacInt(NacInt.getByKey(record.getNacInt()));
	configuracaoBusca.setStatusPublicacao(StatusPublicacao.valueOf(record.getStatusPublicacao()));
	configuracaoBusca.setEditavel(record.getEditavel());
	configuracaoBusca.setVersao(record.getVersao());
	configuracaoBusca.setAgrupaChamadaUnica(record.getAgrupaChamadaUnica());
	configuracaoBusca.setAtivo(record.getAtivo());
	configuracaoBusca.setPeso(record.getPeso());
	configuracaoBusca.setBestPrice(record.getBestPrice());
	if (CollectionUtils.isNotEmpty(empresas)) {
	    configuracaoBusca.setEmpresa(empresas.stream().filter(empresa -> empresa.getId() == record.getEmpresaId())
							 .findFirst().orElse(this.buscaEmpresaPorId(record.getEmpresaId())));
	} else {
	    configuracaoBusca.setEmpresa(this.buscaEmpresaPorId(record.getEmpresaId()));
	}
	return configuracaoBusca;
    }

    private Empresa buscaEmpresaPorId(Integer empresaId) {
	if(empresaId != null) {
	    return empresaService.findByKey(empresaId);
	}
	return null;
    }
}

package br.tur.reservafacil.precificador.application.service.cache;

import br.tur.reservafacil.dominio.Empresa;
import br.tur.reservafacil.dominio.aereo.AcordoComercial;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoBusca;
import br.tur.reservafacil.dominio.tipo.SistEmis;
import br.tur.reservafacil.precificador.application.service.AcordoComercialService;
import br.tur.reservafacil.precificador.domain.repository.ConfiguracaoBuscaRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import javax.persistence.EntityNotFoundException;
import java.util.Collections;
import java.util.List;

/**
 * Created by fepelichero on 08/11/2018.
 */
@Component
public class ConfiguracaoBuscaRedisCacheComponent {

    @Autowired
    private ConfiguracaoBuscaRepository repository;

    @Autowired
    private AcordoComercialService acordoComercialService;

    @Cacheable(cacheNames = "ConfiguracaoBuscaService::findAcordosByConfiguracaoId", cacheManager = "redisCacheManager")
    public List<AcordoComercial> findAcordosByConfiguracaoId(Integer idConfiguracao, List<String> codigoCias, List<SistEmis> sistemasEmissores) {
        return acordoComercialService.findAcordosVigentesByConfiguracaoId(idConfiguracao, codigoCias, sistemasEmissores);
    }

    @Cacheable(cacheNames = "ConfiguracaoBuscaService::findConfiguracaoBuscaPorFilialEEmpresaESistEmisProduto", unless = "#result == null", cacheManager = "redisCacheManager")
    public List<ConfiguracaoBusca> findConfiguracaoBuscaPorFilialEEmpresaESistEmisProduto(String filial, String agencia, String grupo, String empresa, SistEmis sistEmis, String produto) {
        return repository.findConfiguracaoBuscaPorFilialEEmpresaESistEmisProduto(filial, agencia, grupo, empresa, sistEmis, produto);
    }

    @Cacheable(cacheNames = "ConfiguracaoBuscaService::configuracoesSoComRestricaoByEmpresaSisteEmisProduto", unless = "#result == null", cacheManager = "redisCacheManager")
    public List<ConfiguracaoBusca> configuracoesSoComRestricaoByEmpresaSisteEmisProduto(String filial, Empresa empresa,
                                                                                    List<SistEmis> sistEmis, String produto, String unidadeOperacional,
                                                                                    Boolean isBuscaDeTeste, String timeToCache) {
        return repository.findConfiguracaoSoComRestricaoByEmpresaCiaSisteEmisProduto(filial, empresa, Collections.emptyList(), sistEmis, produto, unidadeOperacional,
                isBuscaDeTeste);
    }

    @Cacheable(cacheNames = "ConfiguracaoBuscaService::isBestPriceEnabled", cacheManager = "redisCacheManager")
    public boolean isBestPriceEnabled(Integer id) {
	ConfiguracaoBusca config = repository.findByPkEager(id);

	if (config == null) {
	    throw new EntityNotFoundException("ConfiguracaoBusca com id " + id + " não encontrada.");
	}

	return Boolean.TRUE.equals(config.getBestPrice());
    }
}

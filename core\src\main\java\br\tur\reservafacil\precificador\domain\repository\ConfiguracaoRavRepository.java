package br.tur.reservafacil.precificador.domain.repository;

import br.tur.reservafacil.dominio.aereo.ConfiguracaoRav;
import br.tur.reservafacil.dominio.tipo.NacInt;
import br.tur.reservafacil.dominio.tipo.TipoTarifaAcordo;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 6/30/16.
 */
public interface ConfiguracaoRavRepository extends Repository<ConfiguracaoRav, Integer> {
    ConfiguracaoRav findByEmpresaCiaProdutoTipoTarifaNacIntGrupo(Integer empresaId, String codCia, String produto, TipoTarifaAcordo tipoTarifa,
                                                                 NacInt nacInt, Integer grupoId);
    ConfiguracaoRav findEagerByKey(Integer id);
    List<ConfiguracaoRav> findByParams(int page, Integer pageSize, String orderBy, Map<String, String> params);
    Integer countByParameters(Map<String, String> params);

    List<ConfiguracaoRav> findByEmpresa(Integer empresaId, Integer grupoId, boolean isBuscaDeTeste);
}

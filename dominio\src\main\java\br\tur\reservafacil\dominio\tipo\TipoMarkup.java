package br.tur.reservafacil.dominio.tipo;

/**
 * Created by <PERSON><PERSON><PERSON> on 6/24/16.
 */
public enum TipoMarkup {

    /**
     * Calcula o Markup em cima do desconto dado pela cia aérea
     * Exemplo: Se o desconto da cia foi de 100 reais, e o markup cia for de 50%, o valor de markup será 50 reais
     */
    CIA,

    /**
     * Calcula o Markup em cima do valor neto da tarifa
     * Exemplo: Se o valor neto for de 1000 reais, e o markup cia for de 10%, o valor de markup será 100 reais
     */
    BRUTO,

    /**
     * Calcula o Markup em cima do valor neto da tarifa abatendo o percentual de comissão e centivo
     */
    LIQUIDO,

    /**
     * Calcula o Markup em cima do desconto dado pela cia aérea, porém limitando um percentual máximo de ganho
     * Exemplo 1: Se o desconto da cia foi de 100 reais (representando 10% de desconto), e o markup for 8%, o valor de markup será 80 reais
     * Exemplo 2: Se o desconto da cia foi de 100 reais (representando 10% de desconto), e o markup for 12%, o valor de markup será apenas os 100 reais, pois o desconto dado pela
     * cia é menor que o nosso limite
     */
    LIMITE,

    /**
     * Calcula o Markup em cima do valor neto + taxas
     * Exemplo: Se o valor neto for de 1000 reais e tiver 100 reais de taxa, e o markup cia for de 10%, o valor de markup será 110 reais ((1000 + 100) * 0.1)
     */
    TOTAL,

    /**
     * Calcula o Markup em cima do desconto dado pela cia aérea, análogo ao CIA, tendo como diferença é que tem prioridade sobre as outras regras
     * Exemplo: Se o desconto da cia foi de 100 reais, e o markup cia for de 50%, o valor de markup será 50 reais
     */
    INTELIGENTE;

    public boolean isLiquido() {
	return this.equals(LIQUIDO);
    }

    public boolean isBruto() {
	return this.equals(BRUTO);
    }

    public boolean isCia() {
	return this.equals(CIA);
    }

    public boolean isLimite() { return this.equals(LIMITE); }
}

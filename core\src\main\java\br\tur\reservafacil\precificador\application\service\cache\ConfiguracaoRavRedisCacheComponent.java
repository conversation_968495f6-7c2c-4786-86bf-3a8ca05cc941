package br.tur.reservafacil.precificador.application.service.cache;

import br.tur.reservafacil.dominio.aereo.ConfiguracaoRav;
import br.tur.reservafacil.dominio.tipo.NacInt;
import br.tur.reservafacil.dominio.tipo.TipoTarifaAcordo;
import br.tur.reservafacil.precificador.domain.repository.ConfiguracaoRavRepository;
import br.tur.reservafacil.precificador.domain.repository.ViewConfiguracaoRavRepository;
import com.newrelic.api.agent.Trace;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

/**
 * Created by fepelichero on 08/11/2018.
 */
@Component
public class ConfiguracaoRavRedisCacheComponent {

    @Autowired
    private ConfiguracaoRavRepository repository;

    @Autowired
    private ViewConfiguracaoRavRepository viewConfiguracaoRavRepository;

    @Cacheable(cacheNames = "ConfiguracaoRavService::findByEmpresaCiaProdutoTipoTarifa", cacheManager = "redisCacheManager")
    @Trace
    public ConfiguracaoRav findByEmpresaCiaProdutoTipoTarifaNacInt(Integer empresaId, String codCia, String produto, TipoTarifaAcordo tipoTarifaAcordo,
                                                                   NacInt nacInt, Integer grupoId) {
        return repository.findByEmpresaCiaProdutoTipoTarifaNacIntGrupo(empresaId, codCia, produto, tipoTarifaAcordo, nacInt, grupoId);
    }

    @Cacheable(cacheNames = "ConfiguracaoRavService::findViewByEmpresaCiaProdutoTipoTarifaNacIntGrupo", cacheManager = "redisCacheManager")
    @Trace
    public ConfiguracaoRav findViewByEmpresaCiaProdutoTipoTarifaNacIntGrupo(Integer empresaId, String codCia, String produto, TipoTarifaAcordo tipoTarifaAcordo,
                                                                           NacInt nacInt, Integer grupoId) {
        return viewConfiguracaoRavRepository.findByEmpresaCiaProdutoTipoTarifaNacIntGrupo(empresaId, codCia, produto, tipoTarifaAcordo, nacInt, grupoId);
    }

    @Cacheable(cacheNames = "ConfiguracaoRavService::findByEmpresa", cacheManager = "redisCacheManager")
    @Trace
    public List<ConfiguracaoRav> findByEmpresa(Integer empresaId, Integer grupoId, boolean isBuscaDeTeste) {
        return repository.findByEmpresa(empresaId, grupoId, isBuscaDeTeste);
    }
}

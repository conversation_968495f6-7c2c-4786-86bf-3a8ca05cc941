package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.configuracaoRc.produto;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoRcProdutoRecord;
import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ProdutoRecord;
import br.tur.reservafacil.dominio.Produto;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcProduto;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.CustomMultipleMapper;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.ProdutoRecord2ProdutoMapper;
import org.jooq.Record;
import org.jooq.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.Tables.CONFIGURACAO_RC_PRODUTO;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.Tables.PRODUTO;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
@Component
public class ConfiguracaoRcProdutoEagerMapper
		implements CustomMultipleMapper<List<ConfiguracaoRcProduto>, Result<Record>> {

    @Autowired
    private ConfiguracaoRcProdutoRecord2ConfiguracaoRcProdutoMapper configuracaoRcProdutoRecord2ConfiguracaoRcProdutoMapper;

    @Autowired ProdutoRecord2ProdutoMapper produtoMapper;

    @Override
    public List<ConfiguracaoRcProduto> map(Result<Record> result) {
	List<ConfiguracaoRcProduto> configuracaoRcProdutos = new ArrayList<>();
	final Map<ConfiguracaoRcProdutoRecord, Result<Record>> configuracaoRcProdutoRecordResultMap = result.intoGroups(CONFIGURACAO_RC_PRODUTO);
	for (Map.Entry<ConfiguracaoRcProdutoRecord, Result<Record>> entry : configuracaoRcProdutoRecordResultMap.entrySet()) {
	    if (entry.getKey().getId() != null) {
		final ConfiguracaoRcProduto rcProduto = configuracaoRcProdutoRecord2ConfiguracaoRcProdutoMapper.map(entry.getKey());
		rcProduto.setProduto(criaProduto(entry.getValue()));
		configuracaoRcProdutos.add(rcProduto);
	    }
	}
	return configuracaoRcProdutos;
    }

    private Produto criaProduto(Result<Record> results) {
	if (results.isNotEmpty()) {
	    final ProdutoRecord produtoRecord = results.get(0).into(PRODUTO);
	    if (produtoRecord.getId() != null) {
		return produtoMapper.map(produtoRecord);
	    }
	}
	return null;
    }
}

package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoRcRestricaoRecord;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcRestricao;
import br.tur.reservafacil.dominio.aereo.Restricao;
import br.tur.reservafacil.precificador.domain.repository.ConfiguracaoRcRestricaoRepository;
import org.jooq.Configuration;
import org.jooq.DSLContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.Tables.CONFIGURACAO_RC_RESTRICAO;

/**
 * Created by <PERSON><PERSON> on 20/08/25 - GA-12251.
 */
@Repository
public class ConfiguracaoRcRestricaoJooqDao
		extends AbstractJooqDao<ConfiguracaoRcRestricaoRecord, ConfiguracaoRcRestricao, Integer>
		implements ConfiguracaoRcRestricaoRepository {

    @Autowired
    public ConfiguracaoRcRestricaoJooqDao(Configuration configuration, DSLContext context) {
	super(CONFIGURACAO_RC_RESTRICAO, ConfiguracaoRcRestricao.class, configuration, context);
    }

    @Override
    protected Integer getId(ConfiguracaoRcRestricao configuracaoRcRestricao) {
	return configuracaoRcRestricao.getId();
    }

    @Override
    public void deleteByConfigPrecificacaoId(Integer idConfigPrecificacao) {
	this.getContext().deleteFrom(this.getTable()).where(CONFIGURACAO_RC_RESTRICAO.CONFIGURACAO_RC_ID.eq(idConfigPrecificacao)).execute();
    }

    @Override
    public void deleteByRestricaoList(List<Restricao> restricoes) {
	List<Integer> restricaoIds = restricoes.stream().map(Restricao::getId).collect(Collectors.toList());

	this.getContext().deleteFrom(CONFIGURACAO_RC_RESTRICAO)
			.where(CONFIGURACAO_RC_RESTRICAO.RESTRICAO_ID.in(restricaoIds))
			.execute();
    }
}

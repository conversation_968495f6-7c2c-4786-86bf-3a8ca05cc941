package br.tur.reservafacil.dominio.aereo;

import br.tur.reservafacil.dominio.Agencia;
import br.tur.reservafacil.dominio.Empresa;
import br.tur.reservafacil.dominio.aereo.interfaces.Identificavel;
import br.tur.reservafacil.dominio.aereo.interfaces.Restringivel;
import br.tur.reservafacil.dominio.aereo.interfaces.Versionavel;
import br.tur.reservafacil.dominio.tipo.StatusPublicacao;
import br.tur.reservafacil.dominio.tipo.TipoOperador;
import br.tur.reservafacil.dominio.tipo.TipoRestricao;
import br.tur.reservafacil.dominio.util.PairOfObjects;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import static br.tur.reservafacil.dominio.util.PairOfObjects.aPairOfObjects;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
public class ConfiguracaoRc
		implements Serializable, Identificavel<Integer>, Restringivel<Integer>, Versionavel<Integer> {

    public static final ConfiguracaoRc UNDEF = new ConfiguracaoRc();

    private static final long serialVersionUID = -2763393654567358304L;

    private Integer id;

    private String nome;

    private Integer prioridade;

    private StatusPublicacao statusPublicacao = StatusPublicacao.NAO_PUBLICADA;

    private Integer versao = 0;

    private Integer idOrigem;

    private Integer idConfigEditavel;

    private Boolean editavel;

    private Boolean ativo = Boolean.TRUE;

    private List<ConfiguracaoRcProduto> configuracaoRcProdutos = new ArrayList<>();

    private List<CiaAerea> ciasAereas = new ArrayList<>();

    private List<Empresa> empresas = new ArrayList<>();

    private List<Agencia> agencias = new ArrayList<>();

    private List<Restricao> restricoes = new ArrayList<>();

    //Este model só será utilizado para facilitar a visualização e evitar processamento no frontend
    private List<RestricaoAgrupadora> restricoesAgrupadas = new ArrayList<>();

    public Integer getId() {
	return id;
    }

    public void setId(Integer id) {
	this.id = id;
    }

    public String getNome() {
	return nome;
    }

    public void setNome(String nome) {
	this.nome = nome;
    }

    public Integer getPrioridade() {
	return prioridade;
    }

    public void setPrioridade(Integer prioridade) {
	this.prioridade = prioridade;
    }

    public List<CiaAerea> getCiasAereas() {
	if (CollectionUtils.isNotEmpty(ciasAereas)) {
	    ciasAereas.sort(Comparator.comparing(CiaAerea::getNome));
	    return ciasAereas;
	}
	return Collections.emptyList();
    }

    public void setCiasAereas(List<CiaAerea> ciasAereas) {
	this.ciasAereas = ciasAereas;
    }

    public StatusPublicacao getStatusPublicacao() {
	return statusPublicacao;
    }

    public void setStatusPublicacao(StatusPublicacao statusPublicacao) {
	this.statusPublicacao = statusPublicacao;
    }

    public void addRestricao(Restricao restricao) {
	this.restricoes.add(restricao);
    }

    public List<Restricao> getRestricoes() {
        if (restricoes == null) {
            restricoes = new ArrayList<>();
        }
	return restricoes;
    }

    public void setRestricoes(List<Restricao> restricoes) {
	this.restricoes = restricoes;
    }


    public List<ConfiguracaoRcProduto> getConfiguracaoRcProdutos() {
        return configuracaoRcProdutos;
    }

    public void setConfiguracaoRcProdutos(List<ConfiguracaoRcProduto> configuracaoRcProdutos) {
        this.configuracaoRcProdutos = configuracaoRcProdutos;
    }

    public Integer getVersao() {
	return versao;
    }

    public void setVersao(Integer versao) {
	this.versao = versao;
    }

    public void atualizaVersao() {
	this.versao++;
    }

    public Integer getIdOrigem() {
	return idOrigem != null ? idOrigem : id;
    }

    public void setIdOrigem(Integer idOrigem) {
	this.idOrigem = idOrigem;
    }

    public Boolean isEditavel() {
	return editavel;
    }

    public void setEditavel(Boolean editavel) {
	this.editavel = editavel;
    }

    public List<RestricaoAgrupadora> getRestricoesAgrupadas() {
	return restricoesAgrupadas;
    }

    public void setRestricoesAgrupadas(List<RestricaoAgrupadora> restricoesAgrupadas) {
	this.restricoesAgrupadas = restricoesAgrupadas;
	this.desagrupaRestricoes();
    }

    public List<Empresa> getEmpresas() {
	return empresas;
    }

    public void setEmpresas(List<Empresa> empresas) {
	this.empresas = empresas;
    }

    public boolean isConfiguracaoOrigem() {
        if(this.getIdOrigem() != null) {
            return this.getIdOrigem().equals(id);
        }
        return true;
    }

    public void agrupaRestricoes() {
	if (this.restricoes != null && !restricoes.isEmpty()) {
	    this.restricoesAgrupadas = this.geraListaDeRestricaoAgrupadora();
	}
    }

    public Boolean getAtivo() {
	return ativo;
    }

    public void setAtivo(Boolean ativo) {
	this.ativo = ativo;
    }

    public void setIdConfigEditavel(Integer idConfigEditavel) {
	this.idConfigEditavel = idConfigEditavel;
    }

    public Integer getIdConfigEditavel() {
	return this.idConfigEditavel;
    }

    public List<Agencia> getAgencias() {
	return agencias;
    }

    public void setAgencias(List<Agencia> agencias) {
	this.agencias = agencias;
    }

    public List<RestricaoAgrupadora> geraListaDeRestricaoAgrupadora(){
	Map<PairOfObjects<TipoRestricao, TipoOperador>, List<Restricao>> restricaoConfigMap = this.agrupaRestricoesPorTipoEOperador();
	return restricaoConfigMap.entrySet()
			.stream()
			.map(entry -> new RestricaoAgrupadora(entry.getKey().getTail(), new TipoDadoRestricao(entry.getKey().getHead()), entry.getValue()))
			.collect(Collectors.toList());
    }

    private Map<PairOfObjects<TipoRestricao, TipoOperador>, List<Restricao>> agrupaRestricoesPorTipoEOperador() {
	return this.restricoes.stream().collect(
			Collectors.groupingBy(restricao -> aPairOfObjects(restricao.getTipoRestricao(), restricao.getTipoOperador())));
    }

    @Override
    public String toString() {
	return "ConfiguracaoRc{" +
			"id=" + id +
			", nome='" + nome + '\'' +
			", prioridade=" + prioridade +
			", ciasAereas=" + ciasAereas +
			", statusConfiguracao=" + statusPublicacao +
			", restricoes=" + restricoes +
			", configuracaoRcProdutos=" + configuracaoRcProdutos +
			", versao=" + versao +
			", idOrigem=" + idOrigem +
			", editavel=" + editavel +
			", empresas=" + empresas +
			", agencias=" + agencias +
			'}';
    }

    @Override
    public boolean equals(Object o) {
	if (this == o)
	    return true;
	if (o == null || getClass() != o.getClass())
	    return false;
	ConfiguracaoRc that = (ConfiguracaoRc)o;
	return Objects.equals(id, that.id) &&
			Objects.equals(nome, that.nome) &&
			Objects.equals(prioridade, that.prioridade) &&
			Objects.equals(ciasAereas, that.ciasAereas) &&
			statusPublicacao == that.statusPublicacao &&
			Objects.equals(restricoes, that.restricoes) &&
			Objects.equals(versao, that.versao) &&
			Objects.equals(idOrigem, that.idOrigem) &&
			Objects.equals(editavel, that.editavel) &&
			Objects.equals(agencias, that.agencias) &&
			Objects.equals(configuracaoRcProdutos, that.configuracaoRcProdutos) &&
			Objects.equals(empresas, that.empresas);
    }

    @Override
    public int hashCode() {
	return Objects.hash(id, nome, prioridade, ciasAereas, statusPublicacao, restricoes, configuracaoRcProdutos,
	                    versao, idOrigem, editavel, empresas, agencias);
    }
}

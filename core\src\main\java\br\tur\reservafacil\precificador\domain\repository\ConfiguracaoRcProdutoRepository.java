package br.tur.reservafacil.precificador.domain.repository;

import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcProduto;

import java.util.List;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
public interface ConfiguracaoRcProdutoRepository
		extends Repository<ConfiguracaoRcProduto, Integer> {

    void deleteByConfigPrecificacaoId(Integer idConfigPrecificacao);

    List<ConfiguracaoRcProduto> findByConfigRcId(Integer configuracaoId);

}

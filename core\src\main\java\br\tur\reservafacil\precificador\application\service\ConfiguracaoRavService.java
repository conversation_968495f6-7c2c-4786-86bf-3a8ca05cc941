package br.tur.reservafacil.precificador.application.service;

import br.tur.reservafacil.dominio.Grupo;
import br.tur.reservafacil.dominio.Produto;
import br.tur.reservafacil.dominio.aereo.CiaAerea;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRav;
import br.tur.reservafacil.dominio.tipo.NacInt;
import br.tur.reservafacil.dominio.tipo.TipoConfigRav;
import br.tur.reservafacil.dominio.tipo.TipoTarifaAcordo;
import br.tur.reservafacil.precificador.application.service.cache.ConfiguracaoRavRedisCacheComponent;
import br.tur.reservafacil.precificador.domain.repository.ConfiguracaoRavRepository;
import com.newrelic.api.agent.Trace;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * Created by davidson on 7/1/16.
 */
@Service
public class ConfiguracaoRavService extends AbstractService<ConfiguracaoRav, Integer, ConfiguracaoRavRepository>  {

    @Autowired
    private ConfiguracaoRavServiceRetriever configuracaoRavServiceRetriever;

    public Optional<ConfiguracaoRav> getFirstMatchConfiguracaoRav(Integer empresaId, String codCia, String produto,
                                                                  TipoTarifaAcordo tipoTarifaAcordo, NacInt nacInt, Integer grupoId) {
        return Optional.ofNullable(configuracaoRavServiceRetriever.findByEmpresaCiaProdutoTipoTarifaNacInt(
                        empresaId, codCia, produto, tipoTarifaAcordo, nacInt, grupoId));
    }

    public List<ConfiguracaoRav> findByEmpresa(Integer empresaId, Integer grupoId, boolean isBuscaDeTeste) {
        return configuracaoRavServiceRetriever.findByEmpresa(empresaId, grupoId, isBuscaDeTeste);
    }

    @Cacheable(cacheNames = "ConfiguracaoRavService::findEagerByKey", cacheManager = "ehCacheManager")
    public ConfiguracaoRav findEagerByKey(Integer id) {
        return this.getRepository().findEagerByKey(id);
    }

    public Optional<ConfiguracaoRav> filterConfiguracaoRavPorPrecificavel(List<ConfiguracaoRav> configsRav, String codCia, String produto, TipoTarifaAcordo tipoTarifaAcordo, NacInt nacInt, Integer grupoId) {
        return configsRav.stream()
                         .filter(f -> (f.getTipoTarifaAcordo() != null && f.getTipoTarifaAcordo().equals(tipoTarifaAcordo)) || TipoTarifaAcordo.AMBAS.equals(f.getTipoTarifaAcordo()))
                         .filter(f -> (f.getNacInt() != null && f.getNacInt().equals(nacInt)) || NacInt.AMBOS.equals(f.getNacInt()))
                         .filter(f -> CollectionUtils.isNotEmpty(f.getCiasAereas()) && f.getCiasAereas().stream().map(CiaAerea::getCodigo).collect(Collectors.toList()).contains(codCia))
                         .filter(f -> f.getCiasAereas().stream().anyMatch(ciaAerea -> ciaAerea.getCodigo().equals(codCia) && (ciaAerea.getNacInt().equals(nacInt) || NacInt.AMBOS.equals(ciaAerea.getNacInt()))))
                         .filter(f -> CollectionUtils.isNotEmpty(f.getProdutos()) && f.getProdutos().stream().map(Produto::getNome).collect(Collectors.toList()).contains(produto))
                         .filter(f -> (CollectionUtils.isNotEmpty(f.getGrupos()) && f.getGrupos().stream().map(Grupo::getId).collect(Collectors.toList()).contains(grupoId)) || TipoConfigRav.PADRAO.equals(f.getTipoConfigRav()))
                         .findFirst();
    }

    /**
     *
     * FIXME: Existe apenas por causa que utilizando apenas Spring AOP, sem AspectJ, o @Cacheable não funciona quando chamando métodos da
     * própria classe. Como a idéia é eventualmente migrar para compile-time weaving com AspectJ, essa classe deve sumir e a classe voltar
     * ao que era antes.
     *
     */
    @Component
    static class ConfiguracaoRavServiceRetriever {

        @Autowired
        private ConfiguracaoRavRedisCacheComponent configuracaoRavRedisCacheComponent;

        @Cacheable(cacheNames = "ConfiguracaoRavService::findByEmpresaCiaProdutoTipoTarifa", sync = true, cacheManager = "ehCacheManager")
        @Trace
        public ConfiguracaoRav findByEmpresaCiaProdutoTipoTarifaNacInt(Integer empresaId, String codCia, String produto, TipoTarifaAcordo tipoTarifaAcordo,
                                                                       NacInt nacInt, Integer grupoId) {
            return configuracaoRavRedisCacheComponent.findByEmpresaCiaProdutoTipoTarifaNacInt(empresaId, codCia, produto, tipoTarifaAcordo, nacInt, grupoId);
        }

        @Cacheable(cacheNames = "ConfiguracaoRavService::findByEmpresa", sync = true, cacheManager = "ehCacheManager")
        @Trace
        public List<ConfiguracaoRav> findByEmpresa(Integer empresaId, Integer grupoId, boolean isBuscaDeTeste) {
            return configuracaoRavRedisCacheComponent.findByEmpresa(empresaId, grupoId, isBuscaDeTeste);
        }
    }
}

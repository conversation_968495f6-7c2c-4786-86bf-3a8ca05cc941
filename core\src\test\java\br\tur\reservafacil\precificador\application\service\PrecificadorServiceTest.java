package br.tur.reservafacil.precificador.application.service;

import akka.actor.Actor<PERSON>ef;
import akka.actor.ActorSystem;
import akka.testkit.TestProbe;
import br.tur.reservafacil.dominio.Credencial;
import br.tur.reservafacil.dominio.Empresa;
import br.tur.reservafacil.dominio.Produto;
import br.tur.reservafacil.dominio.Usuario;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoPrecificacao;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRav;
import br.tur.reservafacil.dominio.aereo.MarkupProduto;
import br.tur.reservafacil.dominio.aereo.common.ObfeeCredencial;
import br.tur.reservafacil.dominio.aereo.configuracaofee.ConfiguracaoFee;
import br.tur.reservafacil.dominio.aereo.disponibilidade.ParametroBuscaAerea;
import br.tur.reservafacil.dominio.aereo.tipo.FlagFeatureKeys;
import br.tur.reservafacil.dominio.common.Cambio;
import br.tur.reservafacil.dominio.common.Valor;
import br.tur.reservafacil.dominio.common.ValorTarifa;
import br.tur.reservafacil.dominio.intermediario.v0.to.precificacao.*;
import br.tur.reservafacil.dominio.tipo.Moeda;
import br.tur.reservafacil.dominio.tipo.SistEmis;
import br.tur.reservafacil.dominio.tipo.TipoFee;
import br.tur.reservafacil.dominio.tipo.TipoValor;
import br.tur.reservafacil.dominio.util.*;
import br.tur.reservafacil.dominio.util.configuracaoFee.ConfiguracaoFeeBuilder;
import br.tur.reservafacil.precificador.application.service.features.FlagFeatureService;
import br.tur.reservafacil.precificador.domain.DomainConfigTest;
import br.tur.reservafacil.precificador.domain.component.aereo.InternacionalizacaoComponent;
import br.tur.reservafacil.precificador.domain.component.restricao.RestricaoTreeComponent;
import br.tur.reservafacil.precificador.domain.repository.EmpresaRepository;
import br.tur.reservafacil.precificador.domain.repository.FilialRepository;
import br.tur.reservafacil.precificador.infrastructure.config.DbTestConfig;
import br.tur.reservafacil.precificador.infrastructure.config.ServiceTestConfig;
import br.tur.reservafacil.precificador.infrastructure.security.RequestMetadata;
import br.tur.reservafacil.precificador.infrastructure.security.SessaoComponent;
import br.tur.reservafacil.precificador.infrastructure.security.service.AutenticacaoService;
import com.rits.cloning.Cloner;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.env.Environment;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.web.WebAppConfiguration;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;

import static br.tur.reservafacil.dominio.aereo.tipo.FlagFeatureKeys.NOVO_CALCULO_FEE;
import static br.tur.reservafacil.dominio.tipo.FaixaEtaria.*;
import static br.tur.reservafacil.dominio.tipo.TipoConfigPrecificacao.PADRAO;
import static br.tur.reservafacil.dominio.tipo.TipoConfigPrecificacao.RESTRITA;
import static br.tur.reservafacil.dominio.tipo.TipoMarkup.*;
import static br.tur.reservafacil.dominio.tipo.TipoValor.TAXA_BOLSA;
import static java.util.Arrays.asList;
import static java.util.Collections.singletonList;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyBoolean;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyList;
import static org.mockito.Matchers.anyObject;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Created by ramon on 03/05/16.
 */
@ContextConfiguration(classes = {DbTestConfig.class, DomainConfigTest.class, ServiceTestConfig.class})
@WebAppConfiguration
@ActiveProfiles({"test", "no-auth"})
@SpringBootTest
public class PrecificadorServiceTest {

    private static final BigDecimal FATOR_7_PORCENTO     = new BigDecimal("0.07");
    private static final BigDecimal FATOR_30_PORCENTO    = new BigDecimal("0.3");
    private static final BigDecimal COMISSAO_FORNECEDOR  = new BigDecimal("0.06");
    private static final BigDecimal INCENTIVO_FORNECEDOR = new BigDecimal("0.16");
    private static final BigDecimal MARKUP_BUSCA         = new BigDecimal("0.360544");
    private static final String     PRODUTO_PRECIFICACAO = "VBI";
    private static final Produto    PRODUTO              = ProdutoBuilder.umProduto().comNome(PRODUTO_PRECIFICACAO).doBuild();

    private static final MarkupProduto MARKUP_PRODUTO_TAXA_BOLSA_MOEDA_USD = MarkupProdutoBuilder.umMarkupProduto().comTaxaBolsa(new BigDecimal(100.00)).comTaxaBolsaCHD(new BigDecimal(200.00)).comTaxaBolsaINF(new BigDecimal(300.00)).comProduto(PRODUTO).comMoedaRepasse(
            Moeda.USD).doBuild();
    private static final MarkupProduto MARKUP_PRODUTO_TAXA_BOLSA = MarkupProdutoBuilder.umMarkupProduto().comTaxaBolsa(new BigDecimal(100.00)).comTaxaBolsaCHD(new BigDecimal(200.00)).comTaxaBolsaINF(new BigDecimal(300.00)).comProduto(PRODUTO).doBuild();
    private static final MarkupProduto MARKUP_PRODUTO_TAXA_BOLSA_PERC = MarkupProdutoBuilder.umMarkupProduto().comTaxaBolsaPerc(new BigDecimal(0.10)).comTaxaBolsaCHDPerc(new BigDecimal(0.20)).comTaxaBolsaINFPerc(new BigDecimal(0.30)).comProduto(PRODUTO).doBuild();

    @Mock private ConfiguracaoPrecificacaoService configuracaoPrecificacaoService;

    @Mock private AutenticacaoService autenticacaoService;

    @Mock private ConfiguracaoRavService configuracaoRavService;

    @Mock private ConfiguracaoFeeService configuracaoFeeService;

    @Mock private ConfiguracaoDesativaValorService configuracaoDesativaValorService;

    @Mock private AplicadorCodigoContratoService aplicadorCodigoContratoService;

    @Spy private ActorRef precificavelActor = TestProbe.apply(ActorSystem.apply()).ref();

    @Mock private EmpresaProdutoDefaultService empresaProdutoDefaultService;

    @Mock private SessaoComponent sessaoComponent;

    @Spy private Cloner cloner = new Cloner();

    @Mock private RestricaoService restricaoService;

    @Mock private EmpresaService empresaService;

    @Mock private Environment environment;

    @Mock private RestricaoTreeComponent restricaoTreeComponent;

    @InjectMocks private PrecificadorService precificadorService;

    @InjectMocks
    private PrecificadorService.PrecificacaoRetriever retriever;

    @Mock private PerfilTarifarioService perfilTarifarioService;

    @Mock private ConfiguracaoPrecificacaoComercialService configuracaoPrecificacaoComercialService;

    @Mock  private ConfiguracaoFiltroRecomendacaoService configuracaoFiltroRecomendacaoService;

    @Mock private CambioService cambioService;

    @Mock private AplicadorTaxaOBFeeService aplicadorTaxaOBFeeService;

    @Mock private FlagFeatureService flagFeatureService;

    @Mock private ConfiguracaoPrecificacaoFretamentoService configuracaoPrecificacaoFretamentoService;

    @Mock private ConfiguracaoDuService configuracaoDuService;

    @Mock private InternacionalizacaoComponent internacionalizacaoComponent;

    @Mock private CondicaoPagamentoService condicaoPagamentoService;

    @Mock private FilialRepository filialRepository;

    @Mock private EmpresaRepository empresaRepository;

    @Mock private ConfiguracaoProdutoService configuracaoProdutoService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        retriever = spy(new PrecificadorService.PrecificacaoRetriever(configuracaoPrecificacaoService, configuracaoFiltroRecomendacaoService, configuracaoRavService, configuracaoPrecificacaoFretamentoService,
                configuracaoFeeService, autenticacaoService, cambioService, aplicadorCodigoContratoService,
                configuracaoDesativaValorService, cloner, restricaoService, configuracaoPrecificacaoComercialService,
                aplicadorTaxaOBFeeService, flagFeatureService, configuracaoDuService, internacionalizacaoComponent,
                condicaoPagamentoService,
                configuracaoProdutoService));
        precificadorService = spy(new PrecificadorService(autenticacaoService, cambioService, filialRepository, empresaRepository, empresaService, sessaoComponent, precificavelActor, perfilTarifarioService, retriever, configuracaoRavService, configuracaoFeeService, configuracaoPrecificacaoComercialService, configuracaoDuService, condicaoPagamentoService, flagFeatureService, configuracaoFiltroRecomendacaoService, configuracaoProdutoService));
        when(configuracaoPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(Optional.empty());
//        when(configuracaoRavService.getFirstMatchConfiguracaoRav(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Optional.empty());
//        when(configuracaoRavService.filterConfiguracaoRavPorPrecificavel(Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyInt())).thenReturn(Optional.empty());
        when(configuracaoFeeService.obterConfiguracaoFeePorPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any())).thenReturn(Optional.empty());
        when(autenticacaoService.currentUsuario()).thenReturn(new Usuario());
        when(empresaService.findByKey(anyInt())).thenReturn(new Empresa());
        when(environment.acceptsProfiles(anyString())).thenReturn(true);
        when(cloner.deepClone(Mockito.any())).thenCallRealMethod();
        when(perfilTarifarioService.getFirstMatchConfiguracaoPerfilTarifarioPorRestricao(any(), any(), any(), any())).thenReturn(Optional.of(new br.tur.reservafacil.dominio.aereo.perfilTarifario.PerfilTarifario()));
        when(configuracaoPrecificacaoComercialService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), any(), any(), anyString())).thenReturn(Optional.empty());
        when(aplicadorTaxaOBFeeService.aplicaTaxa(any(), any())).thenReturn(new ResultadoCalculoOBFee(new HashMap<>()));
        when(aplicadorTaxaOBFeeService.buscaObfeeCredencial(any())).thenReturn(Optional.empty());
        when(flagFeatureService.findAll()).thenReturn(new ArrayList<>());
        when(cambioService.buscaUltimoCambio(any())).thenReturn(new Cambio());

        RequestMetadata requestMetadata = new RequestMetadata();
        requestMetadata.setCorrelationId("ABC123");
        when(this.sessaoComponent.getRequestMetadata()).thenReturn(requestMetadata);
    }

    @Test
    public void testaDefinicaoMerchantOwn() {
        ValorTarifa valorTarifa = ValorTarifaBuilder.umValorTarifa().comTipoValor(TipoValor.MARKUP, BigDecimal.ONE).doBuild();

        Map<String, ValorTarifa> valoresCalculados = new HashMap<>();
        valoresCalculados.put("ADT", valorTarifa);

        Merchant merchant = this.retriever.obtemMerchant(valoresCalculados);
        assertEquals(Merchant.OWN, merchant);
    }

    @Test
    public void testaDefinicaoMerchantOwnFareZero() {
        Valor fare = new Valor(new BigDecimal("0.00"), BigDecimal.ZERO, BigDecimal.ZERO);
        ValorTarifa valorTarifa = ValorTarifaBuilder.umValorTarifa().comTarifa(fare).doBuild();

        Map<String, ValorTarifa> valoresCalculados = new HashMap<>();
        valoresCalculados.put("ADT", valorTarifa);

        Merchant merchant = this.retriever.obtemMerchant(valoresCalculados);
        assertEquals(Merchant.OWN, merchant);
    }

    @Test
    public void testaDefinicaoMerchantCia() {
        ValorTarifa valorTarifa = ValorTarifaBuilder.umValorTarifa().comTipoValor(TipoValor.TAXAEMBARQUE, BigDecimal.ONE).doBuild();

        Map<String, ValorTarifa> valoresCalculados = new HashMap<>();
        valoresCalculados.put("ADT", valorTarifa);

        Merchant merchant = this.retriever.obtemMerchant(valoresCalculados);
        assertEquals(Merchant.CIA, merchant);
    }

    @Test
    public void testaDefinicaoMerchantBoth() {
        ValorTarifa valorTarifa = ValorTarifaBuilder.umValorTarifa().comTipoValor(TipoValor.RAV, BigDecimal.ONE).comTipoValor(TipoValor.TAXA_BOLSA, BigDecimal.ONE).doBuild();

        Map<String, ValorTarifa> valoresCalculados = new HashMap<>();
        valoresCalculados.put("ADT", valorTarifa);

        Merchant merchant = this.retriever.obtemMerchant(valoresCalculados);
        assertEquals(Merchant.BOTH, merchant);
    }

    @Test
    public void testaDefinicaoMerchantValoresNulos() {
        Map<String, ValorTarifa> valoresCalculados = new HashMap<>();
        Merchant merchant = this.retriever.obtemMerchant(valoresCalculados);
        assertEquals(Merchant.CIA, merchant);
    }

    @Test
    public void testaCalculoObfee() {
        MarkupProduto markupProduto = MarkupProdutoBuilder.umMarkupProduto().comProduto(PRODUTO).doBuild();
        final ConfiguracaoPrecificacao
                configPrecificRestrita =
                ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comId(1).comTipoPrecificacao(RESTRITA).comMarkup(markupProduto).comTipoMarkup(BRUTO).doBuild();

        Valor tarifa = new Valor(new BigDecimal("100.00"), BigDecimal.ZERO, BigDecimal.ZERO);
        ValorTarifa valorTarifa = ValorTarifaBuilder.umValorTarifa().comTipoValor(TipoValor.OB_FEE, BigDecimal.TEN).comTarifa(tarifa).doBuild();

        Map<String, ValorTarifa> valoresCalculados = new HashMap<>();
        valoresCalculados.put("ADT", valorTarifa);

        ResultadoCalculoOBFee resultadoCalculoOBFee = new ResultadoCalculoOBFee(valoresCalculados);
        ObfeeCredencial obfeeCredencial = new ObfeeCredencial();
        obfeeCredencial.setCredencial(new Credencial());
        when(this.aplicadorTaxaOBFeeService.aplicaTaxa(any(), any())).thenReturn(resultadoCalculoOBFee);
        when(this.aplicadorTaxaOBFeeService.deveAplicarTaxaOb(any(), any(), any())).thenReturn(true);
        when(this.aplicadorTaxaOBFeeService.buscaObfeeCredencial(any())).thenReturn(Optional.of(obfeeCredencial));
        when(configuracaoPrecificacaoService.findPadraoByPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(configuracaoPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(Optional.of(configPrecificRestrita));

        Precificavel precificavel = precificavelMock(false);
        ResultadoPrecificacao resultadoPrecificacao = precificadorService.precifica(Collections.singletonList(precificavel), false, false, new ParametroBuscaAerea(), false).get(0);

        assertEquals(BigDecimal.TEN.setScale(2, RoundingMode.HALF_EVEN), resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.OB_FEE));
        assertEquals(new BigDecimal("100.00"), resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTarifa().getValorNeto());
    }

    @Test
    public void testaCalculoConfFeeComTaxas() {
        MarkupProduto markupProduto = MarkupProdutoBuilder.umMarkupProduto().comProduto(PRODUTO).comFee(FATOR_30_PORCENTO).doBuild();
        final ConfiguracaoPrecificacao
                configPrecificRestrita =
                ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comId(1).comTipoPrecificacao(RESTRITA).comMarkup(markupProduto).comTipoMarkup(TOTAL).doBuild();

        ConfiguracaoFee confFee = ConfiguracaoFeeBuilder.umaConfiguracaoFee().comPercentual(FATOR_7_PORCENTO).comValorMinimo(BigDecimal.ONE).doBuild();
        confFee.setTipoFee(TipoFee.TAXAS);
        when(this.configuracaoFeeService.filterConfiguracaoFeePorPrecificavel(any(), any(), any(), any())).thenReturn(Optional.of(confFee));
        when(configuracaoPrecificacaoService.findPadraoByPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(configuracaoPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(Optional.of(configPrecificRestrita));

        Precificavel precificavel = precificavelMock(false);
        precificavel.getTarifacoes().get(0).getValorTarifa().setDu(BigDecimal.TEN);
        ResultadoPrecificacao resultadoPrecificacao = precificadorService.precifica(Collections.singletonList(precificavel), false, false, new ParametroBuscaAerea(), false).get(0);

        assertEquals(new BigDecimal(1.40).setScale(2, RoundingMode.HALF_EVEN), resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.FEE));
    }

    @Test
    public void testaCalculoFeeByMarkupProdutoComTaxasAoInvesDeConfigFee() {
        MarkupProduto markupProduto = MarkupProdutoBuilder.umMarkupProduto().comProduto(PRODUTO).comFee(FATOR_7_PORCENTO).doBuild();
        final ConfiguracaoPrecificacao
                configPrecificRestrita =
                ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comId(1).comTipoPrecificacao(RESTRITA).comMarkup(markupProduto).comTipoMarkup(TOTAL).doBuild();

        when(configuracaoPrecificacaoService.findPadraoByPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(configuracaoPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(Optional.of(configPrecificRestrita));

        Precificavel precificavel = precificavelMock(false);
        precificavel.getTarifacoes().get(0).getValorTarifa().setDu(BigDecimal.TEN);
        ResultadoPrecificacao resultadoPrecificacao = precificadorService.precifica(Collections.singletonList(precificavel), false, false, new ParametroBuscaAerea(), false).get(0);

        assertEquals(new BigDecimal(1.40).setScale(2, RoundingMode.HALF_EVEN), resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.FEE));
    }

    @Test
    public void testaCalculoComisaoIncentivo() {
        MarkupProduto markupProduto = MarkupProdutoBuilder.umMarkupProduto().comProduto(PRODUTO).doBuild();
        final ConfiguracaoPrecificacao
                configPrecificRestrita =
                ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comId(1).comComissao(FATOR_7_PORCENTO).comIncentivo(FATOR_7_PORCENTO).comTipoPrecificacao(RESTRITA).comMarkup(
                        markupProduto).comTipoMarkup(BRUTO).doBuild();

        when(configuracaoPrecificacaoService.findPadraoByPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(configuracaoPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(Optional.of(configPrecificRestrita));

        Precificavel precificavel = precificavelMock(false);
        ResultadoPrecificacao resultadoPrecificacao = precificadorService.precifica(Collections.singletonList(precificavel), false, false, new ParametroBuscaAerea(), false).get(0);

        BigDecimal comissao = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.COMISSAO_FORNECEDOR);
        BigDecimal incentivo = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.INCENTIVO_FORNECEDOR);
        BigDecimal totalSobretaxa = resultadoPrecificacao.getTotalSobretaxas(ADT.name());
        BigDecimal totalDetalhesTarifa = resultadoPrecificacao.getTotalDetalhesTarifa(ADT.name());

        assertTrue(new BigDecimal("0.00").setScale(2, RoundingMode.HALF_EVEN).compareTo(totalSobretaxa) == 0);
        assertTrue(new BigDecimal("1.40").setScale(2, RoundingMode.HALF_EVEN).compareTo(totalDetalhesTarifa) == 0);
        assertTrue(new BigDecimal("0.70").compareTo(comissao) == 0);
        assertTrue(new BigDecimal("0.70").compareTo(incentivo) == 0);
    }

    @Test
    public void testaCalculoTaxaBolsa() {
        MarkupProduto markupProduto = MarkupProdutoBuilder.umMarkupProduto().comProduto(PRODUTO).comTaxaBolsa(BigDecimal.TEN).doBuild();
        final ConfiguracaoPrecificacao
                configPrecificRestrita =
                ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comId(1).comComissao(FATOR_7_PORCENTO).comIncentivo(FATOR_7_PORCENTO).comTipoPrecificacao(RESTRITA).comMarkup(
                        markupProduto).comTipoMarkup(BRUTO).doBuild();

        when(configuracaoPrecificacaoService.findPadraoByPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(configuracaoPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(Optional.of(configPrecificRestrita));

        Precificavel precificavel = precificavelMock(false);
        ResultadoPrecificacao resultadoPrecificacao = precificadorService.precifica(Collections.singletonList(precificavel), false, false, new ParametroBuscaAerea(), false).get(0);

        BigDecimal comissao = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.COMISSAO_FORNECEDOR);
        BigDecimal incentivo = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.INCENTIVO_FORNECEDOR);
        BigDecimal totalSobretaxa = resultadoPrecificacao.getTotalSobretaxas(ADT.name());
        BigDecimal totalDetalhesTarifa = resultadoPrecificacao.getTotalDetalhesTarifa(ADT.name());
        BigDecimal taxaBolsa = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.TAXA_BOLSA);

        BigDecimal dez = new BigDecimal("10.00").setScale(2, RoundingMode.HALF_EVEN);
        assertTrue(dez.compareTo(totalSobretaxa) == 0);
        assertTrue(new BigDecimal("11.40").setScale(2, RoundingMode.HALF_EVEN).compareTo(totalDetalhesTarifa) == 0);
        assertTrue(new BigDecimal("0.70").compareTo(comissao) == 0);
        assertTrue(new BigDecimal("0.70").compareTo(incentivo) == 0);
        assertTrue(dez.compareTo(taxaBolsa) == 0);
    }

    @Test
    public void testaCalculoTaxaBolsaPerc() {
        MarkupProduto markupProduto = MarkupProdutoBuilder.umMarkupProduto().comProduto(PRODUTO).comTaxaBolsaPerc(FATOR_30_PORCENTO).doBuild();
        final ConfiguracaoPrecificacao
                configPrecificRestrita =
                ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comId(1).comComissao(FATOR_7_PORCENTO).comIncentivo(FATOR_7_PORCENTO).comTipoPrecificacao(RESTRITA).comMarkup(
                        markupProduto).comTipoMarkup(BRUTO).doBuild();

        when(configuracaoPrecificacaoService.findPadraoByPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(configuracaoPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(Optional.of(configPrecificRestrita));

        Precificavel precificavel = precificavelMock(false);
        ResultadoPrecificacao resultadoPrecificacao = precificadorService.precifica(Collections.singletonList(precificavel), false, false, new ParametroBuscaAerea(), false).get(0);

        BigDecimal comissao = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.COMISSAO_FORNECEDOR);
        BigDecimal incentivo = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.INCENTIVO_FORNECEDOR);
        BigDecimal totalSobretaxa = resultadoPrecificacao.getTotalSobretaxas(ADT.name());
        BigDecimal totalDetalhesTarifa = resultadoPrecificacao.getTotalDetalhesTarifa(ADT.name());
        BigDecimal taxaBolsa = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.TAXA_BOLSA);

        BigDecimal trez = new BigDecimal("3.00").setScale(2, RoundingMode.HALF_EVEN);
        assertTrue(trez.compareTo(totalSobretaxa) == 0);
        assertTrue(new BigDecimal("4.40").setScale(2, RoundingMode.HALF_EVEN).compareTo(totalDetalhesTarifa) == 0);
        assertTrue(new BigDecimal("0.70").compareTo(comissao) == 0);
        assertTrue(new BigDecimal("0.70").compareTo(incentivo) == 0);
        assertTrue(trez.compareTo(taxaBolsa) == 0);
    }

    /**
     * Isso acontece quando a configuração é de markup líquido.
     * Neste cenário, devemos subtrair o valor da rav/du do markup
     * Explicação do teste: Como o markup é de 30% e queremos o markup líquido, não importa qual o valor da RAV ou da DU, o total de markup + RAV + DU deve dar os 30%.
     */
    @Test
    public void testaCalculoMarkupComAbatimentoDeDu() {
        final MarkupProduto markupProduto = MarkupProdutoBuilder.umMarkupProduto().comMarkupBusca(FATOR_30_PORCENTO).comFee(FATOR_7_PORCENTO).comDuZerada().comProduto(PRODUTO).doBuild();

        final ConfiguracaoPrecificacao
                configPrecific =
                ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comId(1).comTipoMarkup(LIQUIDO).comMarkup(markupProduto).comTipoPrecificacao(RESTRITA).doBuild();
        when(configuracaoPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(Optional.ofNullable(configPrecific));

        ConfiguracaoRav confRav = ConfiguracaoRavBuilder.umaConfiguracaoRav().comPercentual(FATOR_7_PORCENTO).comValorMinimo(BigDecimal.ZERO).doBuild();
        when(configuracaoRavService.filterConfiguracaoRavPorPrecificavel(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Optional.of(confRav));

        Precificavel precificavel = precificavelMock(true);
        precificavel.setProdutoPrecificacao(PRODUTO.getNome());
        precificavel.getTarifacoes().stream().forEach(tarifaRec -> tarifaRec.getValorTarifa().insereValor(TipoValor.DU, BigDecimal.ONE));

        ResultadoPrecificacao resultadoPrecificacao = precificadorService.precifica(Collections.singletonList(precificavel), false, false, new ParametroBuscaAerea(), false).get(0);

        BigDecimal rav = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.RAV);
        BigDecimal du = resultadoPrecificacao.getTotalTaxas(ADT.name());
        BigDecimal markup = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.MARKUP);

        assertTrue("Não deveria ter DU", BigDecimal.ZERO.compareTo(du) == 0);
        assertTrue(new BigDecimal("0.91").compareTo(rav) == 0);
        assertTrue(new BigDecimal("3.91").compareTo(markup.add(rav).add(du)) == 0);

    }

    @Test
    public void testaCalculoComisaoIncentivoEFeeFornecedorETaxasMarkup() {
        final MarkupProduto
                markupProduto =
                MarkupProdutoBuilder.umMarkupProduto().comMarkupBusca(FATOR_7_PORCENTO).comComissao(FATOR_7_PORCENTO).comIncentivo(FATOR_7_PORCENTO).comRepasse(FATOR_7_PORCENTO).comFee(
                        FATOR_7_PORCENTO).comProduto(PRODUTO).doBuild();

        final ConfiguracaoPrecificacao
                configPrecificRestrita =
                ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comId(1).comComissao(FATOR_7_PORCENTO).comIncentivo(FATOR_7_PORCENTO).comTipoMarkup(BRUTO).comTipoPrecificacao(
                        RESTRITA).comMarkup(markupProduto).doBuild();

        Precificavel precificavel = precificavelMock(false);
        precificavel.setProdutoPrecificacao(PRODUTO_PRECIFICACAO);

        when(configuracaoPrecificacaoService.findPadraoByPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(configuracaoPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(Optional.of(configPrecificRestrita));

        ResultadoPrecificacao resultadoPrecificacao = precificadorService.precifica(Collections.singletonList(precificavel), false, false, new ParametroBuscaAerea(), false).get(0);

        BigDecimal comissaoFornec = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.COMISSAO_FORNECEDOR);
        BigDecimal incentivoFornec = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.INCENTIVO_FORNECEDOR);
        BigDecimal feeFornec = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.FEE);

        BigDecimal markup = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.MARKUP);
        BigDecimal comissaoCliente = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.COMISSAO_CLIENTE);
        BigDecimal incentivoCliente = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.INCENTIVO_CLIENTE);
        BigDecimal repasseCliente = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.REPASSE_CLIENTE);

        BigDecimal totalSobretaxa = resultadoPrecificacao.getTotalSobretaxas(ADT.name());
        BigDecimal totalDetalhesTarifa = resultadoPrecificacao.getTotalDetalhesTarifa(ADT.name());
        assertTrue(new BigDecimal("1.45").setScale(2, RoundingMode.HALF_EVEN).compareTo(totalSobretaxa) == 0);
        assertTrue(new BigDecimal("0.75").compareTo(feeFornec) == 0);

        assertTrue(new BigDecimal("3.50").setScale(2, RoundingMode.HALF_EVEN).compareTo(totalDetalhesTarifa) == 0);
        assertTrue(new BigDecimal("0.70").compareTo(markup) == 0);
        //Markup Real = Markup Real - Markup Busca
        assertTrue(new BigDecimal("0.70").compareTo(comissaoFornec) == 0);
        assertTrue(new BigDecimal("0.70").compareTo(incentivoFornec) == 0);
        assertTrue(new BigDecimal("0.70").compareTo(comissaoCliente) == 0);
        assertTrue(new BigDecimal("0.70").compareTo(incentivoCliente) == 0);
        assertTrue(new BigDecimal("0.70").compareTo(repasseCliente) == 0);
    }

    /**
     * Testa o calculo de comissão fornecedor, incentivo fornecedor, fee, comissão cliente, incentivo cliente e repasse cliente,
     * com mais de uma configuração (configurações acumulativas).
     */
    @Test
    public void testaCalculoComisaoIncentivoEFeeFornecedorETaxasMarkupComConfAcumulativa() {
        final MarkupProduto
                markupProduto =
                MarkupProdutoBuilder.umMarkupProduto().comMarkupBusca(FATOR_7_PORCENTO).comComissao(FATOR_7_PORCENTO).comIncentivo(FATOR_7_PORCENTO).comRepasse(FATOR_7_PORCENTO).comFee(
                        FATOR_7_PORCENTO).comProduto(PRODUTO).doBuild();

        final ConfiguracaoPrecificacao
                configPrecificPadrao =
                ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comId(1).comComissao(FATOR_7_PORCENTO).comIncentivo(FATOR_7_PORCENTO).comTipoMarkup(BRUTO).comTipoPrecificacao(
                        PADRAO).comMarkup(markupProduto).doBuild();

        final MarkupProduto markupConfigRestrita = MarkupProdutoBuilder.umMarkupProduto().comProduto(PRODUTO).doBuild();
        final ConfiguracaoPrecificacao
                configPrecificRestrita =
                ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comId(2).comTipoMarkup(BRUTO).comTipoPrecificacao(RESTRITA).comMarkup(markupConfigRestrita).doBuild();

        when(configuracaoPrecificacaoService.findPadraoByPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any(), eq(Boolean.FALSE))).thenReturn(new ArrayList<>(asList(configPrecificPadrao, configPrecificPadrao)));
        when(configuracaoPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(Optional.of(configPrecificRestrita));

        Precificavel precificavel = precificavelMock(false);
        precificavel.setProdutoPrecificacao(PRODUTO_PRECIFICACAO);

        ResultadoPrecificacao resultadoPrecificacao = precificadorService.precifica(Collections.singletonList(precificavel), false, false, new ParametroBuscaAerea(), false).get(0);

        BigDecimal comissaoFornec = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.COMISSAO_FORNECEDOR);
        BigDecimal incentivoFornec = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.INCENTIVO_FORNECEDOR);
        BigDecimal feeFornec = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.FEE);

        BigDecimal markup = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.MARKUP);
        BigDecimal comissaoCliente = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.COMISSAO_CLIENTE);
        BigDecimal incentivoCliente = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.INCENTIVO_CLIENTE);
        BigDecimal repasseCliente = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.REPASSE_CLIENTE);

        BigDecimal totalSobretaxa = resultadoPrecificacao.getTotalSobretaxas(ADT.name());
        BigDecimal totalDetalhesTarifa = resultadoPrecificacao.getTotalDetalhesTarifa(ADT.name());
        assertTrue(new BigDecimal("3.00").compareTo(totalSobretaxa) == 0);
        assertTrue(new BigDecimal("1.60").compareTo(feeFornec) == 0);

        assertTrue(new BigDecimal("7.00").compareTo(totalDetalhesTarifa) == 0);
        assertTrue(new BigDecimal("1.40").compareTo(markup) == 0);
        assertTrue(new BigDecimal("1.40").compareTo(comissaoFornec) == 0);
        assertTrue(new BigDecimal("1.40").compareTo(incentivoFornec) == 0);
        assertTrue(new BigDecimal("1.40").compareTo(comissaoCliente) == 0);
        assertTrue(new BigDecimal("1.40").compareTo(incentivoCliente) == 0);
        assertTrue(new BigDecimal("1.40").compareTo(repasseCliente) == 0);
    }

    @Test
    public void testPercentageDetailsTax() {

        MarkupProduto markupProduto = MarkupProdutoBuilder.umMarkupProduto().comProduto(PRODUTO).comFee(FATOR_7_PORCENTO).comTaxaBolsaPerc(FATOR_7_PORCENTO).doBuild();
        final ConfiguracaoPrecificacao configPrecificRestrita =
                ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comId(1).comTipoPrecificacao(RESTRITA).comMarkup(markupProduto).comTipoMarkup(BRUTO).doBuild();

        ConfiguracaoRav confRav = ConfiguracaoRavBuilder.umaConfiguracaoRav().comPercentual(FATOR_7_PORCENTO).comValorMinimo(new BigDecimal("0.2")).doBuild();
        when(configuracaoRavService.filterConfiguracaoRavPorPrecificavel(Mockito.any(), Mockito.anyString(), Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.anyInt())).thenReturn(Optional.of(confRav));

        when(configuracaoPrecificacaoService.findPadraoByPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(configuracaoPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(Optional.of(configPrecificRestrita));

        Precificavel precificavel = precificavelMock(false);

        ResultadoPrecificacao resultadoPrecificacao = precificadorService.precifica(Collections.singletonList(precificavel), false, false, new ParametroBuscaAerea(), false).get(0);

        assertEquals(new BigDecimal("0.07"), resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.PERCENTAGE_TAXA_BOLSA));
        assertEquals(new BigDecimal("0.07"), resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.PERCENTAGE_FEE));
        assertEquals(new BigDecimal("0.07"), resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.PERCENTAGE_RAV));
    }

    @Test
    public void testPercentageDetailsTaxComValorMinimo() {

        MarkupProduto markupProduto = MarkupProdutoBuilder.umMarkupProduto().comProduto(PRODUTO).comMarkupBusca(FATOR_30_PORCENTO).comTaxaBolsa(BigDecimal.ONE).comFee(FATOR_7_PORCENTO).doBuild();
        final ConfiguracaoPrecificacao
                configPrecificRestrita =
                ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comId(1).comTipoPrecificacao(RESTRITA).comMarkup(markupProduto).comTipoMarkup(BRUTO).doBuild();

        ConfiguracaoFee confFee = ConfiguracaoFeeBuilder.umaConfiguracaoFee().comPercentual(FATOR_7_PORCENTO).comValorMinimo(new BigDecimal("0.7")).doBuild();
        ConfiguracaoRav confRav = ConfiguracaoRavBuilder.umaConfiguracaoRav().comPercentual(FATOR_7_PORCENTO).comValorMinimo(new BigDecimal("1.1")).doBuild();

        when(configuracaoRavService.filterConfiguracaoRavPorPrecificavel(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Optional.of(confRav));
        when(configuracaoFeeService.filterConfiguracaoFeePorPrecificavel(anyList(), any(), any(), any())).thenReturn(Optional.of(confFee));
        when(flagFeatureService.isActive(FlagFeatureKeys.NOVO_CALCULO_FEE)).thenReturn(true);

        when(configuracaoPrecificacaoService.findPadraoByPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(configuracaoPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(Optional.of(configPrecificRestrita));
        Precificavel precificavel = precificavelMock(false);

        ResultadoPrecificacao resultadoPrecificacao = precificadorService.precifica(Collections.singletonList(precificavel), false, false, new ParametroBuscaAerea(), false).get(0);

        assertEquals(new BigDecimal("0.07"), resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.PERCENTAGE_FEE));
        assertEquals(new BigDecimal("0.08"), resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.PERCENTAGE_RAV));
    }

    @Test
    public void testaCalculoRavRecomendacaoAerea() {
        final MarkupProduto markupProduto = MarkupProdutoBuilder.umMarkupProduto().comProduto(PRODUTO).doBuild();

        final ConfiguracaoPrecificacao
                configPrecificRestrita =
                ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comId(1).comTipoMarkup(BRUTO).comTipoPrecificacao(RESTRITA).comMarkup(markupProduto).doBuild();

        when(configuracaoPrecificacaoService.findPadraoByPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(configuracaoPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(Optional.of(configPrecificRestrita));

        ConfiguracaoRav confRav = ConfiguracaoRavBuilder.umaConfiguracaoRav().comPercentual(FATOR_7_PORCENTO).comValorMinimo(new BigDecimal("0.2")).doBuild();
        when(configuracaoRavService.filterConfiguracaoRavPorPrecificavel(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Optional.of(confRav));

        Precificavel precificavel = precificavelMock(false);

        ResultadoPrecificacao resultadoPrecificacao = precificadorService.precifica(Collections.singletonList(precificavel), false, false, new ParametroBuscaAerea(), false).get(0);

        BigDecimal rav = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.RAV);
        BigDecimal totalSobretaxa = resultadoPrecificacao.getTotalSobretaxas(ADT.name());
        assertTrue(new BigDecimal("0.70").setScale(2, RoundingMode.HALF_EVEN).compareTo(totalSobretaxa) == 0);
        assertTrue(new BigDecimal("0.70").compareTo(rav) == 0);
    }

    @Test
    public void testaCalculoMarkupLiquidoIncentivoLiquidoEFee() {
        final MarkupProduto markupProduto = MarkupProdutoBuilder.umMarkupProduto().comMarkupBusca(MARKUP_BUSCA).comFee(FATOR_7_PORCENTO).comProduto(PRODUTO).doBuild();

        final ConfiguracaoPrecificacao
                configPrecificRestrita =
                ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comId(1).comTipoMarkup(LIQUIDO).comComissao(COMISSAO_FORNECEDOR).comIncentivo(INCENTIVO_FORNECEDOR).comTipoPrecificacao(
                        RESTRITA).comMarkup(markupProduto).doBuild();

        when(this.configuracaoPrecificacaoService.findPadraoByPrecificavel(Mockito.anyList(), anyObject(), any())).thenReturn(new ArrayList<>());
        when(this.configuracaoPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), anyObject(), any(), anyBoolean())).thenReturn(Optional.of(configPrecificRestrita));

        Precificavel precificavel = precificavelMock(false);

        ResultadoPrecificacao resultadoPrecificacao = precificadorService.precifica(Collections.singletonList(precificavel), false, false, new ParametroBuscaAerea(), false).get(0);

        BigDecimal commisao = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.COMISSAO_FORNECEDOR);
        BigDecimal incentivo = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.INCENTIVO_FORNECEDOR);
        BigDecimal markup = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.MARKUP);
        BigDecimal fee = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.FEE);
        assertTrue(new BigDecimal("0.60").compareTo(commisao) == 0);
        assertTrue(new BigDecimal("1.50").compareTo(incentivo) == 0);
        assertTrue(new BigDecimal("0.74").compareTo(markup) == 0);
        assertTrue(new BigDecimal("0.75").compareTo(fee) == 0);
    }

    @Test
    public void testaCalculoRavValorMinimoMaior() {
        final MarkupProduto markupProduto = MarkupProdutoBuilder.umMarkupProduto().comProduto(PRODUTO).doBuild();

        final ConfiguracaoPrecificacao
                configPrecificRestrita =
                ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comId(1).comTipoPrecificacao(RESTRITA).comTipoMarkup(BRUTO).comMarkup(markupProduto).doBuild();

        when(configuracaoPrecificacaoService.findPadraoByPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(configuracaoPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(Optional.of(configPrecificRestrita));

        ConfiguracaoRav confRav = ConfiguracaoRavBuilder.umaConfiguracaoRav().comPercentual(FATOR_7_PORCENTO).comValorMinimo(BigDecimal.ONE).doBuild();
        when(configuracaoRavService.filterConfiguracaoRavPorPrecificavel(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Optional.of(confRav));

        Precificavel precificavel = precificavelMock(false);
        ResultadoPrecificacao resultadoPrecificacao = precificadorService.precifica(Collections.singletonList(precificavel), false, false, new ParametroBuscaAerea(), false).get(0);

        BigDecimal rav = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.RAV);
        BigDecimal totalSobretaxa = resultadoPrecificacao.getTotalSobretaxas(ADT.name());
        assertTrue(BigDecimal.ONE.setScale(2, RoundingMode.HALF_EVEN).compareTo(totalSobretaxa) == 0);
        assertTrue(BigDecimal.ONE.setScale(2, RoundingMode.HALF_EVEN).compareTo(rav) == 0);
    }

    @Test
    public void testaCalculoRav() {
        final MarkupProduto markupProduto = MarkupProdutoBuilder.umMarkupProduto().comProduto(PRODUTO).doBuild();

        final ConfiguracaoPrecificacao
                configPrecificRestrita =
                ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comId(1).comTipoMarkup(BRUTO).comTipoPrecificacao(RESTRITA).comMarkup(markupProduto).doBuild();

        when(configuracaoPrecificacaoService.findPadraoByPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(configuracaoPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(Optional.of(configPrecificRestrita));

        ConfiguracaoRav confRav = ConfiguracaoRavBuilder.umaConfiguracaoRav().comPercentual(FATOR_7_PORCENTO).comValorMinimo(new BigDecimal("0.2")).doBuild();
        when(configuracaoRavService.filterConfiguracaoRavPorPrecificavel(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Optional.of(confRav));

        Precificavel precificavel = precificavelMock(false);

        List<ResultadoPrecificacao> resultadosPrecificacoes = precificadorService.precifica(Collections.singletonList(precificavel), Boolean.FALSE, false, new ParametroBuscaAerea(), false);

        BigDecimal du = resultadosPrecificacoes.get(0).getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.DU);
        BigDecimal rav = resultadosPrecificacoes.get(0).getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.RAV);
        BigDecimal totalSobretaxa = resultadosPrecificacoes.get(0).getTotalSobretaxas(ADT.name());
        assertTrue(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_EVEN).compareTo(du) == 0);
        assertTrue(new BigDecimal("0.70").setScale(2, RoundingMode.HALF_EVEN).compareTo(totalSobretaxa) == 0);
        assertTrue(new BigDecimal("0.70").compareTo(rav) == 0);
    }

    @Test
    public void testaCalculoPrecificacaoComValoresDesativados() {

        final MarkupProduto markupProduto = MarkupProdutoBuilder.umMarkupProduto().comProduto(PRODUTO).doBuild();

        final ConfiguracaoPrecificacao
                configPrecificRestrita =
                ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comId(1).comTipoMarkup(BRUTO).comTipoPrecificacao(RESTRITA).comMarkup(markupProduto).doBuild();

        List<TipoValor> valoresDesativados = Arrays.asList(TipoValor.DU, TipoValor.TAXAEMBARQUE);

        when(configuracaoPrecificacaoService.findPadraoByPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(configuracaoPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(Optional.of(configPrecificRestrita));
        when(configuracaoDesativaValorService.findByProdutoEmpresa(Mockito.any(), Mockito.any())).thenReturn(valoresDesativados);

        ConfiguracaoRav confRav = ConfiguracaoRavBuilder.umaConfiguracaoRav().comPercentual(FATOR_7_PORCENTO).comValorMinimo(new BigDecimal("0.2")).doBuild();
        when(configuracaoRavService.getFirstMatchConfiguracaoRav(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Optional.of(confRav));

        Precificavel precificavel = precificavelMock(false);

        List<ResultadoPrecificacao> resultadosPrecificacoes = precificadorService.precifica(Collections.singletonList(precificavel), Boolean.FALSE, false, new ParametroBuscaAerea(), false);

        assertEquals(resultadosPrecificacoes.get(0).getValoresDesativados().size(), 2);
        assertTrue(resultadosPrecificacoes.get(0).getValoresDesativados().get(0).compareTo(TipoValor.DU) == 0);
        assertTrue(resultadosPrecificacoes.get(0).getValoresDesativados().get(1).compareTo(TipoValor.TAXAEMBARQUE) == 0);

    }

    @Test
    @Disabled
    public void testExclusaoVoo() {
        when(configuracaoPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Optional.empty());

        List<ResultadoPrecificacao> resultadosPrecificacoes = precificadorService.precifica(singletonList(precificavelMock(false)), Boolean.TRUE, false, new ParametroBuscaAerea(), false);

        assertTrue(resultadosPrecificacoes.stream().allMatch(resultadoPrecificacao -> resultadoPrecificacao.getIdConfigRestrita().equals(-404)));
        verify(precificavelActor, times(2)).tell(any(), any());
    }

    @Test
    @Disabled
    public void testExclusaoVooPorConfigDeExclusao() {
        when(configuracaoPrecificacaoService.deveExcluirVooPorConfiguracaoDeExclusao(Mockito.anyList(), Mockito.any(), Mockito.any())).thenReturn(true);

        List<ResultadoPrecificacao> resultadosPrecificacoes = precificadorService.precifica(singletonList(precificavelMock(false)), Boolean.TRUE, false, new ParametroBuscaAerea(), false);

        assertTrue(resultadosPrecificacoes.stream().allMatch(resultadoPrecificacao -> resultadoPrecificacao.getIdConfigRestrita().equals(-404)));
        verify(configuracaoPrecificacaoService, times(1)).deveExcluirVooPorConfiguracaoDeExclusao(Mockito.anyList(), Mockito.any(), Mockito.any());
        verify(precificavelActor, times(2)).tell(any(), any());
    }

    private List<Tarifacao> tarifacoesMock(boolean comDu) {
        Tarifacao tarifacao = new Tarifacao();
        tarifacao.setId(ADT.name());
        tarifacao.setFaixaEtaria(ADT);
        tarifacao.setValorTarifa(valorTarifaMock(comDu));
        return Collections.singletonList(tarifacao);
    }

    private Precificavel precificavelMock(boolean comDu) {
        Precificavel precificavel = new Precificavel();
        precificavel.setCiaAereaViagem("JJ");
        precificavel.setTarifacoes(tarifacoesMock(comDu));
        precificavel.setSegmentos(segmentosMock());
        precificavel.setProdutoPrecificacao(PRODUTO_PRECIFICACAO);
        precificavel.setNacional(Boolean.TRUE);
        precificavel.setSistEmis(SistEmis.LATAM);
        return precificavel;
    }

    private List<Segmento> segmentosMock() {
        List<Segmento> segmentos = new ArrayList<>();
        Segmento segmento = new Segmento();
        segmento.setVoos(voosMock());
        segmentos.add(segmento);
        return segmentos;

    }

    private List<Voo> voosMock() {
        List<Voo> voos = new ArrayList<>();
        Voo voo = new Voo();
        voo.setCiaAereaMarketing("JJ");
        voo.setDataOrigem(LocalDateTime.now());
        voo.setAeroportoOrigem(UUID.randomUUID().toString().substring(0, 3).toUpperCase());
        voo.setAeroportoDestino(UUID.randomUUID().toString().substring(0, 3).toUpperCase());
        voos.add(voo);
        return voos;
    }

    private ValorTarifa valorTarifaMock(boolean comDu) {
        ValorTarifa valores = new ValorTarifa();
        valores.setTarifa(valorMock(BigDecimal.TEN, BigDecimal.TEN, BigDecimal.ZERO));
        if (comDu) {
            valores.setDu(BigDecimal.TEN);
        }
        return valores;
    }

    private Valor valorMock(BigDecimal valorNeto, BigDecimal valorFinal, BigDecimal valorDesconto) {
        Valor valor = new Valor();
        valor.setValorDesconto(valorDesconto);
        valor.setValorFinal(valorFinal);
        valor.setValorNeto(valorNeto);
        return valor;
    }

    /**
     * Testes Configuracao Fee
     */

    @Test
    public void testaCalculoFeeValorMinimoMaior() {

        when(flagFeatureService.isActive(NOVO_CALCULO_FEE)).thenReturn(true);

        final MarkupProduto markupProduto = MarkupProdutoBuilder.umMarkupProduto().comProduto(PRODUTO).doBuild();

        final ConfiguracaoPrecificacao
                configPrecificRestrita =
                ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comId(1).comTipoPrecificacao(RESTRITA).comTipoMarkup(BRUTO).comMarkup(markupProduto).doBuild();

        when(configuracaoPrecificacaoService.findPadraoByPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(configuracaoPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(Optional.of(configPrecificRestrita));

        ConfiguracaoFee confFee = ConfiguracaoFeeBuilder.umaConfiguracaoFee().comPercentual(FATOR_7_PORCENTO).comValorMinimo(BigDecimal.ONE).doBuild();

        when(configuracaoFeeService.filterConfiguracaoFeePorPrecificavel(anyList(), any(), any(), any())).thenReturn(Optional.of(confFee));

        Precificavel precificavel = precificavelMock(false);
        ResultadoPrecificacao resultadoPrecificacao = precificadorService.precifica(Collections.singletonList(precificavel), false, false, new ParametroBuscaAerea(), false).get(0);

        BigDecimal fee = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.FEE);
        BigDecimal totalSobretaxa = resultadoPrecificacao.getTotalSobretaxas(ADT.name());
        assertTrue(BigDecimal.ONE.setScale(2, RoundingMode.HALF_EVEN).compareTo(totalSobretaxa) == 0);
        assertTrue(BigDecimal.ONE.setScale(2, RoundingMode.HALF_EVEN).compareTo(fee) == 0);
    }

    @Test
    public void testaCalculoFee() {

        when(flagFeatureService.isActive(NOVO_CALCULO_FEE)).thenReturn(true);

        final MarkupProduto markupProduto = MarkupProdutoBuilder.umMarkupProduto().comProduto(PRODUTO).doBuild();

        final ConfiguracaoPrecificacao
                configPrecificRestrita =
                ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comId(1).comTipoMarkup(BRUTO).comTipoPrecificacao(RESTRITA).comMarkup(markupProduto).doBuild();

        when(configuracaoPrecificacaoService.findPadraoByPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(configuracaoPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(Optional.of(configPrecificRestrita));

        ConfiguracaoFee confFee = ConfiguracaoFeeBuilder.umaConfiguracaoFee().comPercentual(FATOR_7_PORCENTO).comValorMinimo(new BigDecimal("0.2")).doBuild();
        when(configuracaoFeeService.filterConfiguracaoFeePorPrecificavel(anyList(), any(), any(), any())).thenReturn(Optional.of(confFee));

        Precificavel precificavel = precificavelMock(false);

        List<ResultadoPrecificacao> resultadosPrecificacoes = precificadorService.precifica(Collections.singletonList(precificavel), Boolean.FALSE, false, new ParametroBuscaAerea(), false);

        BigDecimal du = resultadosPrecificacoes.get(0).getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.DU);
        BigDecimal fee = resultadosPrecificacoes.get(0).getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.FEE);
        BigDecimal totalSobretaxa = resultadosPrecificacoes.get(0).getTotalSobretaxas(ADT.name());
        assertTrue(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_EVEN).compareTo(du) == 0);
        assertTrue(new BigDecimal("0.70").setScale(2, RoundingMode.HALF_EVEN).compareTo(totalSobretaxa) == 0);
        assertTrue(new BigDecimal("0.70").compareTo(fee) == 0);
    }

    @Test
    public void testaCalculoFeeRecomendacaoAerea() {

        when(flagFeatureService.isActive(NOVO_CALCULO_FEE)).thenReturn(true);

        final MarkupProduto markupProduto = MarkupProdutoBuilder.umMarkupProduto().comProduto(PRODUTO).doBuild();

        final ConfiguracaoPrecificacao
                configPrecificRestrita =
                ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comId(1).comTipoMarkup(BRUTO).comTipoPrecificacao(RESTRITA).comMarkup(markupProduto).doBuild();

        when(configuracaoPrecificacaoService.findPadraoByPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any())).thenReturn(new ArrayList<>());
        when(configuracaoPrecificacaoService.obterConfigPrecificacaoRestritaPorPrecificavel(Mockito.anyList(), Mockito.any(), Mockito.any(), Mockito.anyBoolean())).thenReturn(Optional.of(configPrecificRestrita));

        ConfiguracaoFee confFee = ConfiguracaoFeeBuilder.umaConfiguracaoFee().comPercentual(FATOR_7_PORCENTO).comValorMinimo(new BigDecimal("0.2")).doBuild();
        when(configuracaoFeeService.filterConfiguracaoFeePorPrecificavel(anyList(), any(), any(), any())).thenReturn(Optional.of(confFee));

        Precificavel precificavel = precificavelMock(false);

        ResultadoPrecificacao resultadoPrecificacao = precificadorService.precifica(Collections.singletonList(precificavel), false, false, new ParametroBuscaAerea(), false).get(0);

        BigDecimal fee = resultadoPrecificacao.getValorTarifaById(ADT.name()).get().getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.FEE);
        BigDecimal totalSobretaxa = resultadoPrecificacao.getTotalSobretaxas(ADT.name());
        assertTrue(new BigDecimal("0.70").setScale(2, RoundingMode.HALF_EVEN).compareTo(totalSobretaxa) == 0);
        assertTrue(new BigDecimal("0.70").compareTo(fee) == 0);
    }

    @Test
    public void testaCalculoTaxaBolsaADT() {
        Cambio cambio = CambioBuilder.umCambio().comValor(new BigDecimal(1)).doBuild();
        Produto produto = ProdutoBuilder.umProduto().comNome(PRODUTO_PRECIFICACAO).doBuild();
        MarkupProduto markupProduto = MarkupProdutoBuilder.umMarkupProduto().comTaxaBolsa(new BigDecimal(100)).comProduto(produto).doBuild();
        List<ConfiguracaoPrecificacao> configs = Collections.singletonList(ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comMarkup(MARKUP_PRODUTO_TAXA_BOLSA).doBuild());
        String produtoPrecificacao = PRODUTO_PRECIFICACAO;
        Valor fare = new Valor(new BigDecimal(1000),new BigDecimal(1000), new BigDecimal(1000));
        ValorTarifa valorTarifa = ValorTarifaBuilder.umValorTarifa().comTarifa(fare).doBuild();
        Map<TipoValor, BigDecimal> result = retriever.calculaRC(cambio,configs,produtoPrecificacao,valorTarifa,ADT);
        assertTrue(new BigDecimal(100).compareTo(result.get(TAXA_BOLSA)) == 0);
    }
    @Test
    public void testaCalculoTaxaBolsaADTComCambio() {
        Cambio cambio = CambioBuilder.umCambio().comValor(new BigDecimal(5)).doBuild();
        List<ConfiguracaoPrecificacao> configs = Collections.singletonList(ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comMarkup(MARKUP_PRODUTO_TAXA_BOLSA_MOEDA_USD).doBuild());
        String produtoPrecificacao = PRODUTO_PRECIFICACAO;
        Valor fare = new Valor(new BigDecimal(1000),new BigDecimal(1000), new BigDecimal(1000));
        ValorTarifa valorTarifa = ValorTarifaBuilder.umValorTarifa().comTarifa(fare).doBuild();
        Map<TipoValor, BigDecimal> result = retriever.calculaRC(cambio,configs,produtoPrecificacao,valorTarifa,ADT);
        assertTrue(new BigDecimal(500).compareTo(result.get(TAXA_BOLSA)) == 0);
    }
    @Test
    public void testaCalculoTaxaBolsaPercADT() {
        Cambio cambio = CambioBuilder.umCambio().comValor(new BigDecimal(1)).doBuild();
        List<ConfiguracaoPrecificacao> configs = Collections.singletonList(ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comMarkup(MARKUP_PRODUTO_TAXA_BOLSA_PERC).doBuild());
        String produtoPrecificacao = PRODUTO_PRECIFICACAO;
        Valor fare = new Valor(new BigDecimal(1000),new BigDecimal(1000), new BigDecimal(1000));
        ValorTarifa valorTarifa = ValorTarifaBuilder.umValorTarifa().comTarifa(fare).doBuild();
        Map<TipoValor, BigDecimal> result = retriever.calculaRC(cambio,configs,produtoPrecificacao,valorTarifa,ADT);
        assertTrue(new BigDecimal(100.00).compareTo(result.get(TAXA_BOLSA)) == 0);
    }

    @Test
    public void testaCalculoTaxaBolsaCHD() {
        Cambio cambio = CambioBuilder.umCambio().comValor(new BigDecimal(1)).doBuild();
        List<ConfiguracaoPrecificacao> configs = Collections.singletonList(ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comMarkup(MARKUP_PRODUTO_TAXA_BOLSA).doBuild());
        String produtoPrecificacao = PRODUTO_PRECIFICACAO;
        Valor fare = new Valor(new BigDecimal(1000),new BigDecimal(1000), new BigDecimal(1000));
        ValorTarifa valorTarifa = ValorTarifaBuilder.umValorTarifa().comTarifa(fare).doBuild();
        Map<TipoValor, BigDecimal> result = retriever.calculaRC(cambio,configs,produtoPrecificacao,valorTarifa,CHD);
        assertTrue(new BigDecimal(200.00).compareTo(result.get(TAXA_BOLSA)) == 0);
    }
    @Test
    public void testaCalculoTaxaBolsaCHDCambio() {
        Cambio cambio = CambioBuilder.umCambio().comValor(new BigDecimal(5)).doBuild();
        List<ConfiguracaoPrecificacao> configs = Collections.singletonList(ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comMarkup(MARKUP_PRODUTO_TAXA_BOLSA_MOEDA_USD).doBuild());
        String produtoPrecificacao = PRODUTO_PRECIFICACAO;
        Valor fare = new Valor(new BigDecimal(1000),new BigDecimal(1000), new BigDecimal(1000));
        ValorTarifa valorTarifa = ValorTarifaBuilder.umValorTarifa().comTarifa(fare).doBuild();
        Map<TipoValor, BigDecimal> result = retriever.calculaRC(cambio,configs,produtoPrecificacao,valorTarifa,CHD);
        assertTrue(new BigDecimal(1000.00).compareTo(result.get(TAXA_BOLSA)) == 0);
    }
    @Test
    public void testaCalculoTaxaBolsaPercCHD() {
        Cambio cambio = CambioBuilder.umCambio().comValor(new BigDecimal(1)).doBuild();
        List<ConfiguracaoPrecificacao> configs = Collections.singletonList(ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comMarkup(MARKUP_PRODUTO_TAXA_BOLSA_PERC).doBuild());
        String produtoPrecificacao = PRODUTO_PRECIFICACAO;
        Valor fare = new Valor(new BigDecimal(1000),new BigDecimal(1000), new BigDecimal(1000));
        ValorTarifa valorTarifa = ValorTarifaBuilder.umValorTarifa().comTarifa(fare).doBuild();
        Map<TipoValor, BigDecimal> result = retriever.calculaRC(cambio,configs,produtoPrecificacao,valorTarifa,CHD);
        assertTrue(new BigDecimal(200.00).compareTo(result.get(TAXA_BOLSA)) == 0);
    }
    @Test
    public void testaCalculoTaxaBolsaINF() {
        Cambio cambio = CambioBuilder.umCambio().comValor(new BigDecimal(1)).doBuild();
        List<ConfiguracaoPrecificacao> configs = Collections.singletonList(ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comMarkup(MARKUP_PRODUTO_TAXA_BOLSA).doBuild());
        String produtoPrecificacao = PRODUTO_PRECIFICACAO;
        Valor fare = new Valor(new BigDecimal(1000),new BigDecimal(1000), new BigDecimal(1000));
        ValorTarifa valorTarifa = ValorTarifaBuilder.umValorTarifa().comTarifa(fare).doBuild();
        Map<TipoValor, BigDecimal> result = retriever.calculaRC(cambio,configs,produtoPrecificacao,valorTarifa,INF);
        assertTrue(new BigDecimal(300.00).compareTo(result.get(TAXA_BOLSA)) == 0);
    }

    @Test
    public void testaCalculoTaxaBolsaPercINF() {
        Cambio cambio = CambioBuilder.umCambio().comValor(new BigDecimal(1)).doBuild();
        List<ConfiguracaoPrecificacao> configs = Collections.singletonList(ConfiguracaoPrecificacaoBuilder.umaConfigPrecificacao().comMarkup(MARKUP_PRODUTO_TAXA_BOLSA_PERC).doBuild());
        String produtoPrecificacao = PRODUTO_PRECIFICACAO;
        Valor fare = new Valor(new BigDecimal(1000),new BigDecimal(1000), new BigDecimal(1000));
        ValorTarifa valorTarifa = ValorTarifaBuilder.umValorTarifa().comTarifa(fare).doBuild();
        Map<TipoValor, BigDecimal> result = retriever.calculaRC(cambio,configs,produtoPrecificacao,valorTarifa,INF);
        assertTrue(new BigDecimal(300.00).compareTo(result.get(TAXA_BOLSA)) == 0);
    }
}

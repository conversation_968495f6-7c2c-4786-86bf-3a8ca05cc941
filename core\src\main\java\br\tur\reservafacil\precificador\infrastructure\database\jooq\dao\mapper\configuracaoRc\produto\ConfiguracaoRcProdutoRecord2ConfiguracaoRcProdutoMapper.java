package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.configuracaoRc.produto;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoRcProdutoRecord;
import br.tur.reservafacil.dominio.Produto;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcProduto;
import br.tur.reservafacil.dominio.tipo.Moeda;
import org.jooq.RecordMapper;
import org.springframework.stereotype.Component;

/**
 * Created by Gabriela on 20/08/25 - GA-12251.
 */
@Component
public class ConfiguracaoRcProdutoRecord2ConfiguracaoRcProdutoMapper
        implements RecordMapper<ConfiguracaoRcProdutoRecord, ConfiguracaoRcProduto> {

    @Override
    public ConfiguracaoRcProduto map(ConfiguracaoRcProdutoRecord record) {
        ConfiguracaoRcProduto configuracaoRcProduto = new ConfiguracaoRcProduto();
        configuracaoRcProduto.setId(record.getId());
        configuracaoRcProduto.setAtivo(record.getAtivo());
        configuracaoRcProduto.setCodigoRc(record.getCodigoRc());
        configuracaoRcProduto.setTaxaBolsa(record.getTaxaBolsa());
        configuracaoRcProduto.setTaxaBolsaPerc(record.getTaxaBolsaPerc());
        configuracaoRcProduto.setTaxaBolsaChd(record.getTaxaBolsaChd());
        configuracaoRcProduto.setTaxaBolsaPercChd(record.getTaxaBolsaPercChd());
        configuracaoRcProduto.setTaxaBolsaInf(record.getTaxaBolsaInf());
        configuracaoRcProduto.setTaxaBolsaPercInf(record.getTaxaBolsaPercInf());
        if (record.getMoedaRepasse() != null) {
            configuracaoRcProduto.setMoedaRepasse(Moeda.getByCodigo(record.getMoedaRepasse()));
        }

        Produto produto = new Produto();
        produto.setId(record.getProdutoId());
        configuracaoRcProduto.setProduto(produto);

        return configuracaoRcProduto;
    }

}

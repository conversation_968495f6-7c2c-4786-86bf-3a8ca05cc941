package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoRcProdutoRecord;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcProduto;
import br.tur.reservafacil.precificador.domain.repository.ConfiguracaoRcProdutoRepository;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.configuracaoRc.produto.ConfiguracaoRcProdutoEagerMapper;
import org.jooq.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.Tables.CONFIGURACAO_RC_PRODUTO;
import static br.com.reservafacil.precificador.infrastructure.database.jooq.generated.Tables.PRODUTO;

/**
 * Created by <PERSON><PERSON> on 20/08/25 - GA-12251.
 */
@Repository
public class ConfiguracaoRcProdutoJooqDao
		extends AbstractJooqDao<ConfiguracaoRcProdutoRecord, ConfiguracaoRcProduto, Integer>
		implements ConfiguracaoRcProdutoRepository {

    @Autowired
    private ConfiguracaoRcProdutoEagerMapper configuracaoRcProdutoEagerMapper;

    @Autowired
    public ConfiguracaoRcProdutoJooqDao(Configuration configuration, DSLContext context) {
	super(CONFIGURACAO_RC_PRODUTO, ConfiguracaoRcProduto.class, configuration, context);
    }

    @Override
    protected Integer getId(ConfiguracaoRcProduto configuracaoRcProduto) {
	return configuracaoRcProduto.getId();
    }

    @Override
    public void deleteByConfigPrecificacaoId(Integer idConfigPrecificacao) {
	this.getContext().deleteFrom(this.getTable()).where(CONFIGURACAO_RC_PRODUTO.CONFIGURACAO_RC_ID.eq(idConfigPrecificacao)).execute();
    }

    @Override
    public List<ConfiguracaoRcProduto> findByConfigRcId(Integer configuracaoId) {
	List<SelectField<?>> fields = new ArrayList<>();
	fields.addAll(Arrays.asList(CONFIGURACAO_RC_PRODUTO.fields()));
	fields.addAll(Arrays.asList(PRODUTO.fields()));
	Result<Record> result =
			this.getContext().select(fields).from(CONFIGURACAO_RC_PRODUTO).innerJoin(PRODUTO).on(
					CONFIGURACAO_RC_PRODUTO.PRODUTO_ID.eq(PRODUTO.ID)).where(
					CONFIGURACAO_RC_PRODUTO.CONFIGURACAO_RC_ID.eq(
							configuracaoId)).fetch();

	return configuracaoRcProdutoEagerMapper.map(result);
    }

}

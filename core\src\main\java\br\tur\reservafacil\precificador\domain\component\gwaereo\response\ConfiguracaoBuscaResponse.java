package br.tur.reservafacil.precificador.domain.component.gwaereo.response;

import java.io.Serializable;
import java.util.Objects;

/**
 * Created by ramon on 14/12/16.
 */
public class ConfiguracaoBuscaResponse implements Serializable {

    private Integer id;
    private String nome;
    private Boolean bestPriceEnabled;

    public ConfiguracaoBuscaResponse() {
        this.id = -1;
    }

    public ConfiguracaoBuscaResponse(boolean bestPriceEnabled) {
	this.bestPriceEnabled = bestPriceEnabled;
    }

    public Integer getId() {
	return id;
    }

    public void setId(Integer id) {
	this.id = id;
    }

    public String getNome() {
	return nome;
    }

    public void setNome(String nome) {
	this.nome = nome;
    }

    public Boolean getBestPriceEnabled() {
	return bestPriceEnabled;
    }

    public void setBestPriceEnabled(Boolean bestPriceEnabled) {
	this.bestPriceEnabled = bestPriceEnabled;
    }

    @Override
    public boolean equals(Object o) {
	if (this == o)
	    return true;
	if (o == null || getClass() != o.getClass())
	    return false;
	ConfiguracaoBuscaResponse that = (ConfiguracaoBuscaResponse)o;
	return Objects.equals(id, that.id) && Objects.equals(nome, that.nome);
    }

    @Override
    public int hashCode() {
	return Objects.hash(id, nome);
    }
}

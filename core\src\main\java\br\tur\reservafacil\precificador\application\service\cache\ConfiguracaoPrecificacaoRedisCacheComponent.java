package br.tur.reservafacil.precificador.application.service.cache;

import br.tur.reservafacil.dominio.aereo.ConfiguracaoPrecificacao;
import br.tur.reservafacil.dominio.tipo.TipoConfigPrecificacao;
import br.tur.reservafacil.precificador.domain.repository.ConfiguracaoPrecificacaoRepository;
import br.tur.reservafacil.precificador.infrastructure.performance.TimeAudit;
import com.newrelic.api.agent.Trace;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by fepelichero on 08/11/2018.
 */
@Component
public class ConfiguracaoPrecificacaoRedisCacheComponent {


    @Autowired
    private ConfiguracaoPrecificacaoRepository repository;

    @Trace
    @Cacheable(cacheNames = "ConfiguracaoPrecificacaoService::findByIds", cacheManager = "redisCacheManager")
    public List<ConfiguracaoPrecificacao> findByIds(List<Integer> keys) {
        return repository.findEagerByKeys(keys);
    }

    @Trace
    @Cacheable(cacheNames = "ConfiguracaoPrecificacaoService::findByKey", cacheManager = "redisCacheManager")
    public ConfiguracaoPrecificacao findByKey(Integer key) {
        return repository.findEaggerByKey(key);
    }

    @Trace
    @Cacheable(cacheNames = "ConfiguracaoPrecificacaoService::findByCiaTipoConfigEmpresaIdProduto", cacheManager = "redisCacheManager")
    @TimeAudit
    public List<ConfiguracaoPrecificacao> findByCiaTipoConfigEmpresaIdProduto(String codCia, TipoConfigPrecificacao tipoConfigPrecificacao,
                                                                              Integer empresaId, String produto, Boolean isBuscaDeTeste, String timeToCache, Boolean markupCiaFirst) {
        return repository.buscaSoComRestricaoByCiaTipoConfigEmpresaIdProduto(codCia, tipoConfigPrecificacao, empresaId, produto, isBuscaDeTeste, markupCiaFirst);
    }

    @Trace
    @Cacheable(cacheNames = "ConfiguracaoPrecificacaoService::findByCiaTipoConfigEmpresaIdProdutoBPRICEMarkupLimite", cacheManager = "redisCacheManager")
    @TimeAudit
    public List<ConfiguracaoPrecificacao> findByCiaTipoConfigEmpresaIdProdutoBPRICEMarkupLimite(String codCia, TipoConfigPrecificacao tipoConfigPrecificacao, Integer empresaId, String produto) {
        return repository.buscaSoComRestricaoByCiaTipoConfigEmpresaIdProdutoBPRICEMarkupLimite(codCia, tipoConfigPrecificacao, empresaId, produto);
    }

}

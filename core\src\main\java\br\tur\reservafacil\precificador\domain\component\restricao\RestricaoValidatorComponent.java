package br.tur.reservafacil.precificador.domain.component.restricao;

import br.tur.reservafacil.dominio.aereo.Restricao;
import br.tur.reservafacil.dominio.aereo.TipoAgrupamento;
import br.tur.reservafacil.dominio.aereo.voosExcluidos.MotivoExclusaoVoo;
import br.tur.reservafacil.dominio.aereo.voosExcluidos.RestricaoBarrada;
import br.tur.reservafacil.dominio.tipo.TipoOperador;
import br.tur.reservafacil.dominio.tipo.TipoRestricao;
import br.tur.reservafacil.dominio.tipo.TipoValorRestricao;
import br.tur.reservafacil.precificador.domain.strategy.restricao.exceptions.FormatoDoValorInvalido;
import br.tur.reservafacil.precificador.domain.strategy.restricao.operacao.*;
import com.newrelic.api.agent.Trace;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static br.tur.reservafacil.dominio.aereo.TipoAgrupamento.*;
import static org.apache.commons.lang3.builder.ToStringBuilder.reflectionToString;

/**
 * Created by davidson on 5/16/16.
 */
@Component public class RestricaoValidatorComponent {

    private final RestricaoTreeComponent restricaoTreeComponent;

    @Autowired public RestricaoValidatorComponent(RestricaoTreeComponent restricaoTreeComponent) {
        this.restricaoTreeComponent = restricaoTreeComponent;
    }

    private static final Logger                              LOG             = LoggerFactory.getLogger(RestricaoValidatorComponent.class);
    private              OperacaoFactory<AssinaturaOperacao> operadorFactory = new OperacaoFactoryDefaultImpl();

    public boolean validaRestricoes(List<Restricao> restricoesRec, List<Restricao> restricaoDeConfiguracao) {
        final boolean processaAteOFim = false;
        //Se não existe motivo de exclusão de voo a configuração é valida para precificavel.
        return this.validaRestricoes(restricoesRec, restricaoDeConfiguracao, processaAteOFim) == null;
    }

    /**
     * @param restricoesRec           uma lista de restrições a filtrarem a configuracao correta
     * @param restricaoDeConfiguracao a lista de restrições que compõe a configuração (seja ela de busca ou precificação)
     * @param processaAteOFim         <code>true</code> caso deva processar até o fim capturando todos os motivos de exclução de voo, <code>false<code/> caso contrário.
     * @return null caso todas as restrições de busca satisfação todas as restrições de filtragem, caso contrário devolve um {@link MotivoExclusaoVoo} com as informações do porque o voo será excluído.
     */
    @Trace
    public MotivoExclusaoVoo validaRestricoes(List<Restricao> restricoesRec, List<Restricao> restricaoDeConfiguracao, boolean processaAteOFim) {

        List<Restricao> arvoreDeRestricoes = restricaoTreeComponent.gerarArvoreDeRestricoes(restricaoDeConfiguracao);

        MotivoExclusaoVoo motivoExclusaoVooAgrupado = new MotivoExclusaoVoo();

        boolean isValid = true;

        for (Restricao restricao : arvoreDeRestricoes) {
            final boolean valorInverso = restricao.getTipoAgrupamento() != null && restricao.getTipoAgrupamento().isValorInverso();
            boolean restricaoValida = this.validar(restricao, restricoesRec, motivoExclusaoVooAgrupado, valorInverso, null);
            isValid = isValid && restricaoValida;

            if (!isValid) {
                LOG.debug("Uma ou mais restrições de busca não satisfazem as restrições de filtragem de busca!");
                if (!processaAteOFim) {
                    return motivoExclusaoVooAgrupado;
                }
            }
        }

        if (!isValid) {
            return motivoExclusaoVooAgrupado;
        }
        return null;
    }

    private boolean validar(Restricao restricao, List<Restricao> restricoesAValidar, final MotivoExclusaoVoo motivo,
            boolean inverterValor, TipoAgrupamento tipoAgrupamentoPai) {
        return validar(restricao, restricoesAValidar, motivo, inverterValor, false, tipoAgrupamentoPai);
    }

    private boolean validar(Restricao restricao, List<Restricao> restricoesAValidar, final MotivoExclusaoVoo motivo,
            boolean inverterValor, boolean naoContidoEm, TipoAgrupamento tipoAgrupamentoPai) {
        boolean isValid = false;

        if (CollectionUtils.isNotEmpty(restricao.getRestricoes())) {
            TipoRestricao tipoRestricao;
            TipoAgrupamento tipoAgrupamento = restricao.getTipoAgrupamento();

            // Validador especifico para restricoes agrupadas com operador DIFERENTE
            if (restricao.getTipoOperador() == TipoOperador.DIFERENTE) {
                List<String> valoresConfigurados = restricao.getRestricoes().stream().map(Restricao::getValor).collect(Collectors.toList());
                List<Restricao> restricoesRecomendacao = restricoesAValidar.stream().filter(restricaoRecomendacao -> restricaoRecomendacao.getTipoRestricao() == restricao.getRestricoes().get(0).getTipoRestricao()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(restricoesRecomendacao) && restricoesRecomendacao.size() == 1) {
                    boolean valida = true;
                    if (restricao.getTipoAgrupamento() == AND || restricao.getTipoAgrupamento() == CONTIDO_EM || restricao.getTipoAgrupamento() == null) {
                        valida = valoresConfigurados.stream().noneMatch(valor -> valor.equalsIgnoreCase(restricoesRecomendacao.get(0).getValor()));
                    } else if (restricao.getTipoAgrupamento() == OR) {
                        valida = valoresConfigurados.stream().anyMatch(valor -> !valor.equalsIgnoreCase(restricoesRecomendacao.get(0).getValor()));
                    } else if (restricao.getTipoAgrupamento() == NAND) {
                        if (CollectionUtils.isNotEmpty(restricao.getRestricoes()) && restricao.getRestricoes().get(0).getTipoOperador() == TipoOperador.CONTEM) {
                            valida = !StringUtils.containsIgnoreCase(restricoesRecomendacao.get(0).getValor(), valoresConfigurados.get(0));
                        } else {
                            valida = valoresConfigurados.stream().allMatch(valor -> !valor.equalsIgnoreCase(restricoesRecomendacao.get(0).getValor()));
                        }
                    }
                    if(!valida){
                        motivo.addRestricaoBarrada(restricao.getRestricoes().stream().map(Restricao::getValor).collect(Collectors.joining(",")) , restricao.getRestricoes().stream().map(Restricao::getTipoRestricao).collect(Collectors.toList()).get(0));
                    }
                    return valida;
                }
            }

            try{
                switch (tipoAgrupamento) {
                    case OR:
                        isValid = restricao.getRestricoes().stream().anyMatch(r -> this.validar(r, restricoesAValidar, motivo, inverterValor, tipoAgrupamento));
                        break;
                    case AND:
                        isValid = restricao.getRestricoes().stream().allMatch(r -> this.validar(r, restricoesAValidar, motivo, inverterValor, tipoAgrupamento));
                        break;
                    case NAND:
                        boolean valida;
                        List<String> valoresConfigurados = restricao.getRestricoes().stream().map(Restricao::getValor).collect(Collectors.toList());
                        List<Restricao> restricoesRecomendacao = restricoesAValidar.stream().filter(restricaoRecomendacao -> restricaoRecomendacao.getTipoRestricao() == restricao.getRestricoes().get(0).getTipoRestricao()).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(restricoesRecomendacao) && restricoesRecomendacao.size() == 1) {
                            if (restricao.getTipoOperador() == TipoOperador.IGUAL) {
                                if (restricoesRecomendacao.get(0).getTipoRestricao().getTipoValor() == TipoValorRestricao.TEXTO &&
                                        restricao.getRestricoes().get(0).getTipoOperador() == TipoOperador.IGUAL) {
                                    valida = valoresConfigurados.stream().noneMatch(valor -> valor.equalsIgnoreCase(restricoesRecomendacao.get(0).getValor()));
                                } else if (restricoesRecomendacao.get(0).getTipoRestricao().getTipoValor() == TipoValorRestricao.PERIODO) {
                                    valida = restricao.getRestricoes().stream().noneMatch(rest -> {
                                        try {
                                            return validaRestricaoUnitaria(restricoesRecomendacao.get(0), rest, rest.getTipoOperador());
                                        } catch (FormatoDoValorInvalido e) {
                                            LOG.error(e.getMessage());
                                            return !restricao.getRestricoes().stream().allMatch(r -> this.validar(r, restricoesAValidar, motivo, inverterValor, tipoAgrupamento));
                                        }
                                    });
                                } else {
                                    valida = !restricao.getRestricoes().stream().allMatch(r -> this.validar(r, restricoesAValidar, motivo, inverterValor, tipoAgrupamento));
                                }
                            } else if (restricao.getTipoOperador() == TipoOperador.DIFERENTE) {
                                valida = valoresConfigurados.stream().allMatch(valor -> !valor.equalsIgnoreCase(restricoesRecomendacao.get(0).getValor()));
                            } else {
                                valida = restricao.getRestricoes().stream().noneMatch(r -> this.validar(r, restricoesAValidar, motivo, inverterValor, tipoAgrupamento));
                            }
                        } else {
                            valida = !restricao.getRestricoes().stream().allMatch(r -> this.validar(r, restricoesAValidar, motivo, inverterValor, tipoAgrupamento));
                        }

                        if(!valida){
                            motivo.addRestricaoBarrada(restricao.getRestricoes().stream().map(Restricao::getValor).collect(Collectors.joining(",")) , restricao.getRestricoes().stream().map(Restricao::getTipoRestricao).collect(Collectors.toList()).get(0));
                        }

                        isValid = valida;
                        break;
                    case CONTIDO_EM:
                    case NAO_CONTIDO_EM:
                        isValid = validarConjunto(restricao, restricoesAValidar, motivo, inverterValor, tipoAgrupamento);
                        break;
                    case UNDEF:
                        LOG.error("Tipo de agrupamento não foi implementado");
                        isValid = false;
                        break;
                }

                if(isValid){
                    List<RestricaoBarrada> restricaoBarradas = motivo.getRestricoesBarradas().stream().filter(m -> !m.getTipoRestricao().equals(restricao.getRestricoes().get(0).getTipoRestricao())).collect(Collectors.toList());
                    motivo.setRestricoesBarradas(restricaoBarradas);
                }
            }catch (Exception ex){
                RestricaoBarrada restricaoBarrada = new RestricaoBarrada();
                String causa = ex.getMessage();
                if (causa == null) {
                    causa = ex.toString();
                }
                if (ArrayUtils.isNotEmpty(ex.getStackTrace())) {
                    causa = causa + " - stackTrace: " + ex.getStackTrace()[0].toString();
                }
                restricaoBarrada.setValor(causa);
                motivo.setRestricoesBarradas(Collections.singletonList(restricaoBarrada));
                LOG.error(ex.getMessage());
                return false;
            }

            return isValid;
        }

        TipoRestricao tipoRestricao = restricao.getTipoRestricao();
        TipoOperador tipoOperador = restricao.getTipoOperador();
        List<Restricao> restricoesFiltradas = restricoesAValidar.stream().filter(r -> r.getTipoRestricao() == tipoRestricao).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(restricoesFiltradas)) {
            if (tipoOperador.isEmptyAllowed() || tipoRestricao == TipoRestricao.AGRUPAMENTO) {
                return true;
            }
            motivo.addRestricaoNaoGerada(tipoRestricao);
            return false;
        }
        Predicate<Restricao> restricaoPredicate = getRestricaoPredicate(restricoesFiltradas, restricao, tipoOperador, motivo, inverterValor, naoContidoEm, tipoAgrupamentoPai);
        return restricaoPredicate.test(restricao);
    }

    private boolean validarConjunto(Restricao restricao, List<Restricao> restricoesAValidar, MotivoExclusaoVoo motivo, boolean inverterValor, TipoAgrupamento tipoAgrupamento) {
        TipoRestricao tipoRestricao;
        boolean tipoNaoContidoEm = tipoAgrupamento == NAO_CONTIDO_EM;
        tipoRestricao = restricao.getRestricoes().get(0).getTipoRestricao();
        List<Restricao> filteredRestrictions = restricoesAValidar.stream()
                                                                 .filter(r -> r.getTipoRestricao() == tipoRestricao).collect(Collectors.toList());

        if (filteredRestrictions.isEmpty() && !tipoNaoContidoEm) {
            motivo.addRestricaoNaoGerada(tipoRestricao);
            return false;
        }

        final TipoOperador operador = restricao.getRestricoes().get(0).getTipoOperador();
        boolean valido = filteredRestrictions.stream()
                                             .map(restricaoStream -> new Restricao(restricaoStream.getValor(), operador,restricaoStream.getTipoRestricao()))
                                             .allMatch(r -> this.validar(r, restricao.getRestricoes(), motivo, inverterValor, tipoNaoContidoEm, tipoAgrupamento));
        if (tipoNaoContidoEm) {
            return !valido;
        }
        return valido;
    }

    private Predicate<Restricao> getRestricaoPredicate(List<Restricao> rests, Restricao restricaoRecomendacao, TipoOperador tipoOperador,
            MotivoExclusaoVoo motivoExclusaoVoo, boolean inverterValor, boolean naoContidoEm,
            TipoAgrupamento tipoAgrupamentoPai) {
        if (TipoAgrupamento.AND.equals(tipoAgrupamentoPai) &&
                (TipoOperador.TERMINA_COM.equals(restricaoRecomendacao.getTipoOperador()) ||
                        TipoOperador.DENTRO.equals(restricaoRecomendacao.getTipoOperador()) ||
                        TipoOperador.FORA.equals(restricaoRecomendacao.getTipoOperador()) ||
                        TipoOperador.COMECA_COM.equals(restricaoRecomendacao.getTipoOperador()))) {
            return restricao -> rests.stream().allMatch(restricaoStream -> {
                try {
                    boolean valida = this.validaRestricaoUnitaria(restricaoStream, restricao, tipoOperador);
                    if (!valida || naoContidoEm) {
                        motivoExclusaoVoo.addRestricaoBarrada(inverterValor ? restricaoRecomendacao.getValor() : restricaoStream.getValor(),
                                restricao.getTipoRestricao());
                    }
                    if (valida) {
                        if (LOG.isDebugEnabled()) {
                            LOG.debug("A restricao de busca: " + reflectionToString(restricaoStream) + " é compatível com a restrição: " + reflectionToString(restricao));
                        }
                        return true;
                    }
                    return false;
                } catch (FormatoDoValorInvalido e) {
                    if (LOG.isDebugEnabled()) {
                        LOG.error("A aplicação da restricao de busca: "
                                + reflectionToString(restricaoStream)
                                + " com a restricao "
                                + reflectionToString(restricao)
                                + " resultou em erro, pois um ocorreu um erro na conversão de um valor.", e);
                    }
                    return false;
                }
            });
        } else {
            return restricao -> rests.stream().anyMatch(restricaoStream -> {
                try {
                    boolean valida = this.validaRestricaoUnitaria(restricaoStream, restricao, tipoOperador);
                    if (!valida || naoContidoEm) {
                        motivoExclusaoVoo.addRestricaoBarrada(inverterValor ? restricaoRecomendacao.getValor() : restricaoStream.getValor(),
                                restricao.getTipoRestricao());
                    }
                    if (valida) {
                        if (LOG.isDebugEnabled()) {
                            LOG.debug("A restricao de busca: " + reflectionToString(restricaoStream) + " é compatível com a restrição: " + reflectionToString(restricao));
                        }
                        return true;
                    }
                    return false;
                } catch (FormatoDoValorInvalido e) {
                    //TODO: Verificar se o tratamento é apenas esse mesmo.
                    if (LOG.isDebugEnabled()) {
                        LOG.error("A aplicação da restricao de busca: "
                                + reflectionToString(restricaoStream)
                                + " com a restricao "
                                + reflectionToString(restricao)
                                + " resultou em erro, pois um ocorreu um erro na conversão de um valor.", e);
                    }
                    return false;
                }
            });
        }
    }

    @Trace
    public boolean validaRestricaoUnitaria(Restricao restricaoA, Restricao restricaoB, TipoOperador tipoOperador)
            throws FormatoDoValorInvalido {
        TipoValorRestricao tipoValor = restricaoB.getTipoRestricao().getTipoValor();
        Operacao operacao = operadorFactory.getOperacao(new AssinaturaOperacao(tipoOperador, tipoValor));
        if (restricaoA == null || restricaoA.getValor() == null) {
            return operacao instanceof OperacaoNuloVerdadeiro;
        }
        return operacao.aplique(restricaoA.getValor(), restricaoB.getValor());
    }
}

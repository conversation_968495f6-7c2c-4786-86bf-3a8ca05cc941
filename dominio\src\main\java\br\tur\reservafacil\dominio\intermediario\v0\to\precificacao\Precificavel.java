package br.tur.reservafacil.dominio.intermediario.v0.to.precificacao;

import br.tur.reservafacil.dominio.aereo.Aeroporto;
import br.tur.reservafacil.dominio.aereo.CiaAerea;
import br.tur.reservafacil.dominio.aereo.tipo.TipoViagem;
import br.tur.reservafacil.dominio.common.ValorTarifa;
import br.tur.reservafacil.dominio.tipo.FaixaEtaria;
import br.tur.reservafacil.dominio.tipo.SistEmis;
import br.tur.reservafacil.dominio.tipo.TipoTarifaAcordo;
import br.tur.reservafacil.dominio.tipo.TipoValor;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by ramon on 11/04/17.
 */
public class Precificavel implements Serializable {

    private String idAgrupamento;
    private String idUnidadePrecificavel;

    //precisamos da credencialId e pagamentoCartao para decidirmos se a chamada de precificação precisa aplicar obfee ou nao.
    private Integer credencialId;
    private Boolean pagamentoCartao;

    private List<Segmento>   segmentos;
    private TipoTarifaAcordo tipoTarifaAcordo;
    private String           codigoContrato;
    private Boolean          contratoProprio = Boolean.FALSE;
    private String           produtoPrecificacao;
    private List<Tarifacao>  tarifacoes;
    private String           ciaAereaViagem;
    private String           officeIdReserva;
    private String           officeIdEmissao;
    private Boolean          isSurface;
    private List<Integer>    precificacaoIds;
    private Integer          idConfigSucessor;
    private Boolean          isNacional;
    private SistEmis         sistEmis;
    private LocalDateTime    dataCriacao;
    private TipoPrecificavel tipoPrecificavel;
    private Boolean          reservaGrupo = Boolean.FALSE;
    private Integer          numeroTrechos;
    private String           depuracaoHash;
    private String           excludedByFilter;
    private BigDecimal 	     invoicedEntryAmount = BigDecimal.ZERO;
    private         boolean importedBooking;
    @Getter private Integer idConfigRestrita;
    @Getter private String  condicaoPrecificacao;
    @Getter private Integer configuracaoRavId;
    @Getter private Integer configuracaoFeeId;
    @Getter @Setter private List<String> formasPagamento;
    @Getter @Setter private LocalDateTime dataCriacaoReserva;
    @Getter @Setter private TipoViagem tipoViagem;

    public Integer getCredencialId() {
        return credencialId;
    }

    public void setCredencialId(Integer credencialId) {
        this.credencialId = credencialId;
    }

    public Boolean getPagamentoCartao() {
        return pagamentoCartao;
    }

    public void setPagamentoCartao(Boolean pagamentoCartao) {
        this.pagamentoCartao = pagamentoCartao;
    }

    public String getIdAgrupamento() {
        return idAgrupamento;
    }

    public void setIdAgrupamento(String idAgrupamento) {
        this.idAgrupamento = idAgrupamento;
    }

    public String getIdUnidadePrecificavel() {
        return idUnidadePrecificavel;
    }

    public void setIdUnidadePrecificavel(String idUnidadePrecificavel) {
        this.idUnidadePrecificavel = idUnidadePrecificavel;
    }

    public List<Segmento> getSegmentos() {
        if (segmentos == null) {
            segmentos = new ArrayList<>();
        }
        return segmentos;
    }

    public void setSegmentos(List<Segmento> segmentos) {
        this.segmentos = segmentos;
    }

    public TipoTarifaAcordo getTipoTarifaAcordo() {
        return tipoTarifaAcordo;
    }

    public void setTipoTarifaAcordo(TipoTarifaAcordo tipoTarifaAcordo) {
        this.tipoTarifaAcordo = tipoTarifaAcordo;
    }

    public String getCodigoContrato() {
        return codigoContrato;
    }

    public void setCodigoContrato(String codigoContrato) {
        this.codigoContrato = codigoContrato;
    }

    public Boolean getContratoProprio() {
        return contratoProprio;
    }

    public void setContratoProprio(Boolean contratoProprio) {
        this.contratoProprio = contratoProprio;
    }

    public String getProdutoPrecificacao() {
        return produtoPrecificacao;
    }

    public void setProdutoPrecificacao(String produtoPrecificacao) {
        this.produtoPrecificacao = produtoPrecificacao;
    }

    public List<Tarifacao> getTarifacoes() {
        if (tarifacoes == null) {
            tarifacoes = new ArrayList<>();
        }

        return tarifacoes;
    }

    public void setTarifacoes(List<Tarifacao> tarifacoes) {
        this.tarifacoes = tarifacoes;
    }

    public String getCiaAereaViagem() {
        return ciaAereaViagem;
    }

    public void setCiaAereaViagem(String ciaAereaViagem) {
        this.ciaAereaViagem = ciaAereaViagem;
    }

    public String getOfficeIdReserva() {
        return officeIdReserva;
    }

    public void setOfficeIdReserva(String officeIdReserva) {
        this.officeIdReserva = officeIdReserva;
    }

    public String getOfficeIdEmissao() {
        return officeIdEmissao;
    }

    public void setOfficeIdEmissao(String officeIdEmissao) {
        this.officeIdEmissao = officeIdEmissao;
    }

    public Boolean getSurface() {
        return isSurface;
    }

    public void setSurface(Boolean surface) {
        isSurface = surface;
    }

    public List<Integer> getPrecificacaoIds() {
        return precificacaoIds;
    }

    public void setPrecificacaoIds(List<Integer> precificacaoIds) {
        this.precificacaoIds = precificacaoIds;
    }

    public Integer getIdConfigSucessor() {
        return idConfigSucessor;
    }

    public void setIdConfigSucessor(Integer idConfigSucessor) {
        this.idConfigSucessor = idConfigSucessor;
    }

    public Boolean isNacional() {
        return isNacional;
    }

    public void setNacional(Boolean nacional) {
        isNacional = nacional;
    }

    public SistEmis getSistEmis() {
        return sistEmis == null ? SistEmis.UNDEF : sistEmis;
    }

    public void setSistEmis(SistEmis sistEmis) {
        this.sistEmis = sistEmis;
    }

    public LocalDateTime getDataCriacao() {
        return dataCriacao;
    }

    public void setDataCriacao(LocalDateTime dataCriacao) {
        this.dataCriacao = dataCriacao;
    }

    public TipoPrecificavel getTipoPrecificavel() {
        return tipoPrecificavel;
    }

    public void setTipoPrecificavel(TipoPrecificavel tipoPrecificavel) {
        this.tipoPrecificavel = tipoPrecificavel;
    }

    public Integer  getNumeroTrechos() {
        return Optional.ofNullable(numeroTrechos).orElse(Optional.ofNullable(this.segmentos).map(List::size).orElse(0));
    }

    public String getDepuracaoHash() {
        return depuracaoHash;
    }

    public void setDepuracaoHash(String depuracaoHash) {
        this.depuracaoHash = depuracaoHash;
    }

    public void setNumeroTrechos(Integer numeroTrechos) {
        this.numeroTrechos = numeroTrechos;
    }

    public String getExcludedByFilter() {
        return excludedByFilter;
    }

    public void setExcludedByFilter(String excludedByFilter) {
        this.excludedByFilter = excludedByFilter;
    }

    public boolean isExcludedByFilter() {
        return StringUtils.isNotEmpty(this.excludedByFilter);
    }

    public BigDecimal getInvoicedEntryAmount() {
	return invoicedEntryAmount;
    }

    public void setInvoicedEntryAmount(BigDecimal invoicedEntryAmount) {
	this.invoicedEntryAmount = invoicedEntryAmount;
    }

    public boolean isImportedBooking() {
	return importedBooking;
    }

    public void setImportedBooking(boolean importedBooking) {
	this.importedBooking = importedBooking;
    }

    @JsonIgnore
    public boolean isBusca() {
        return tipoPrecificavel != null && tipoPrecificavel.isBusca();
    }

    @JsonIgnore
    public boolean isImportacao() {
	return importedBooking;
    }

    @JsonIgnore
    public List<FaixaEtaria> getFaixasEtarias() {
        return this.tarifacoes.stream().map(Tarifacao::getFaixaEtaria).collect(Collectors.toList());
    }

    @JsonIgnore
    public BigDecimal getValorNetoTarifas() {
        return streamValorTarifa()
                        .map(vt -> Optional.ofNullable(vt.getTarifa()))
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .map(v -> Optional.ofNullable(v.getValorNeto()))
                        .map(o -> o.orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @JsonIgnore
    public BigDecimal getValorDescontoTarifas() {
        return streamValorTarifa()
                        .map(vt -> Optional.ofNullable(vt.getTarifa()))
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .map(v -> Optional.ofNullable(v.getValorDesconto()))
                        .map(o -> o.orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @JsonIgnore
    public BigDecimal getValorFinalTarifas() {
        return streamValorTarifa()
                        .map(vt -> Optional.ofNullable(vt.getTarifa()))
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .map(v -> Optional.ofNullable(v.getValorFinal()))
                        .map(o -> o.orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private Stream<Tarifacao> streamTarifacoes() {
        if (CollectionUtils.isEmpty(this.tarifacoes)) {
            return Stream.empty();
        }
        return this.tarifacoes.stream();
    }

    private Stream<ValorTarifa> streamValorTarifa() {
        return streamTarifacoes()
                        .map(t -> Optional.ofNullable(t.getValorTarifa()))
                        .filter(Optional::isPresent)
                        .map(Optional::get);
    }

    @JsonIgnore
    public BigDecimal getValorTaxaEmbarque() {
        return streamValorTarifa()
                        .map(vt -> vt.getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.TAXAEMBARQUE))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @JsonIgnore
    public BigDecimal getValorDU() {
        return streamValorTarifa()
                        .map(vt -> vt.getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.DU))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @JsonIgnore
    public BigDecimal getValorSobretaxas() {
        return streamValorTarifa()
                        .map(vt -> vt.getTotalSobretaxas())
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @JsonIgnore
    public boolean containsMarkup() {
	return streamValorTarifa()
			.anyMatch(vt -> vt.getTaxaSobretaxaDetalheTarifaIndividual(TipoValor.MARKUP).compareTo(BigDecimal.ZERO) > 0);
    }

    @JsonIgnore
    public List<Voo> getVoos() {
        return this.streamVoos().collect(Collectors.toList());
    }


    private Stream<Voo> streamVoos() {
        if (CollectionUtils.isEmpty(this.segmentos)) {
            return Stream.empty();
        }

        return this.segmentos.stream().flatMap(s -> s.getVoos().stream());
    }

    @JsonIgnore
    public Set<Aeroporto> getAeroportos() {
        Set<String>
                        aeroportos =
                        this.streamVoos().flatMap(v -> Stream.of(v.getAeroportoOrigem(), v.getAeroportoDestino()))
                                        .collect(Collectors.toSet());

        return aeroportos.stream().map(Aeroporto::new).collect(Collectors.toSet());
    }

    @JsonIgnore
    public Set<CiaAerea> getCiasAereas() {
        Set<String>
                        ciasAereas =
                        this.streamVoos()
                                        .flatMap(v -> Stream.of(v.getCiaAereaCodeShare(), v.getCiaAereaMarketing()))
                                        .collect(Collectors.toSet());

        return ciasAereas.stream().map(CiaAerea::new).collect(Collectors.toSet());
    }

    @JsonIgnore
    public int getQuantidadePax(FaixaEtaria faixaEtaria) {
        return this.streamTarifacoes()
                        .filter(t -> t.getFaixaEtaria() == faixaEtaria)
                        .mapToInt(Tarifacao::getQuantidade)
                        .sum();
    }

    @JsonIgnore
    public int getQuantidadeAdultos() {
        return getQuantidadePax(FaixaEtaria.ADT);
    }

    @JsonIgnore
    public int getQuantidadeCriancas() {
        return getQuantidadePax(FaixaEtaria.CHD);
    }

    @JsonIgnore
    public int getQuantidadeIdosos() {
        return getQuantidadePax(FaixaEtaria.SNR);
    }

    @JsonIgnore
    public int getQuantidadeBebes() {
        return getQuantidadePax(FaixaEtaria.INF);
    }

    public boolean getReservaGrupo() {
        return reservaGrupo;
    }

    public void setReservaGrupo(boolean reservaGrupo) {
        this.reservaGrupo = reservaGrupo;
    }

    public void setIdConfigRestrita(Integer idConfigRestrita) {
	this.idConfigRestrita = idConfigRestrita;
    }

    public void setCondicaoPrecificacao(String condicaoPrecificacao) {
	this.condicaoPrecificacao = condicaoPrecificacao;
    }

    public void setConfiguracaoRavId(Integer configuracaoRavId) {
	this.configuracaoRavId = configuracaoRavId;
    }

    public void setConfiguracaoFeeId(Integer configuracaoFeeId) {
	this.configuracaoFeeId = configuracaoFeeId;
    }

    private String toIndentedString(Object o) {
        return o == null ? "null" : o.toString().replace("\n", "\n    ");
    }

    @Override public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class Precificavel {\n");
        sb.append("     idAgrupamento: ").append(this.toIndentedString(this.idAgrupamento)).append("\n");
        sb.append("     , idUnidadePrecificavel: ").append(this.toIndentedString(this.idUnidadePrecificavel)).append("\n");
        sb.append("     , credencialId: ").append(this.toIndentedString(this.credencialId)).append("\n");
        sb.append("     , pagamentoCartao: ").append(this.toIndentedString(this.pagamentoCartao)).append("\n");
        sb.append("     , segmentos: ").append(this.toIndentedString(this.segmentos)).append("\n");
        sb.append("     , tipoTarifaAcordo: ").append(this.toIndentedString(this.tipoTarifaAcordo)).append("\n");
        sb.append("     , codigoContrato: ").append(this.toIndentedString(this.codigoContrato)).append("\n");
        sb.append("     , produtoPrecificacao: ").append(this.toIndentedString(this.produtoPrecificacao)).append("\n");
        sb.append("     , tarifacoes: ").append(this.toIndentedString(this.tarifacoes)).append("\n");
        sb.append("     , ciaAereaViagem: ").append(this.toIndentedString(this.ciaAereaViagem)).append("\n");
        sb.append("     , officeIdReserva: ").append(this.toIndentedString(this.officeIdReserva)).append("\n");
        sb.append("     , isSurface: ").append(this.toIndentedString(this.isSurface)).append("\n");
        sb.append("     , precificacaoIds: ").append(this.toIndentedString(this.precificacaoIds)).append("\n");
        sb.append("     , idConfigSucessor: ").append(this.toIndentedString(this.idConfigSucessor)).append("\n");
        sb.append("     , isNacional: ").append(this.toIndentedString(this.isNacional)).append("\n");
        sb.append("     , sistEmis: ").append(this.toIndentedString(this.sistEmis)).append("\n");
        sb.append("     , dataCriacao: ").append(this.toIndentedString(this.dataCriacao)).append("\n");
        sb.append("     , tipoPrecificavel: ").append(this.toIndentedString(this.tipoPrecificavel)).append("\n");
        sb.append("     , reservaGrupo: ").append(this.toIndentedString(this.reservaGrupo)).append("\n");
        sb.append("     , numeroTrechos: ").append(this.toIndentedString(this.numeroTrechos)).append("\n");
        sb.append("     , depuracaoHash: ").append(this.toIndentedString(this.depuracaoHash)).append("\n");
        sb.append("     , excludedByFilter: ").append(this.toIndentedString(this.excludedByFilter)).append("\n");
	sb.append("     , invoicedEntryAmount: ").append(this.toIndentedString(this.invoicedEntryAmount)).append("\n");
	sb.append("     , importedBooking: ").append(this.toIndentedString(this.importedBooking)).append("\n");
	sb.append("     , idConfigRestrita: ").append(this.toIndentedString(this.idConfigRestrita)).append("\n");
	sb.append("     , condicaoPrecificacao: ").append(this.toIndentedString(this.condicaoPrecificacao)).append("\n");
	sb.append("     , configuracaoFeeId: ").append(this.toIndentedString(this.configuracaoFeeId)).append("\n");
	sb.append("     , configuracaoRavId: ").append(this.toIndentedString(this.configuracaoRavId)).append("\n");
	sb.append("	, formasPagamento: ").append(this.toIndentedString(this.formasPagamento)).append("\n");
        sb.append("}");
        return sb.toString();
    }
}

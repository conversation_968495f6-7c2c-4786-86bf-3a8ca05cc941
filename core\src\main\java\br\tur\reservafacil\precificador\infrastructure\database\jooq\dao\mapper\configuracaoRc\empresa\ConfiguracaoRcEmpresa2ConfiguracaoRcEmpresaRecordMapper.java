package br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.configuracaoRc.empresa;

import br.com.reservafacil.precificador.infrastructure.database.jooq.generated.tables.records.ConfiguracaoRcEmpresaRecord;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoRcEmpresa;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.mapper.CustomMapper;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON><PERSON> on 20/08/25 - GA-12251.
 */
@Component
public class ConfiguracaoRcEmpresa2ConfiguracaoRcEmpresaRecordMapper
		implements CustomMapper<ConfiguracaoRcEmpresa, ConfiguracaoRcEmpresaRecord> {

    @Override
    public ConfiguracaoRcEmpresaRecord map(ConfiguracaoRcEmpresa input, ConfiguracaoRcEmpresaRecord output) {
	if (input.getId() != null) {
	    output.setId(input.getId());
	}
	output.setEmpresaId(input.getEmpresa() != null ? input.getEmpresa().getId() : null);
	output.setConfiguracaoRcId(input.getConfiguracaoRc() != null ? input.getConfiguracaoRc().getId() : null);
	return output;
    }
}

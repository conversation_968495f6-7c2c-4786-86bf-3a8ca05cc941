package br.tur.reservafacil.precificador.application.service;

import br.tur.reservafacil.dominio.Empresa;
import br.tur.reservafacil.dominio.Filial;
import br.tur.reservafacil.dominio.aereo.AcordoComercial;
import br.tur.reservafacil.dominio.aereo.ConfiguracaoBusca;
import br.tur.reservafacil.dominio.aereo.Restricao;
import br.tur.reservafacil.dominio.aereo.RestricaoAgrupadora;
import br.tur.reservafacil.dominio.tipo.SistEmis;
import br.tur.reservafacil.dominio.tipo.TipoOperador;
import br.tur.reservafacil.dominio.tipo.TipoRestricao;
import br.tur.reservafacil.precificador.application.service.cache.ConfiguracaoBuscaRedisCacheComponent;
import br.tur.reservafacil.precificador.domain.component.restricao.RestricaoValidatorComponent;
import br.tur.reservafacil.precificador.domain.repository.ConfiguracaoBuscaRepository;
import br.tur.reservafacil.precificador.domain.repository.EmpresaRepository;
import br.tur.reservafacil.precificador.domain.repository.FilialRepository;
import br.tur.reservafacil.precificador.infrastructure.database.jooq.dao.UsuarioJooqDao;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by davidson on 6/3/16.
 */
@Service
public class ConfiguracaoBuscaService
		extends AbstractService<ConfiguracaoBusca, Integer, ConfiguracaoBuscaRepository> {


    @Autowired
    private ConfiguracaoBuscaRedisCacheComponent configuracaoBuscaRedisCacheComponent;

    @Autowired
    public UsuarioJooqDao usuarioRepository;

    @Autowired
    private EmpresaRepository empresaRepository;

    @Autowired
    private FilialRepository filialRepository;

    @Autowired
    private RestricaoValidatorComponent restricaoValidatorComponent;

    @Autowired
    private BeanFactory factory;

    private ConfiguracaoBuscaService proxy;


    public List<ConfiguracaoBusca> findByKeys(Set<Integer> ids) {
	return this.getRepository().findByKeys(ids);
    }

    public ConfiguracaoBusca findByPkEager(Integer idConfBusca) {
	final ConfiguracaoBusca configuracaoBusca = this.getRepository().findByPkEager(idConfBusca);
	configuracaoBusca.agrupaRestricoes();
	return configuracaoBusca;
    }

    public List<RestricaoAgrupadora> findRestricoesByConfiguracaoBusca(Integer idConfBusca) {
	return this.findByPkEager(idConfBusca).geraListaDeRestricaoAgrupadora();
    }

    private ConfiguracaoBuscaService getProxy() {
        if (this.proxy == null) {
            this.proxy = this.factory.getBean(ConfiguracaoBuscaService.class);
        }
        return this.proxy;
    }

    public List<ConfiguracaoBusca> findConfiguracaoSoComRestricaoByEmpresaSisteEmisProduto(String filial, Empresa empresa,
                                                                                           List<SistEmis> sistEmis, String produto, String unidadeOperacional,
                                                                                           Boolean isBuscaDeTeste) {
        return getProxy().configuracoesSoComRestricaoByEmpresaSisteEmisProduto(filial, empresa, sistEmis, produto, unidadeOperacional, isBuscaDeTeste);
    }

    @Cacheable(cacheNames = "ConfiguracaoBuscaService::configuracoesSoComRestricaoByEmpresaSisteEmisProduto", unless = "#result == null", cacheManager = "ehCacheManager")
    public List<ConfiguracaoBusca> configuracoesSoComRestricaoByEmpresaSisteEmisProduto(String filial, Empresa empresa,
											List<SistEmis> sistEmis, String produto, String unidadeOperacional,
											Boolean isBuscaDeTeste) {


        final LocalTime time = LocalTime.now();
        int second = time.getSecond();

        String timeToCache = isBuscaDeTeste ? ", time=" + DateTimeFormatter.ofPattern("HH:mm:").format(time) + second / 10 : "";

        return configuracaoBuscaRedisCacheComponent.configuracoesSoComRestricaoByEmpresaSisteEmisProduto(filial, empresa, sistEmis, produto, unidadeOperacional, isBuscaDeTeste, timeToCache);
    }

    @Cacheable(cacheNames = "ConfiguracaoBuscaService::findConfiguracaoBuscaPorFilialEEmpresaESistEmisProduto", cacheManager = "ehCacheManager", sync = true)
    public List<ConfiguracaoBusca> findConfiguracaoBuscaPorFilialEEmpresaESistEmisProduto(String filial, String agencia, String grupo, String empresa, SistEmis sistEmis, String produto) {
	return configuracaoBuscaRedisCacheComponent.findConfiguracaoBuscaPorFilialEEmpresaESistEmisProduto(filial, agencia, grupo, empresa, sistEmis, produto);
    }

    public List<ConfiguracaoBusca> findConfiguracaoBuscaPorFilialEEmpresaCiaProduto(String filial, String agencia, String grupo, String empresa, String cia, String produto) {
	return this.getRepository().findConfiguracaoBuscaPorFilialEEmpresaCiaProduto(filial, agencia, grupo, empresa, cia, produto);
    }

    public List<ConfiguracaoBusca> findByParameters(int page, Integer pageSize, String orderBy, Map<String, String> params) {
	List<ConfiguracaoBusca> resultados = this.getRepository().findByParameters(page, pageSize, orderBy, params);
	resultados.forEach(ConfiguracaoBusca::agrupaRestricoes);
	return resultados;
    }

    public Integer countByParameters(Map<String, String> params) {
	return this.getRepository().countByParameters(params);
    }

    @Cacheable(cacheNames = "ConfiguracaoBuscaService::isBestPriceEnabled", cacheManager = "ehCacheManager", sync = true)
    public boolean isBestPriceEnabled(Integer id) {
	return configuracaoBuscaRedisCacheComponent.isBestPriceEnabled(id);
    }

    @Cacheable(cacheNames = "ConfiguracaoBuscaService::findAcordosByConfiguracaoId", cacheManager = "ehCacheManager", sync = true)
    public List<AcordoComercial> findAcordosByConfiguracaoId(Integer idConfiguracao, List<String> codigoCias, List<SistEmis> sistemasEmissores) {
	return configuracaoBuscaRedisCacheComponent.findAcordosByConfiguracaoId(idConfiguracao, codigoCias, sistemasEmissores);
    }

    public List<Integer> findIdsDeConfiguracoesDisponiveis(String filialRef, String empresaRef) {
	final Empresa empresa = empresaRepository.buscaEmpresaPorReferencia(empresaRef);

	if (empresa == null) {
	    return Collections.emptyList();
	}

	final Filial filial = filialRepository.buscarPorReferenciaEEmpresa(filialRef, empresa.getId());

	if (filial == null) {
	    return Collections.emptyList();
	}

	List<ConfiguracaoBusca> configuracoesBusca = this.getRepository().findConfiguracaoSoComRestricaoByEmpresa(empresa);

	if (CollectionUtils.isEmpty(configuracoesBusca)) {
	    return Collections.emptyList();
	}

	final String strFilialId = filial.getId().toString();
	List<Restricao> restricaoFilial = Arrays.asList(new Restricao(strFilialId, TipoOperador.IGUAL, TipoRestricao.FILIAL));

	final List<Restricao> restricoes = configuracoesBusca.stream().flatMap(c -> c.getRestricoes().stream())
			.filter(r -> r.getTipoRestricao() == TipoRestricao.FILIAL)
			.collect(Collectors.toList());

	List<ConfiguracaoBusca> configuracoesBuscasValidas = new ArrayList<>();

	if (CollectionUtils.isEmpty(restricoes)) {
	    configuracoesBuscasValidas = configuracoesBusca;
	} else {
	    configuracoesBusca.stream()
			    .filter(c -> restricaoValidatorComponent.validaRestricoes(restricaoFilial, c.getRestricoes()))
			    .forEach(configuracoesBuscasValidas::add);
	}

	return configuracoesBuscasValidas.stream().map(ConfiguracaoBusca::getId).collect(Collectors.toList());
    }
}
